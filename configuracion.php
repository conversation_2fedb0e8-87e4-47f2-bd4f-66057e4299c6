<?php

include_once('util_db.php');
include_once('util_menu_rework.php');
include_once('util_session.php');
include_once('util_enhanced_security.php');
manejar_sesion();
$usuario_id = $_SESSION['usuario_id'];
$codigo_usuario = $_SESSION['usuario_codigo'];

$active = _get('active');

rastrear('configuracion.php', '', $usuario_id);

$r_dias = mysql_query("SELECT fecha FROM usuarios_dias_laborables WHERE id_usuario = $usuario_id");

while ($row = mysql_fetch_assoc($r_dias)) {
    $dias[] = $row;
}
$parametro = get_usuario_has_parametro($usuario_id, 'CONFIGURACION_DISPOSITIVOS');
$qmf = get_parametro_usuario($usuario_id, 'QUADMINDS_FLASH');
$useNewDriverApp = get_parametro_usuario($usuario_id, 'USE_NEW_DRIVER_APP');
$tnt = get_parametro_usuario($usuario_id, 'TRACK_AND_TRACE');
$existsInAuthSystem = get_parametro_usuario($usuario_id, 'EXISTS_IN_AUTH_SYSTEM');
$hasOrderTemplates = get_parametro_usuario($usuario_id, 'ENABLE_ORDERSTEMPLATE');
$isLoadCheckEnabled = get_parametro_usuario($usuario_id, 'IS_LOAD_CHECK_ENABLED');

//get user code
if (!strpos($codigo_usuario, '@') || $parametro == 1) {
    $showDispConfigTab = true;
} else {
    $showDispConfigTab = false;
}

$complexityText = get_parametro_usuario(getMainUser($usuario_id), 'PASSWD_COMPLEXITY_TEXT');
$passwordHistory = get_parametro_usuario(getMainUser($usuario_id), 'PASSWD_KEEP_HISTORY');
$historyText = sprintf(_("La contraseña no puede ser igual a las %d últimas utilizadas"), $passwordHistory);

//Merchants
$merchants_admin = get_parametro_usuario($usuario_id, 'ADMINISTRAR_VENDEDORES');

?>

<!DOCTYPE html>

<html>

<head>
    <title>QuadMinds</title>
    <link rel="icon" href="img/icon_quadminds.ico" type="image/x-icon" />
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <link href="https://cdn.jsdelivr.net/npm/material-components-web@11.0.0/dist/material-components-web.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/material-components-web@11.0.0/dist/material-components-web.min.js"></script>
    <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.1/themes/smoothness/jquery-ui.css" type="text/css" />
    <link rel="stylesheet" href="css/scroll.css" type="text/css" />
    <link rel="stylesheet" type="text/css" href="css/base3.css?v=4" />
    <link rel="stylesheet" type="text/css" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="css/datepicker.css" />
    <link rel="stylesheet" type="text/css" href="css/bootstrap-datepicker.css" />
    <link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css" />
    <link rel="stylesheet" type="text/css" href="/chameleon/css/spin.css" />

    <link href="css/bootstrap-select.min.css" rel="stylesheet">
    <link href="styles/configuracion.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="css/bootstrap-switch.min.css" />
    <script src="https://cdn.jsdelivr.net/npm/uuid@latest/dist/umd/uuidv4.min.js"></script>
    <script type="text/javascript" src="js/functions.js"></script>
    <script type="text/javascript" src="js/jquery.min.js"></script>
    <script type="text/javascript" src="js/bootstrap-datepicker.js"></script>
    <script type="text/javascript" src="js/bootstrap-datepicker.<?= getLanguage() ?>.js"></script>
    <script type="text/javascript" src="js/bootstrap-select.min.js"></script>
    <script type="text/javascript" src="js/bootstrap-select-extended.js"></script>
    <script type="text/javascript" src="js/jquery.li-scroller.1.0.js"></script>
    <script type="text/javascript" language="javascript" src="js/bootstrap/bootstrap.min.js"></script>
    <script type="text/javascript" src="https://www.google.com/jsapi"></script>
    <script type="text/javascript" src="js/d3.v3.min.js"></script>
    <script type="text/javascript" src="js/ajaxtabs.js" language="javascript"></script>
    <script type="text/javascript" src="js/bootstrap-switch.min.js" language="javascript"></script>
    <script type="text/javascript" src="/chameleon/js/spin.umd.js"></script>
    <script type="text/javascript" src="/webhooks_configuracion/js/webhooks_details.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/libphonenumber-js@1.9.15/bundle/libphonenumber-min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.5/build/js/intlTelInput.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.5/build/css/intlTelInput.css">

    <script src="scripts/functions.js"></script>

    <? include_once('analytics.php') ?>
</head>

<body class='home'>
    <?php
    $hideMenu = 1;
    $header_type = 'wide';
    $noBootstrap = true;
    $loadBootstrap = false;
    include_once('header.php');
    /*write_header_bootstrap();
write_menu_bootstrap("Configuracion");*/
    //write_header();
    //write_menu(_("Configuracion"));

    write_end_div(2);
    ?>

    <div class="container" style="box-shadow: none;">

        <div class="row title">
            <?php echo "<div>
                            <span class='glyphicon glyphicon-cog'></span>
                            <span>" . _('Configuración de') . " " . $_SESSION['usuario_nombre'] . "
                            </span>
                        </div>"; ?>
            <!--        <hr style="border-color: rgba(253, 252, 252, 0.68)">-->
        </div>
        <table border="0" width="100%" class="row well" style="border: 0; background-color: rgba(255,255,255,0)">

            <tr>
                <td valign="top">
                    <div style="display: flex;">
                        <!-- Nav tabs -->
                        <ul id="tabNav" class="nav nav-tabs block buttonBlock" role="tablist">
                            <li role="presentation" class="active buttonDesign" value="pass" id="firstTab">
                                <a href="#password" data-url="/configuracion_tab_seguridad.php" aria-controls="password" role="tab" data-toggle="tab">
                                    <span class="btnTitle"><?= _('Cuenta'); ?></span>
                                </a>
                            </li>
                            <? if ($existsInAuthSystem && $merchants_admin) : ?>
                                <li role="presentation" class="buttonDesign" id="merchantsTab">
                                    <a href="#merchants-admin" data-url="/merchants_settings/merchants_settings.php" aria-controls="merchants-admin" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Vendedores'); ?></span>
                                        <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7; margin-left: 4px;">NUEVO</span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <? if ($existsInAuthSystem && $hasOrderTemplates) : ?>
                                <li role="presentation" class="buttonDesign" id="integrationsSettings">
                                    <a id="integrationsSettingsTab" href="#integrationsSettingsConfig" data-url="/integrations_settings/integrations_settings.php" aria-controls="integrations" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Integraciones'); ?></span>
                                        <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7; margin-left: 4px;">NUEVO</span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <li role="presentation" class="buttonDesign">
                                <a href="#lenguage" data-url="/configuracion_tab_timezone.php" aria-controls="lenguage" role="tab" data-toggle="tab">
                                    <span class="btnTitle"><?= _('Parámetros regionales'); ?></span>
                                </a>
                            </li>
                            <? if (!$qmf) : ?>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#emails" data-url="/configuracion_tab_emails.php" aria-controls="emails" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Emails'); ?></span>
                                    </a>
                                </li>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#holidays" data-url="/configuracion_tab_calendario.php" aria-controls="holidays" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Días no laborables'); ?></span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <li role="presentation" id="landing-tab-link" class="buttonDesign">
                                <a href="#landing" data-url="/configuracion_tab_landing_pages.php" aria-controls="landings" role="tab" data-toggle="tab">
                                    <span class="btnTitle"><?= _('Página de inicio'); ?></span>
                                </a>
                            </li>
                            <li role="presentation" class="buttonDesign" id="tntAdvancedTab">
                                <a id="tntAdvancedConfigTab" href="#tntAdvancedConfig" data-url="/tnt_advanced_configuration/tnt_advanced_settings.php" aria-controls="tntAdvancedConfig" role="tab" data-toggle="tab">
                                    <span class="btnTitle"><?= _('Modificar entrega'); ?></span>
                                    <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7; margin-left: 4px;"><?= _("NUEVO"); ?></span>
                                </a>
                            </li>
                            <? if ($qmf || $tnt) : ?>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#tnt" data-url="/configuracion_tab_tnt.php" aria-controls="tnt" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Notificaciones al cliente'); ?></span>
                                    </a>
                                </li>
                                <li role="presentation" class="buttonDesign">
                                    <a id="smsConfigTab" href="#configuracionSMS" data-url="/smsConfiguracion/configuracionSMS.php" aria-controls="smsConfig" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Configuracion SMS'); ?></span>
                                    </a>
                                </li>
                                <li role="presentation" class="buttonDesign" id="webhookTab">
                                    <a id="webHookConfigTab" href="#webHookConfig" data-url="/webhooks_configuracion/webhook_configuration_tab.php" aria-controls="webHooks" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Webhooks'); ?></span>
                                    </a>
                                </li>
                                <li role="presentation" class="buttonDesign" id="epodTab">
                                    <a id="epodConfigTab" href="#epodConfig" data-url="/epod_configuration/epod_configuration.php" aria-controls="epod" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Epod'); ?></span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <? if ($showDispConfigTab) : ?>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#devices" data-url="/configuracion_tab_dispositivos.php" aria-controls="devices" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Dispositivos'); ?></span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <li role="presentation" class="buttonDesign">
                                <a href="#api-key" data-url="/configuracion_tab_api.php" aria-controls="api-key" role="tab" data-toggle="tab">
                                    <span class="btnTitle"><?= _('API'); ?></span>
                                </a>
                            </li>
                            <? if ($qmf && !$useNewDriverApp) : ?>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#mobile-app" data-url="/configuracion_tab_mobile_app.php" aria-controls="mobile-app" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('App móvil'); ?></span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <? if ($useNewDriverApp) : ?>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#driver-app" data-url="/driverAppConfiguracion/configuracion_tab_driver_app.php" aria-controls="driver-app" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Driver App'); ?></span>
                                    </a>
                                </li>
                            <? endif; ?>
                            <? if ($useNewDriverApp && $isLoadCheckEnabled) : ?>
                                <li role="presentation" class="buttonDesign">
                                    <a href="#driver-app-load-check" data-url="/driverAppConfiguracion/configuracion_tab_load_check.php" aria-controls="driver-app" role="tab" data-toggle="tab">
                                        <span class="btnTitle"><?= _('Control de carga'); ?></span>
                                    </a>
                                </li>
                            <? endif; ?>
                        </ul>


                        <!-- Tab panes -->
                        <div class="tab-content block blockCont">
                            <div role="tabpanel" class="tab-pane active" id="password"></div>
                            <div role="tabpanel" class="tab-pane" id="merchants-admin"></div>
                            <div role="tabpanel" class="tab-pane" id="lenguage"></div>
                            <div role="tabpanel" class="tab-pane" id="emails"></div>
                            <div role="tabpanel" class="tab-pane" id="holidays"></div>
                            <div role="tabpanel" class="tab-pane" id="landing"></div>
                            <div role="tabpanel" class="tab-pane" id="devices"></div>
                            <div role="tabpanel" class="tab-pane" id="tnt"></div>
                            <div role="tabpanel" class="tab-pane" id="tntAdvancedConfig"></div>
                            <div role="tabpanel" class="tab-pane" id="integrationsSettingsConfig"></div>
                            <div role="tabpanel" class="tab-pane" id="configuracionSMS"></div>
                            <div role="tabpanel" class="tab-pane" id="webHookConfig"></div>
                            <div role="tabpanel" class="tab-pane" id="epodConfig"></div>
                            <div role="tabpanel" class="tab-pane" id="api-key"></div>
                            <div role="tabpanel" class="tab-pane" id="mobile-app"></div>
                            <div role="tabpanel" class="tab-pane" id="driver-app"></div>
                            <div role="tabpanel" class="tab-pane" id="driver-app-load-check"></div>
                        </div>
                    </div>

                    <!-- Modal eliminar emails-->

                    <div id="confirmar_eliminar_email" class="modal fade">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                    <h4 class="modal-title"><?= _('Eliminar dirección de email'); ?></h4>
                                </div>
                                <div class="modal-body">
                                    <p><?= _('¿Está seguro que desea eliminar esta dirección de email?'); ?></p>
                                </div>
                                <div class="modal-footer">
                                    <button id="cancelarModal" type="button" class="btn btn-default" data-dismiss="modal"><?= _("Cancelar") ?></button>
                                    <button id="deleteEmail" type="button" class="btn btn-danger"><?= _("Eliminar") ?> </button>
                                </div>
                            </div><!-- /.modal-content -->
                        </div><!-- /.modal-dialog -->
                    </div><!-- /.modal -->

                    <!--                <div id="tabs" class="block buttonBlock">-->
                    <!---->
                    <!--                    <button onclick="setTimeout(function(){creado = false},30)" type='button' class='btn btn-default buttonDesign'>-->
                    <!--                        <a href='configuracion_tab_seguridad.php' rel='divcontainer' class='selected' style="font-size: 15px"></a>-->
                    <!--                    </button>-->
                    <!--                    <button onclick="setTimeout(function(){creado = false},30)" type='button' class='btn btn-default buttonDesign'>-->
                    <!--                        <a href='configuracion_tab_timezone.php' rel='divcontainer'></a>-->
                    <!--                    </button>-->
                    <!--                    <button onclick="setTimeout(function(){creado = false},30)" type='button' class='btn btn-default buttonDesign'>-->
                    <!--                        <a href='configuracion_tab_emails.php' rel='divcontainer'></a>-->
                    <!--                    </button>-->
                    <!--                    <button onclick = "setTimeout(function(){creado = false;createCalendario()},500)" type='button' class='btn btn-default buttonDesign'>-->
                    <!--                        <a href='configuracion_tab_calendario.php'  rel='divcontainer'></a>-->
                    <!--                    </button>-->
                    <!---->
                    <!--                </div>-->
                    <!--                <br style="clear: left" />-->

                    <!--                <div id="divcontainer" class="block blockCont"></div>-->


                    <script type="text/javascript">
                        const active = '<?php echo $active; ?>';
                        var dias = <?= json_encode($dias); ?>;
                        var creado = false;

                        $(document).ready(function() {
                            //                        var mytabs= new Ddajaxtabs("tabs", "divcontainer");
                            //                        mytabs.setpersist(false);
                            //                        mytabs.setselectedClassTarget("link"); //"link" or "linkparent"
                            //                        mytabs.init();

                            //load content for first tab and initialize

                            // history.pushState(null, "", "cuenta.php");

                            // TODO: Pls standarize tabs and move this to a switch case
                            if (active == 'webhooks') {
                                $("#firstTab").removeClass("active");
                                $("#password").removeClass("active");
                                $("#webhookTab").addClass("active");
                                $("#webHookConfig").addClass("active");
                                $('#webHookConfig').load('/webhooks_configuracion/webhook_configuration_tab.php', function() {
                                    $('#tabNav').tab(); //initialize tabs
                                });
                            } else if (active == 'merchants') {
                                $("#firstTab").removeClass("active");
                                $("#password").removeClass("active");
                                $("#merchantsTab").addClass("active");
                                $("#merchants-admin").addClass("active");
                                $('#merchants-admin').load('/merchants_settings/merchants_settings.php', function() {
                                    $('#tabNav').tab(); //initialize tabs
                                });
                            }


                            $('#password').load('/configuracion_tab_seguridad.php', function() {
                                $('#tabNav').tab(); //initialize tabs
                                $("#imageFileClick, #imageFileClickPencil").click(() => {
                                    $("#imageFileName").click();
                                });
                                $("#imageFileName").on('change', () => {
                                    uploadImagenLogo();
                                });
                            });
                            $('.buttonDesign').on('show.bs.tab', function(e) {
                                var pattern = /#.+/gi //use regex to get anchor(==selector)
                                var contentID = e.target.toString().match(pattern)[0]; //get anchor
                                var contentUrl = $(e.target).data("url");
                                //load content for selected tab
                                $(contentID).load(contentUrl, function() {
                                    $('#tabNav').tab(); //reinitialize tabs
                                    createCalendario();
                                    if (contentID == '#devices') {
                                        //init datepicker
                                        $('.selectpicker').selectpicker({
                                            width: '300px',
                                            dropupAuto: 'false',
                                            header: ' '
                                        }).selectpickerCheck({
                                            strNinguno: '<?= _("Ninguno") ?>',
                                            strTodos: '<?= _("Todos") ?>'
                                        });
                                        $('.selectusers').selectpicker({
                                            width: '300px',
                                            dropupAuto: 'false'
                                        });
                                    }
                                    if (contentID === '#mobile-app') {
                                        // Add new height to this tab in particular
                                        $(".tab-pane.active").css("height", 950);
                                    }
                                    if (contentID == '#tnt') {
                                        $("[data-toggle='switch']").bootstrapSwitch({
                                            onText: '<?= _("Si") ?>',
                                            offText: '<?= _("No") ?>'
                                        });
                                        $('[data-toggle="tooltip"]').tooltip();
                                        inRouteStateChanged();
                                        receivedStateChanged();
                                        receivedMerchantStateChanged();
                                    }
                                });
                            });

                            $('#deleteEmail').click(function() {
                                var id = $("#confirmar_eliminar_email").data("id");
                                borrarEmailConfirmado(id);
                                $('#confirmar_eliminar_email').modal('hide');
                            });

                            $('#cancelarModal').click(function() {
                                $('#confirmar_eliminar_email').modal('hide');
                            });

                            if ($('#apiKey').val() !== '') {
                                $('#viewKey').show();
                                $('#noKey').hide();
                            } else {
                                $('#noKey').show();
                                $('#viewKey').hide();
                            }
                        });

                        function generateKey(length, operacion) {

                            var chars = "abcdefghijklmnopqrstuvwxyz" +
                                "ABCDEFGHIJKLMNOPQRSTUVWXYZ" +
                                "0123456789";
                            //+ "`~!@#$%^&*()-_=+[{]}\\|;:'\",<.>/?";
                            var array = new Array(length);
                            try {
                                // crypto.getRandomValues esta disponible en
                                // chrome 11, firefox 21, opera 15 e IE 11
                                var intArray = new Uint32Array(length);
                                window.crypto.getRandomValues(intArray);
                                for (var a in intArray) {
                                    array[a] = chars[intArray[a] % chars.length];
                                }
                            } catch (e) {
                                // Math.random no es seguro para crypto...
                                var a = 0;
                                while (a < length) {
                                    array[a++] = chars[Math.floor(Math.random() * chars.length)];
                                }
                            }

                            var key = array.join('');

                            $.ajax({
                                url: "configuracion_tab_api.php",
                                type: "POST",
                                data: {
                                    operacion: operacion,
                                    key: key
                                },
                                success: function(msg) {
                                    if (msg == 0) {
                                        $('#err').show();
                                    } else {
                                        $('#apiKey').val(key);
                                        $('#noKey').hide();
                                        $('#viewKey').show();
                                    }
                                }
                            });

                        }

                        function createCalendario() {
                            if (creado == false) {
                                $('#calendario').datepicker({
                                    format: 'yyyy-mm-dd',
                                    multidate: true
                                });

                                if (dias) {
                                    var dates = [];
                                    for (var i = 0; i < dias.length; i++) {
                                        dates.push(dias[i]['fecha']);
                                    }
                                    $('#calendario').datepicker('setDates', dates);
                                    creado = true;
                                }
                            }

                        }

                        function deleteDias() {
                            $.ajax({
                                url: 'configuracion_tab_calendario.php',
                                type: "POST",
                                data: {
                                    borrar: true
                                },
                                success: function(msg) {
                                    ga('send', 'pageview', '/configuracion_tab_calendario.php');
                                    $('#alertDiv').html("<?= _('Borrado con éxito'); ?>")
                                    document.getElementById('form_response').innerHTML = '<span style="color:darkseagreen;"><?= _('Días borrados correctamente'); ?></span>';
                                    location.reload();
                                }
                            });
                        }

                        function saveDias() {
                            var dates = $('#calendario').datepicker('getUTCDates');
                            $.ajax({
                                url: 'configuracion_tab_calendario.php',
                                type: "POST",
                                data: {
                                    dates: JSON.stringify(dates)
                                },
                                success: function(msg) {
                                    ga('send', 'pageview', '/configuracion_tab_calendario.php');
                                    document.getElementById('form_response').innerHTML = '<span style="color:darkseagreen;"><?= _('Configuración guardada correctamente'); ?></span>';
                                    location.reload();
                                }
                            });
                        }

                        function assignDevicesToUsers() {
                            var users;
                            if ($("#selectUsuarios").selectpicker('val')) {
                                users = $("#selectUsuarios").selectpicker('val');
                            } else {
                                users = [];
                            }

                            $("#botonAsignar").attr("disabled", "disabled");
                            $("#botonAsignarTodos").attr("disabled", "disabled");
                            $.ajax({
                                url: 'configuracion_tab_dispositivos.php?operacion=setAllDispositivos',
                                type: "POST",
                                data: {
                                    users: JSON.stringify(users)
                                },
                                success: function(msg) {
                                    ga('send', 'pageview', '/configuracion_tab_dispositivos.php');
                                    mostrarAlertResultado(msg == '');
                                    cargarSubusuarios();
                                }
                            });
                        }

                        function assignSomeDevicesToUsers() {
                            var select = $("#selectDispositivos");
                            var i;
                            var values = [];
                            for (i = 0; i < select[0].length; i++) {
                                if (select[0][i].selected) {
                                    values.push(select[0][i].value);
                                }
                            }
                            if ($("#selectUsuarioUnico").selectpicker('val')) {
                                $("#botonAsignar").attr("disabled", "disabled");
                                $("#botonAsignarTodos").attr("disabled", "disabled");
                                $.ajax({
                                    url: "configuracion_tab_dispositivos.php?operacion=setearPermisos",
                                    type: "POST",
                                    data: {
                                        subusuario_id: $("#selectUsuarioUnico")[0].value,
                                        dispositivos: values
                                    },
                                    complete: function(msg) {
                                        mostrarAlertResultado(msg.responseText == '1');
                                    }
                                });
                            }
                        }

                        function mostrarAlertResultado(msg) {
                            if (msg) {
                                $("#resultado").append("<div id='alertResult' class='alert alert-success' style='padding: 8px !important; width: 80%; margin-left: 10%; text-align: center;' role='alert'><?= _('¡Asignado correctamente!') ?></div>");
                                $('#alertResult').fadeOut(2100, function() {
                                    $(this).remove();
                                    $("#botonAsignar").removeAttr("disabled");
                                    $("#botonAsignarTodos").removeAttr("disabled");
                                });
                            } else {
                                $("#resultado").append("<div id='alertResult' class='alert alert-danger' style='padding: 8px !important; width: 80%; margin-left: 10%; text-align: center;' role='alert'><?= _('En este momento la asignación no esta disponible') ?> </div>");
                                $('#alertResult').fadeOut(2100, function() {
                                    $(this).remove();
                                    $("#botonAsignar").removeAttr("disabled");
                                    $("#botonAsignarTodos").removeAttr("disabled");
                                });
                            }
                        }
                        let razon_social;

                        function showControlsRazonSocial() {
                            $('#showControlsRazonSocial').hide();
                            $('#saveRazonSocial').show();
                            $('#cancelRazonSocial').show();

                            $('#razon_social').attr('disabled', false);
                            $('#razon_social').focus();
                            razon_social = $('#razon_social').val();
                            setRazonSocial(razon_social);
                        }

                        function cancelRazonSocial() {
                            $('#showControlsRazonSocial').show();
                            $('#saveRazonSocial').hide();
                            $('#cancelRazonSocial').hide();

                            $('#razon_social').attr('disabled', true);
                            $('#razon_social').val(razon_social);

                        }


                        function grabarClave() {
                            var pwd_actual = document.getElementById('pwd_actual').value;
                            var pwd_nueva = document.getElementById('pwd_nueva').value;
                            var pwd_nueva2 = document.getElementById('pwd_nueva2').value;
                            if (pwd_nueva == pwd_nueva2) {
                                $.ajax({
                                    url: "cambiar_password.php",
                                    type: "POST",
                                    data: {
                                        pwd_actual: pwd_actual,
                                        pwd_nueva: pwd_nueva
                                    },
                                    success: function(msg) {
                                        ga('send', 'pageview', '/cambiar_password.php');
                                        returnAJAX(msg)
                                    }
                                });
                            } else {
                                alert("<?= _('Las passwords ingresadas no coinciden.'); ?>");
                            }
                        }

                        function grabarTz() {
                            $.ajax({
                                url: "configuracion_tab_timezone.php",
                                type: "POST",
                                data: {
                                    nuevo_timezone: $("#timezone").val(),
                                    nuevo_locale: $("#locale").val(),
                                    nuevas_unidades: $("#unidades").val(),
                                    nuevo_pais: $("#pais").val()
                                },
                                success: function(msg) {
                                    ga('send', 'pageview', '/configuracion_tab_timezone.php');
                                    returnAJAX(msg);
                                    location.reload();
                                }
                            });
                        }

                        function grabarTNT() {
                            var received_state = $(".bootstrap-switch-id-received-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var in_route_state = $(".bootstrap-switch-id-in-route-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var eta = $("#eta").is(":checked") ? 0 : 1;
                            var eta_received = $("#eta_received").is(":checked") ? 0 : 1;
                            var delivered_state = $(".bootstrap-switch-id-delivered-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var is_close_state = $(".bootstrap-switch-id-is-close-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var not_fulfilled_partial_state = $(".bootstrap-switch-id-not-fulfilled-partial-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var not_fulfilled_state = $(".bootstrap-switch-id-not-fulfilled-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var email_origin = $('#email-origin').val();
                            var orgDisplayName = $('#orgDisplayName').val();
                            var contact_phone = iti.getNumber(intlTelInputUtils.numberFormat.INTERNATIONAL);
                            var contact_tnt = $(".bootstrap-switch-id-contact-tnt").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var margen_tnt = $('#margen_tnt').val();
                            var margen_tnt2 = $('#margen_tnt2').val();
                            var merchant_received_state = $(".bootstrap-switch-id-merchant-received-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var eta_merchant_received = $("#eta_merchant_received").is(":checked") ? 1 : 0;
                            var merchant_fulfilled_state = $(".bootstrap-switch-id-merchant-fulfilled-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var merchant_not_fulfilled_partial_state = $(".bootstrap-switch-id-merchant-not-fulfilled-partial-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var merchant_not_fulfilled_state = $(".bootstrap-switch-id-merchant-not-fulfilled-state").hasClass('bootstrap-switch-on') ? 1 : 0;

                            var sms_received_state = $(".bootstrap-switch-id-sms-received-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var sms_in_route_state = $(".bootstrap-switch-id-sms-in-route-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var sms_arriving_state = $(".bootstrap-switch-id-sms-arriving-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var sms_delivered_state = $(".bootstrap-switch-id-sms-delivered-state").hasClass('bootstrap-switch-on') ? 1 : 0;
                            var notification_operation_type = document.getElementById('notification-type').value;

                            var validatedResult = validateContactInputs(email_origin, contact_phone);
                            if (validatedResult) {
                                $.ajax({
                                    url: "configuracion_tab_tnt.php",
                                    type: "POST",
                                    data: {
                                        operation: 'save',
                                        received_state,
                                        in_route_state,
                                        eta,
                                        delivered_state,
                                        is_close_state,
                                        not_fulfilled_state,
                                        not_fulfilled_partial_state,
                                        eta_received,
                                        email_origin,
                                        orgDisplayName,
                                        contact_phone,
                                        contact_tnt,
                                        margen_tnt,
                                        margen_tnt2,
                                        merchant_received_state,
                                        eta_merchant_received,
                                        merchant_fulfilled_state,
                                        merchant_not_fulfilled_partial_state,
                                        merchant_not_fulfilled_state,
                                        sms_received_state,
                                        sms_in_route_state,
                                        sms_arriving_state,
                                        sms_delivered_state,
                                        notification_operation_type
                                    },
                                    success: function(msg) {
                                        if (msg == 1) {
                                            $("#tnt_response").html('<div class="alert alert-success"><?= _('Configuración guardada correctamente'); ?></div>');
                                        } else {
                                            $("#tnt_response").html('<div class="alert alert-danger"><?= _('Error al guardar la configuración'); ?></div>');

                                        }
                                    }
                                });
                            }
                        }


                        function returnAJAX(responseText) {
                            if (responseText == '1') {
                                document.getElementById('form_response').innerHTML = '<span style="color:darkseagreen;"><?= _('Configuración guardada correctamente'); ?></span>';
                            } else if (responseText == '2') {
                                document.getElementById('form_response').innerHTML = '<span style="color:red;"><?= $complexityText ?></span>';
                            } else if (responseText == '3') {
                                document.getElementById('form_response').innerHTML = '<span style="color:red;"><?= $historyText ?></span>';
                            } else {
                                document.getElementById('form_response').innerHTML = '<span style="color:red;"><?= _('Error al guardar la configuración'); ?></span>';
                            }

                        }

                        function validateEmail(email) {
                            //                            var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                            //                            return re.test(email);
                            var mailformat = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
                            return (email.match(mailformat));
                        }

                        function validateNotNullPhone(phone) {
                            return phone && phone !== '';
                        }

                        function validateContactInputs(email, phone) {
                            const validEmail = validateEmail(email)
                            const validPhone = validateNotNullPhone(phone);
                            if (validEmail /* && validPhone */ ) return true;

                            if (!validEmail) {
                                $("#error-email-config").html('<span id="noti_email_error" style="color:red;font-size: 12px;"><?= _('Ingrese una dirección email válida'); ?></span>');
                                setTimeout(function() {
                                    $("#noti_email_error").hide();
                                }, 3000);
                            }

                            if (!validPhone) {
                                $("#error-contact-phone-config").html('<span id="noti_phone_error" style="color:red;font-size: 12px;"><?= _('Ingrese un número de contacto'); ?></span>');
                                setTimeout(function() {
                                    $("#noti_phone_error").hide();
                                }, 3000);
                            }

                            return false;
                        }

                        function borrarEmail(item) {
                            $('#confirmar_eliminar_email').modal('show');
                            $("#confirmar_eliminar_email").data('id', item.parentElement.parentElement.id);
                        }

                        function actualizarSuscripcionMailing(item) {
                            var operacion;

                            if (item.checked)
                                operacion = "suscribir";
                            else
                                operacion = "desuscribir";

                            var email = item.value;
                            var reporte = $(item).attr('data-reporte');

                            $.ajax({
                                url: "configuracion_tab_emails.php",
                                type: "POST",
                                data: {
                                    operacion: operacion,
                                    reporte: reporte,
                                    email: email
                                },
                                success: function(msg) {
                                    ga('send', 'pageview', '/configuracion_tab_emails.php?function=actualizarSuscripcionMailing');
                                }
                            });

                        }

                        function borrarEmailConfirmado(id) {
                            $.ajax({
                                url: "configuracion_tab_emails.php",
                                type: "POST",
                                data: {
                                    eliminar: id
                                },
                                success: function(msg) {
                                    ga('send', 'pageview', '/configuracion_tab_emails.php?function=borrarEmailconfirmado');
                                    if (msg == "1") {
                                        $("#" + id).remove();
                                        if ($("#direcciones").find("tr").length <= 1) {
                                            $("#direcciones").html("<tr id='vacio'><td><?= _('No posee direcciones de email ingresadas en el sistema'); ?></td></tr>");
                                        }
                                    }
                                }
                            });
                        }

                        function guardarEmail() {
                            var nombre = $("#nombre").val();
                            var email = $("#email").val().trim();
                            if (!validateEmail(email)) {
                                $("#form_response_email").html('<span id="noti_error" style="color:red;"><?= _('Ingrese una dirección de email válida'); ?></span>');
                                setTimeout(function() {
                                    $("#noti_error").hide();
                                }, 2000);
                            } else {
                                $.ajax({
                                    url: "configuracion_tab_emails.php",
                                    type: "POST",
                                    data: {
                                        nombre: nombre,
                                        email: email
                                    },
                                    success: function(msg) {
                                        ga('send', 'pageview', '/configuracion_tab_emails.php?function=guardarEmail');
                                        if (msg) {
                                            if ($("#direcciones").find("tr").attr('id') == "vacio") {
                                                $("#direcciones").html("<thead><tr><th><?= _('Nombre'); ?></th><th><?= _('Email'); ?></th><th><?= _('Eliminar'); ?></th><th><?= _('Resumen diario') ?></th><th><?= _('Resumen semanal') ?></th><th><?= _('Resumen mensual') ?></th></tr></thead>");
                                            }
                                            var row = "<tr id='" + msg + "'>" +
                                                "<td>" + nombre + "</td>" +
                                                "<td>" + email + "</td>" +
                                                "<td><img id='msg' alt='borrar' src='img/delete.png' width='12px' class='borrar' onclick='borrarEmail(this)'/></td>" +
                                                "<td><input type='checkbox' name='reporte_diario_status' data-reporte='diario' value='" + email + "' onchange='actualizarSuscripcionMailing(this)'/></td>" +
                                                "<td><input type='checkbox' name='reporte_semanal_status' data-reporte='semanal' value='" + email + "' onchange='actualizarSuscripcionMailing(this)'/></td>" +
                                                "<td><input type='checkbox' name='reporte_mensual_status' data-reporte='mensual' value='" + email + "' onchange='actualizarSuscripcionMailing(this)'/></td>" +
                                                "</tr>";
                                            $("#direcciones").append(row);
                                            $("#nombre").val("");
                                            $("#email").val("");
                                        }
                                    }
                                });
                            }
                        }

                        function cargarSubusuarios() {
                            $.ajax({
                                url: "configuracion_tab_dispositivos.php?operacion=cargarSubusuarios",
                                type: "POST",
                                data: {
                                    operacion: 'cargarSubusuarios'
                                },
                                success: function(msg) {
                                    var obj = JSON.parse(msg);
                                    var listadoDeOpciones = "";
                                    var i;
                                    for (i = 0; i < obj.length; i++) {
                                        var optionActual = "<option value='" + obj[i].id + "'>" + obj[i].codigo + "</option>";
                                        listadoDeOpciones += optionActual;
                                    }
                                    $("#selectUsuarioUnico").empty();
                                    $("#selectUsuarioUnico").selectpicker('refresh');
                                    $("#selectUsuarioUnico").append(listadoDeOpciones);
                                    $("#selectUsuarioUnico").selectpicker('val', []);
                                    $("#selectUsuarioUnico").selectpicker('refresh');
                                }
                            });
                        }

                        function cargarPermisos() {
                            $.ajax({
                                url: "configuracion_tab_dispositivos.php?operacion=cargarPermisos",
                                type: "POST",
                                data: {
                                    subusuario_id: $("#selectUsuarioUnico")[0].value,
                                    operacion: 'cargarPermisos'
                                },
                                success: function(msg) {
                                    var obj = JSON.parse(msg);
                                    var listadoDeOpciones = "";
                                    var i;
                                    for (i = 0; i < obj.length; i++) {
                                        var optionActual = "<option value='" + obj[i].id + "' " + (obj[i].valor ? 'selected' : '') + ">" + obj[i].codigo + "</option>";
                                        listadoDeOpciones += optionActual;
                                    }
                                    $("#selectDispositivos").empty();
                                    $("#selectDispositivos").selectpicker('refresh');
                                    $("#selectDispositivos").append(listadoDeOpciones);
                                    $("#selectDispositivos").selectpicker('refresh');
                                }
                            });
                        }

                        function uploadImagenLogo() {
                            var data = new FormData();
                            data.append('file', $('#imageFileName')[0].files[0]);
                            data.append('operacion', 'upload_logo');
                            $.ajax({
                                url: "util_upload.php",
                                type: "POST",
                                dataType: 'JSON',
                                cache: false,
                                contentType: false,
                                processData: false,
                                data: data,
                                success: function(data) {
                                    ga('send', 'pageview', '/util_upload.php?function=upload_logo');
                                    if (data == 0)
                                        alert("<?= _('Archivo invalido o supera el tamaño máximo de 100Kb') ?>");
                                    else {
                                        $('#imageFileClick').attr('src', data);
                                        document.getElementById('form_response').innerHTML = '<span style="color:darkseagreen;"><?= _('Logo actualizado correctamente!'); ?></span>';
                                    }
                                }
                            });
                        }


                        // ---------------- Mobile App Configuration Tab (Only Flash Users) ----------------
                        // Mobile rejected state arrays
                        let stateId;
                        const toTrash = [];
                        const toCreate = [];
                        const toUpdate = [];

                        function saveReasons() {
                            // Get switches options
                            const manualSign = $('#manual-sign-option > label.active > input').val();
                            const picture = $('#picture-option > label.active > input').val();
                            const geocode = $('#geocode-option > label.active > input').val();
                            const gps_cert_precision = $('#precision_certificacion > label.active > input').val();
                            const sobreescribir = $('#sobreescribir_estado > label.active > input').val();
                            const enable_start_visit = $('#enable_start_visit > label.active > input').val();

                            const macroState = {
                                manualSign,
                                picture,
                                geocode,
                                gps_cert_precision,
                                sobreescribir,
                                enable_start_visit
                            };

                            $.ajax({
                                url: 'configuracion_tab_mobile_app.php',
                                type: "POST",
                                dataType: 'JSON',
                                data: {
                                    operation: 'save',
                                    toCreate: JSON.stringify(toCreate),
                                    toUpdate: JSON.stringify(toUpdate),
                                    toTrash: JSON.stringify(toTrash),
                                    macroState: JSON.stringify(macroState)
                                },
                                success: function(msg) {
                                    if (msg != 0) {
                                        $('#ajaxMsj').html("<?= _('¡Cambios Guardados!'); ?>");

                                        // Refresh page after save to load new sub states
                                        if (!allEmpty(toCreate, toUpdate, toTrash)) {
                                            let time = 3 // seconds
                                            setInterval(function() {
                                                time--;
                                                $('#ajaxMsj-timer').html(`Refrescando en ${time}`);
                                                if (time === 0)
                                                    location.reload();
                                            }, 1000);
                                        }
                                    } else {
                                        $('#ajaxMsj').html("<?= _('Error al guardar. Por favor inténtalo nuevamente'); ?>")
                                    }
                                }
                            });
                        }

                        function addNewReason() {
                            // Generate a random UI creation id.
                            const randomId = uuidv4()

                            // Note we add randomId to input and NewReason attrb to span parent
                            $('#rejected-reasons-list').append(
                                `<span uuiid="${randomId}" newreason="${randomId}" class="rejected-reason-span">
                                <input maxlength="30" id="${randomId}" value="" onChange="inputChangeListener(this)">
                                <div class="rejected-reason-remove" onclick="removeReason('${randomId}')" >
                                    <i style="color: #888888" class="fa fa-trash fa-2x"></i>
                                </div>
                            </span>`
                            );

                            // Disable button if max reasons
                            if ($('#rejected-reasons-list span').length >= 10) {
                                $('#addReasonsButton').attr('disabled', true);
                            }

                        }

                        function removeReason(id) {

                            // Disable button if max reasons
                            if ($('#rejected-reasons-list span').length < 10) {
                                $('#addReasonsButton').attr('disabled', false);
                            }

                            if (isIdNumber(id)) {
                                addToTrashArray(id);
                            }
                            // Just removes UI element
                            $(`span[uuiid=${id}]`).remove()
                        }

                        // Helpers
                        const isIdNumber = (id) => /^\d+$/.test(id);
                        const allEmpty = (...args) => args.every(array => !array.length);

                        // Event handlers
                        const inputChangeListener = (inputData) => {
                            // Is in db? numeric ids are db only
                            if (isIdNumber(inputData.id)) {
                                addToUpdateArray(inputData)
                            } else {
                                addToCreateArray(inputData)
                            }
                        }

                        // Add to corresponsing array functions
                        const addToTrashArray = (id) => toTrash.push(id);

                        const addToUpdateArray = (inputData) => toUpdate.push({
                            id: inputData.id,
                            descripcion: inputData.value,
                        });

                        const addToCreateArray = (inputData) => toCreate.push({
                            id_estado: stateId,
                            descripcion: inputData.value,
                            codigo: null,
                            fecha_borrado: null,
                            geocode_client: 0,
                            comentario: 0,
                            firma: 0,
                            foto: 0,
                        });

                        // ---------------- Mobile App Configuration Tab (Only Flash Users) ----------------

                        function emailOriginChanged() {
                            if ($(".bootstrap-switch-id-in-route-state").hasClass('bootstrap-switch-on')) {
                                $("#eta").removeAttr('disabled');
                            } else {
                                $("#eta").attr('disabled', 'disabled').prop("checked", false);
                            }
                        }

                        function inRouteStateChanged() {
                            if ($(".bootstrap-switch-id-in-route-state").hasClass('bootstrap-switch-on')) {
                                $("#eta").removeAttr('disabled');
                            } else {
                                $("#eta").attr('disabled', 'disabled').prop("checked", false);
                            }
                        }

                        function receivedStateChanged() {
                            if ($(".bootstrap-switch-id-received-state").hasClass('bootstrap-switch-on')) {
                                $("#eta_received").removeAttr('disabled');
                            } else {
                                $("#eta_received").attr('disabled', 'disabled').prop("checked", false);
                            }
                        }

                        function receivedMerchantStateChanged() {
                            if ($(".bootstrap-switch-id-merchant-received-state").hasClass('bootstrap-switch-on')) {
                                $("#eta_merchant_received").removeAttr('disabled');
                            } else {
                                $("#eta_merchant_received").attr('disabled', 'disabled').prop("checked", false);
                            }
                        }

                        function triggerSmsConfigTab() {
                            $('#smsConfigTab').click()
                        }

                        function triggerDeliveryModificationConfigTab(state) {
                            // TODO: detect what state was clicked and pass it to webcomponent
                            $('#tntAdvancedConfigTab').click()
                        }
                    </script>
                </td>
            </tr>
        </table>

        <div id='floatclearer'>.</div>

    </div><!-- end div contentwrapper -->
</body>

</html>
