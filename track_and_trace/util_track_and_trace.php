<?php
include_once('/var/www/saas/track_and_trace/constants.php');
include_once('/var/www/saas/track_and_trace/sendgrid_template_ids.php');
include_once('/var/www/saas/util_db.php');
include_once('/var/www/saas/util_session.php');
include_once('/var/www/saas/track_and_trace/vendor/autoload.php');
include_once('/var/www/saas/track_and_trace/sendgrid.env.php');
include_once('/var/www/saas/classes/DomainObjects/UsuariosParametro.php');
include_once('/var/www/saas/classes/Mail/Mail.php');
include_once('/var/www/saas/classes/Settings/Notifications/OrderEventTypes.php');
include_once('/var/www/saas/track_and_trace/GoneThroughStates.php');
include_once('/var/www/saas/track_and_trace/tnt.env.php');

define("BASE_URL", isProduction() ? SAAS_URL : SAAS_TEST_URL);

function get_message_type_by_gender()
{
    return [
        'request' => [
            'label' => _('pedido'),
            'translation' => 'Tu pedido',
            'gender' => 'M'
        ],
        'order' => [
            'label' => _('orden'),
            'translation' => 'Tu orden',
            'gender' => 'F'
        ],
        'visit' => [
            'label' => _('visita'),
            'translation' => 'Tu visita',
            'gender' => 'F'
        ],
        'service' => [
            'label' => _('servicio'),
            'translation' => 'Tu servicio',
            'gender' => 'M'
        ]
    ];
};

function set_locale_tnt($locale_tnt = "es_AR.UTF-8")
{
    putenv("LANG=" . $locale_tnt);
    $localeSetted = setlocale(LC_MESSAGES, $locale_tnt);
    $domain = "messages";
    $path_to_setted = bindtextdomain($domain, dirname(__FILE__) . '/locale');
    bind_textdomain_codeset($domain, 'UTF-8');
    textdomain($domain);
    loggear("localeSetted: $localeSetted", 'tnt_test');
    loggear("path_to_setted: $path_to_setted", 'tnt_test');
}

/**
 * Assign delivery date
 * @param object $dateObject
 * @param string $locale  From user
 * @return object
 */
function getSemanticDate($date, $locale = "es_AR.UTF-8")
{

    if (is_null($locale)) $locale = "es_AR.UTF-8";
    setlocale(LC_ALL, $locale);

    if (is_string($date)) {
        $newDate = DateTime::createFromFormat('Y-m-d\TH:i:s', $date);
        if ($newDate == false) {
            loggear("Error on semanticDate $date", "getSemanticDate");
            return [
                'day' => "",
                'number' => "",
                'month' => ""
            ];
        }
        $date = $newDate;
    }

    $day = strftime("%A", $date->getTimestamp());
    $number = strftime("%d", $date->getTimestamp());
    $month = strftime("%B", $date->getTimestamp());
    return array(
        'day' => $day,
        'number' => $number,
        'month' => $month
    );
}

/**
 * Calculate timeFrame
 * @param string $estimatedDateFromDB . ej "2021-05-11 13:10:54"
 * @param string $userTimeZone . ej: "America/Buenos_Aires"
 * @param int $tnt_margin in seconds . ej: 3600
 * @param int $tnt_margin2 in seconds . ej: 3600
 * @param string $timeWindow . ej: '9:30 - 16:00'
 * @return string $deliveryTime . ej: '9:30 am - 11:00 am'
 */
function calculateTimeFrame($estimatedDateFromDB, $userTimeZone, $tnt_margin, $tnt_margin2, $timeWindow = null)
{
    $tnt_margin2 = $tnt_margin2 ?: $tnt_margin;

    if ($estimatedDateFromDB) {

        $estimatedArrivalDate = DateTime::createFromFormat('Y-m-d H:i:s', $estimatedDateFromDB, new DateTimeZone("America/Buenos_Aires"));
        $estimatedArrivalDate->setTimezone(new DateTimeZone($userTimeZone));

        $seconds = strtotime($estimatedArrivalDate->format('Y-m-d H:i:s'));
        $rounded_seconds = round($seconds / (30 * 60)) * (30 * 60);

        if ($tnt_margin && $tnt_margin2) {

            $deliveryBottom = $rounded_seconds - $tnt_margin;
            $deliveryTop = $rounded_seconds + $tnt_margin2;
        
            if ($timeWindow) {
                list($timeWindowStart, $timeWindowEnd) = explode(' - ', $timeWindow);
        
                $timeWindowStart = strtotime($timeWindowStart);
                $timeWindowEnd = strtotime($timeWindowEnd);

                if ($rounded_seconds < $timeWindowStart || $rounded_seconds > $timeWindowEnd) {
                    $deliveryBottom = $rounded_seconds - $tnt_margin;
                    $deliveryTop = $rounded_seconds + $tnt_margin2;
                } else {
                    $deliveryBottom = max($deliveryBottom, $timeWindowStart);
                    $deliveryTop = min($deliveryTop, $timeWindowEnd);
                }
            }

            $bottom = date('H:i a', $deliveryBottom);
            $top = date('H:i a', $deliveryTop);
        
            $deliveryTime = $bottom . " - " . $top;
        
    }}

    return $deliveryTime;
};

/**
 * Calculate timeFrame
 * @param string $estimatedDepositExitFromDB . ej "2021-05-11 13:10:54"
 * @param string $orderTimeZone . ej: "America/Buenos_Aires"
 * @param array  $orderStateDelivered data of order in last state
 * @return array 'departureDate' => $departureDate, 'deliveredTime' => $deliveredTime, 'deliveredDate' => $deliveredDate, 'visitDate' => $visitDate
 */
function calculateDeliveredData($depositExitDateFromDB, $orderTimeZone = 'America/Buenos_Aires', $orderStateDelivered)
{
    if (!in_array($orderTimeZone, DateTimeZone::listIdentifiers())) {
        $orderTimeZone = 'America/Buenos_Aires';
    }

    $now = new DateTime('now', new DateTimeZone($orderTimeZone));

    if (!$depositExitDateFromDB)
        $departureDate = $now->format('d/m/Y h:i a');
    else {
        $deliveredDate = new DateTime($depositExitDateFromDB, new DateTimeZone('America/Buenos_Aires'));
        $deliveredDate->setTimezone(new DateTimeZone($orderTimeZone));
        $departureDate = $deliveredDate->format('d/m/Y h:i a');
    }

    if (!$orderStateDelivered) {
        $visitDate = $now->format('d/m/Y');
        $deliveredTime = $now->format('h:i a');
    } else {
        $deliveredDate = new DateTime($orderStateDelivered['timestamp'], new DateTimeZone('America/Buenos_Aires'));
        $deliveredDate->setTimezone(new DateTimeZone($orderTimeZone));
        $visitDate = $deliveredDate->format('d/m');
        $deliveredTime = $deliveredDate->format('h:i a');
    }
    return array(
        'departureDate' => $departureDate,
        'deliveredTime' => $deliveredTime,
        'deliveredDate' => $deliveredDate,
        'visitDate' => $visitDate
    );
};

/**
 * Assign email template
 * @param string $emailType type of email according to state
 * @param string $operation  order type of operation
 * @return string
 */
function assignSendGridEmailTemplate($emailType, $operation)
{
    $isProduction = isProduction();
    // Decide whether if is delivery or pickup operation type
    $isDelivery = $operation == 1;
    // Select emails according to environment

    switch ($emailType) {
        case 'startEmail':
            $templateId = SendGridTemplatesIds::getPreparingTemplate($isDelivery, $isProduction);
            break;
        case 'inTransitEmail':
            $templateId = SendGridTemplatesIds::getInTransitTemplate($isDelivery, $isProduction);
            break;
        case 'isCloseEmail':
            $templateId = SendGridTemplatesIds::getIsCloseTemplate($isDelivery, $isProduction);
            break;
        case 'fulfilledEmail':
            $templateId = SendGridTemplatesIds::getFulfilledTemplate($isDelivery, $isProduction);
            break;
        case 'notFulfilledEmail':
            $templateId = SendGridTemplatesIds::getNotFulfilledTemplate($isDelivery, $isProduction);
            break;
        default:
            $templateId = SendGridTemplatesIds::getPreparingTemplate($isDelivery, $isProduction);
            break;
    }

    return $templateId;
}

/**
 * get order merchants contact info
 * @param string $orderId
 * @return array
 */
function getMerchantsByOrderId($orderId)
{
    $merchants_array = array();
    $query = "SELECT *
                  FROM pedidos_remitentes pr
                  INNER JOIN remitentes r ON (r.id = pr.id_remitente)
                  INNER JOIN remitentes_emails re ON (re.id_remitente = r.id)
                  WHERE id_pedido = $orderId";

    $merchants = mysql_query($query);

    if (mysql_num_rows($merchants) > 0) {
        while ($row = mysql_fetch_assoc($merchants)) {
            $merchants_array[] = $row;
        };
        return $merchants_array;
    }
    return [];
}

/**
 * Recibe un array de id de pedidos generando sus correspondientes QTN e insertandolos en la tabla pedidos_tracking_number
 *
 * @param $id_pedidos
 *
 */
function bindQTN($id_pedidos)
{
    if (gettype($id_pedidos) !== 'array')
        throw new Exception('id_pedidos should be an array');

    $pedidos_string = implode(',', $id_pedidos);

    $query_pedidos_dupe = "SELECT * FROM pedidos_tracking_number where id_pedido IN ($pedidos_string)";
    $r_query_pedidos_dupe = mysql_query($query_pedidos_dupe);

    if (mysql_num_rows($r_query_pedidos_dupe) > 0) {
        $dupe_ids = [];
        while ($row = mysql_fetch_assoc($r_query_pedidos_dupe)) {
            $dupe_ids[] = $row['id_pedido'];
            $records[$row['id_pedido']] = $row['qtn'];
        }
        $id_pedidos = array_values(array_diff($id_pedidos, $dupe_ids));
    }

    if (count($id_pedidos) > 0) {
        $url = ROUTE_API_URL_PROD . 'qtn?quantity=' . count($id_pedidos);
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_URL => $url,
            CURLOPT_HTTPHEADER => ['x-saas-apiKey: ' . LEGACY_API_KEY]
        ]);
        $api_response = curl_exec($curl);
        curl_close($curl);
        $response_object = json_decode($api_response);
        $qtn_array = $response_object->data->qtn;

        $id_usuario = mysql_result(mysql_query("SELECT id_usuario FROM pedidos WHERE id = " . $id_pedidos[0]), 0, 0);
        $organization = get_parametro_usuario($id_usuario, 'TRACK_AND_TRACE');
        $qtn_links_array = shortQtnUrls($qtn_array, $organization);

        for ($i = 0; $i < count($qtn_links_array); ++$i) {
            $id_pedido = $id_pedidos[$i];
            $qtn = $qtn_links_array[$i]['qtn'];
            $link = $qtn_links_array[$i]['link'];
            $records[$id_pedido] = $qtn;
            $query = "INSERT INTO pedidos_tracking_number (id_pedido, qtn, asigned_date, link)
                              VALUES ('$id_pedido','$qtn', NOW(), '$link')";
            mysql_query($query);
        }
    }

    return json_encode($records);
}

function shortQtnUrls($qtn_array, $organization)
{
    $url = URL_SHORTENER_ENDPOINT . '/action/shorten_bulk';

    $links_object = new stdClass();
    $links_object->links = [];
    $qtn_array = isset($qtn_array) ? $qtn_array : [];
    for ($i = 0; $i < count($qtn_array); ++$i) {
        $link = new stdClass();

        $link->url = $buttonLink = isProduction() ?
            'https://tracking.qmt.app/trace?qtn=' . $qtn_array[$i] . '&organization=' . $organization :
            'https://tracking-test.quadminds.com/trace?qtn=' . $qtn_array[$i] . '&organization=' . $organization;

        $links_object->links[] = $link;
    }

    $params = ['key' => URL_SHORTENER_APIKEY, 'data' => json_encode($links_object, JSON_UNESCAPED_SLASHES)];
    $curl = curl_init();
    curl_setopt_array($curl, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_URL => $url . '?' . http_build_query($params),
        CURLOPT_POST => 1,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    $api_response = curl_exec($curl);
    $error = curl_error($curl);
    curl_close($curl);
    $response_object = json_decode($api_response);

    //Estructura que retorna el servicio de shortner
    //
    //{
    //     "action": "shorten_bulk",
    //     "result": {
    //         "shortened_links": [
    //             {
    //                 "long_url": "https://flash.quadminds.com",
    //                 "short_url": "https://qmt.app/mQk6s"
    //             },
    //             {
    //                 "long_url": "https://saas.quadminds.com",
    //                 "short_url": "https://qmt.app/jbQA9"
    //             }
    //         ]
    //     }
    // }

    $shortened_links = $response_object->result->shortened_links;
    $qtn_links_array = [];
    for ($i = 0; $i < count($qtn_array); ++$i) {
        $qtn_links_array[] = ['qtn' => $qtn_array[$i], 'link' => $shortened_links[$i]->short_url];
    }

    return $qtn_links_array;
}

/**
 * @param int $orderId
 * @param int $stateId
 * @return bool
 */
function checkIfAlreadyOnState($orderId, $stateId)
{
    $query = "SELECT id_estado FROM pedidos_estados_hist WHERE id_pedido = '$orderId' order by timestamp desc limit 1";
    $result = mysql_query($query);
    if (mysql_num_rows($result) > 0) {
        $peh = mysql_fetch_array($result);
        if ($peh['id_estado'] == $stateId) {
            loggear("[WARNING] Order " . $orderId . " has already passed state " . GoneThroughStates::getEnumState($stateId), 'TNT');
            return true;
        }
    }

    return false;
}

/**
 * @param int $userId
 * @param OrderEventTypes $eventType
 * @return bool
 */
function checkIfConfiguredWebhooks($userId, $eventType)
{
    $query = "SELECT * FROM `webhooks` w
              INNER JOIN `webhooks_eventos` we ON w.id = we.id_webhook
              INNER JOIN `entidades_eventos` eventos ON  we.id_evento = eventos.id
              WHERE w.id_usuario = $userId AND eventos.evento = '$eventType'";

    $result = mysql_query($query);
    if (mysql_num_rows($result) > 0) {

        return true;
    }

    return false;
}

/**
 *
 * Genera el evento de pedido en preparacion y genera el email de notificacion al destinatario.
 *
 * @param $id_pedido
 * @param $timestamp
 * @param null $latitud
 * @param null $longitud
 * @return resource
 */
function addStandardReceivedState($id_pedido, $timestamp, $latitud = null, $longitud = null)
{
    if (checkIfAlreadyOnState($id_pedido, GoneThroughStates::getStateIdByEnum(OrderEventTypes::planned))) return;

    $return = mysql_query("INSERT INTO pedidos_estados_hist (id_pedido, id_estado, timestamp, latitud, longitud)
                          VALUES ($id_pedido, 1, '$timestamp', '$latitud', '$longitud' )");

    $order = getOrderById($id_pedido);
    if ($order) {
        try {
            generateStartEmail($order);
        } catch (Exception $e) {
            loggear('generateStartEmail failed with message: ' . $e->getMessage(), 'TNT');
        }
        if (checkIfExistsInEventService($id_pedido)) {
            generateSmsNotification($order, OrderEventTypes::planned);
            if (checkIfConfiguredWebhooks($order["id_usuario"], OrderEventTypes::planned)) {
                generateWebhookNotification($order, OrderEventTypes::planned);
            }
        }
    }

    return $return;
}

/**
 *
 * Genera el evento de pedido en transito y genera el email de notificacion al destinatario.
 *
 * @param $id_pedido
 * @param $timestamp
 * @param $latitud
 * @param $longitud
 * @return resource
 */
function addStandardInRouteState($id_pedido, $timestamp, $latitud, $longitud)
{
    if (checkIfAlreadyOnState($id_pedido, GoneThroughStates::getStateIdByEnum(OrderEventTypes::route_started))) return;

    $query_estado = "INSERT INTO pedidos_estados_hist (id_pedido, id_estado, timestamp, latitud, longitud)
                          VALUES ($id_pedido, 2, '$timestamp', '$latitud', '$longitud' )";
    $return = mysql_query($query_estado);

    $order = getOrderById($id_pedido);
    if ($order) {
        generateInTransitEmail($order);
        generateMerchantInTransitEmail($id_pedido);
        if (checkIfExistsInEventService($id_pedido)) {
            generateSmsNotification($order, OrderEventTypes::route_started);
            if (checkIfConfiguredWebhooks($order["id_usuario"], OrderEventTypes::route_started)) {
                generateWebhookNotification($order, OrderEventTypes::route_started);
            }
        }
        generatePushNotification($order, OrderEventTypes::route_started);
    }

    return $return;
}

/**
 *
 * Genera el evento de pedido arribando o cerca.
 *
 * @param $id_pedido
 * @param $timestamp
 * @param $latitud
 * @param $longitud
 * @return resource
 */
function addStandardIsCloseState($id_pedido, $timestamp, $latitud, $longitud)
{
    if (checkIfAlreadyOnState($id_pedido, GoneThroughStates::getStateIdByEnum(OrderEventTypes::arriving))) return;

    loggear("add standard is close pedido: $id_pedido $timestamp", 'ce_in_tnt');

    $return = mysql_query("INSERT INTO pedidos_estados_hist (id_pedido, id_estado, timestamp, latitud, longitud)
                          VALUES ($id_pedido, 3, '$timestamp', '$latitud', '$longitud' )");

    $order = getOrderById($id_pedido);
    if ($order) {
        generateIsCloseEmail($order);
        if (checkIfExistsInEventService($id_pedido)) {
            generateSmsNotification($order, OrderEventTypes::arriving);
            if (checkIfConfiguredWebhooks($order["id_usuario"], OrderEventTypes::arriving)) {
                generateWebhookNotification($order, OrderEventTypes::arriving);
            }
        }
        generatePushNotification($order, OrderEventTypes::arriving);
    }

    return $return;
}

/**
 *
 * Genera el evento de pedido entregado y genera el email de notificacion al destinatario.
 *
 * @param $id_pedido
 * @param $timestamp
 * @param $latitud
 * @param $longitud
 * @return resource
 */
function addStandardDeliveredState($id_pedido, $timestamp, $latitud, $longitud)
{
    if (checkIfAlreadyOnState($id_pedido, GoneThroughStates::getStateIdByEnum(OrderEventTypes::delivered))) return;

    $return = mysql_query("INSERT INTO pedidos_estados_hist (id_pedido, id_estado, timestamp, latitud, longitud)
                          VALUES ($id_pedido, 4, '$timestamp', '$latitud', '$longitud' )");

    $order = getOrderById($id_pedido);
    if ($order) {
        generateDeliveredEmail($order);
        generateMerchantFulfilledEmail($id_pedido);
        if (checkIfExistsInEventService($id_pedido)) {
            generateSmsNotification($order, OrderEventTypes::delivered);
            if (checkIfConfiguredWebhooks($order["id_usuario"], OrderEventTypes::delivered)) {
                generateWebhookNotification($order, OrderEventTypes::delivered);
            }
        }
        generatePushNotification($order, OrderEventTypes::delivered);
    }

    return $return;
}

function addStandardRejectedState($id_pedido, $rejectedType)
{
    $order = getOrderById($id_pedido);
    generateNotFulfilledEmail($order, $rejectedType);
    generateMerchantNotFulfilledEmail($id_pedido, $rejectedType);
    if (checkIfConfiguredWebhooks($order["id_usuario"], OrderEventTypes::failed)) {
        generateWebhookNotification($order, OrderEventTypes::failed);
    }
    generatePushNotification($order, OrderEventTypes::failed);
}

function getOrderStates($id_pedido)
{
    $states = [];
    $query = "SELECT * FROM pedidos_estados_hist WHERE id_pedido = $id_pedido";
    $r_states = mysql_query($query);
    while ($row = mysql_fetch_assoc($r_states)) {
        $states[] = $row;
    }
    return $states;
}

function getFeedbackQuestions($id_pedido)
{
    $query = "SELECT uf.* FROM usuarios_tracking_feedback_questions uf INNER JOIN pedidos p ON (p.id_usuario = uf.id_usuario) WHERE p.id = $id_pedido";
    $r_states = mysql_query($query);
    return mysql_fetch_assoc($r_states);
}

function getFeedbackQuestionsByUser($userId)
{
    $query = "SELECT uf.* FROM usuarios_tracking_feedback_questions uf WHERE uf.id_usuario = $userId";
    $r_questions = mysql_query($query);
    return mysql_fetch_assoc($r_questions);
}

/**
 * @param string|number $oderId
 * @return array $details
 */
function getOrderItems($orderId)
{
    if (!$orderId) return;

    $query = "SELECT mp.descripcion as `name`, id_producto as id, codigo_producto as code, cantidad as qty, cantidad_entregada as delivered_qty
              FROM pedidos_detalles pd
              INNER JOIN maestro_productos mp ON mp.id = pd.id_producto
              WHERE pd.id_pedido = $orderId";
    $detailsResult = mysql_query($query);

    $details = [];

    while ($row = mysql_fetch_assoc($detailsResult)) {
        $details[] = $row;
    }

    return $details;
}

/**
 * @param string $qtn
 * @param array|string $userIds
 */
function getOrderByQTN($qtn, $userIds)
{
    // Safe check if single user id is passed as parameter
    if (!is_array($userIds)) {
        $userIds = array($userIds);
    }

    $userIds = implode(",", $userIds);

    $query = "SELECT ptn.id_pedido, ptn.qtn, ptn.link,
                     p.codigo_pedido, p.id_cliente, p.id_usuario, p.id_ce_estado, p.operacion,
                     c.razon_social, c.domicilio, c.latitud, c.longitud,
                     r.fecha, r.fecha_salida_deposito, r.id_dispositivo,
                     d.latitud_gps, d.longitud_gps, d.ult_medicion_gps,
                     cr.fecha_visitado, cr.fecha_programada_llegada,
                     cre.fecha_entregado,
                     u.url_logo, u.nombre as orgName, u.locale, u.timezone,
                     cee.id as id_estado, cee.descripcion as descripcion_estado, cee.afirmativo,
                     cese.id as id_subestado, cese.descripcion as descripcion_subestado,
                     pf.id_pedido as hasFeedback
                  FROM pedidos_tracking_number ptn
                    INNER JOIN pedidos p ON (p.id = ptn.id_pedido)
                    LEFT JOIN clientes_repartos cr ON (cr.id = p.id_cliente_reparto)
                    LEFT JOIN clientes_repartos_entregas cre ON (cre.id_cliente_reparto = cr.id)
                    LEFT JOIN repartos r ON (r.id = cr.id_reparto)
                    INNER JOIN dispositivos d ON (d.id = r.telefono_asignado)
                    INNER JOIN clientes c ON (c.id = p.id_cliente)
                    LEFT JOIN usuarios u ON (p.id_usuario = u.id)
                    LEFT JOIN ce_estados cee ON (p.id_ce_estado = cee.id)
                    LEFT JOIN ce_subestados cese ON (p.id_ce_subestado = cese.id)
                    LEFT JOIN pedidos_feedback pf ON (pf.id_pedido = p.id)
                  WHERE ptn.qtn = '$qtn'
                  AND p.id_usuario IN ($userIds)";

    $r_pedido = mysql_query($query);

    if (!$r_pedido) return false;

    if (mysql_num_rows($r_pedido) == 0) return false;

    if (mysql_num_rows($r_pedido) > 0) {
        $order = mysql_fetch_assoc($r_pedido);
        $orderId = $order['id_pedido'];
        $order['currentState'] = -1;
        $order['questions'] = getFeedbackQuestions($orderId);
        $order['items'] = getOrderItems($orderId);
        $states = getOrderStates($orderId);
        foreach ($states as $state) {
            $state['description'] = assignSemanticStatus($state['id_estado']);
            $order['states'][$state['id_estado']] = $state;
            if ($order['currentState'] < $state['id_estado'])
                $order['currentState'] = $state['id_estado'];
        }
        return $order;
    }
    return false;
}

function getOrderById($id_pedido)
{
    $query = "SELECT ptn.id_pedido, ptn.qtn, ptn.link,
                     p.id, p.codigo_pedido, p.id_cliente, p.id_usuario, p.operacion, p.time_window as order_time_window,
                     c.razon_social, c.domicilio, c.latitud, c.longitud, c.email, c.telefono_contacto, c.id_usuario as poi_id_usuario, c.codigo as codigo_cliente, c.time_window as poi_time_window,
                     r.fecha, r.fecha_salida_deposito, r.id as routeId, r.codigo as routeCode,
                     m.id as vehicleId, m.nombre as vehicleName, m.patente as vehiclePlate,
                     cr.fecha_visitado, cr.fecha_programada_llegada,
                     cre.fecha_entregado,
                     ch.id as driverId, ch.nombre as driverName, ch.telefono as driverPhone,
                     ch.documento as driverDocument,
                     u.url_logo, u.timezone, up.valor as user_key, u.nombre as orgName, u.locale,
                     cee.id as id_estado, cee.descripcion as descripcion_estado, cee.afirmativo,
                     cese.id as id_subestado, cese.descripcion as descripcion_subestado,
                     pf.id_pedido as hasFeedback
                    FROM pedidos p
                    INNER JOIN pedidos_tracking_number ptn ON (p.id = ptn.id_pedido)
                    LEFT JOIN clientes_repartos cr ON (cr.id = p.id_cliente_reparto)
                    LEFT JOIN clientes_repartos_entregas cre ON (cre.id_cliente_reparto = cr.id)
                    LEFT JOIN repartos r ON (r.id = cr.id_reparto)
                    LEFT JOIN dispositivos d ON (r.id_dispositivo = d.id)
                    LEFT JOIN maquinas m ON (d.id_maquina = m.id)
                    LEFT JOIN choferes ch ON (ch.id = r.id_chofer)
                    INNER JOIN clientes c ON (c.id = p.id_cliente)
                    INNER JOIN usuarios u ON (p.id_usuario = u.id)
                    INNER JOIN usuarios_parametros up ON (up.id_usuario = u.id AND up.codigo_parametro = 'TRACK_AND_TRACE')
                    LEFT JOIN ce_estados cee ON (p.id_ce_estado = cee.id)
                    LEFT JOIN ce_subestados cese ON (p.id_ce_subestado = cese.id)
                    LEFT JOIN pedidos_feedback pf ON (pf.id_pedido = p.id)
                  WHERE p.id = $id_pedido";

    $r_pedido = mysql_query($query);

    if (mysql_num_rows($r_pedido) > 0) {
        $order = mysql_fetch_assoc($r_pedido);
        $orderId = $order['id_pedido'];
        $order['currentState'] = -1;
        $order['questions'] = getFeedbackQuestions($orderId);
        $order['items'] = getOrderItems($orderId);
        $states = getOrderStates($orderId);
        foreach ($states as $state) {
            $state['description'] = assignSemanticStatus($state['id_estado']);
            $order['states'][$state['id_estado']] = $state;
            if ($order['currentState'] < $state['id_estado'])
                $order['currentState'] = $state['id_estado'];
        }
        return $order;
    }
    return false;
}

function validateQTN($qtn)
{
    $query = "SELECT ptn.id_pedido
                  FROM pedidos_tracking_number ptn
                  WHERE ptn.qtn = '$qtn'";

    $r_pedido = mysql_query($query);
    if (mysql_num_rows($r_pedido) > 0)
        return true;
    else
        return false;
}

// ---------------------------------------------------- Emails ----------------------------------------------------

/**
 * WIP Jan/2022 meant to be used later to standardize email payloads
 * Groups data into key entities (organization, order and poi)
 * @param object|array $data
 * @return array ['organization' => $orgData,'order' => $orderData,'poi' => $poiData,'env' => BASE_URL]
 */
function buildStandardEmailPayload($data)
{

    //If environment Production or Testing
    $orgLogo = BASE_URL . 'img/' . $data['url_logo'];

    $poiData = array(
        'name' => $data['razon_social'],
        'phone' => $data["telefono_contacto"],
        'address' => $data['domicilio']
    );

    $orderData = array(
        'name' => $data['razon_social'],
        'items' => $data['items'],
        'phone' => $data["telefono_contacto"],
        'address' => $data['domicilio'],
        'number' => $data['codigo_pedido'],
    );

    $orgData = array(
        'logo' => $orgLogo,
        'name' => $data['orgName'],
    );

    $standarPayload = array(
        'organization' => $orgData,
        'order' => $orderData,
        'poi' => $poiData,
        'env' => BASE_URL
    );

    return $standarPayload;
}

function generateMerchantInTransitEmail($id_pedido)
{
    //Order
    $order = getOrderById($id_pedido);

    //Array merchants
    $merchants = getMerchantsByOrderId($id_pedido);
    if (empty(array_filter($merchants))) return;

    //SenderEmail
    $poiEmail = getDestinationEmail($order);
    loggear("Merchant In Transit email: " . json_encode($merchants), 'TNT');

    //Send mail or not
    $send_enabled = get_parametro_usuario($order['id_usuario'], 'TNT_MERCHANT_RECEIVED_STATE_ENABLED');

    if ($send_enabled === '0') return;

    //ETA
    $eta_enabled = filter_var(get_parametro_usuario($order['id_usuario'], 'TNT_MERCHANT_RECEIVED_STATE_ETA_ENABLED'), FILTER_VALIDATE_BOOLEAN);

    if (!$eta_enabled) {
        loggear("Correo Merchant con eta deshabilitado para el id_usuario  " . $order['id_usuario'] . " id_pedido: $id_pedido", "TNT");
    }

    //TimeZone
    $estimatedDateFromDB = $order['fecha_programada_llegada'];
    $userTimeZone = getTZUsuarioById($order['id_usuario']);
    $tnt_margin = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN');
    $tnt_margin2 = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN_2');
    $rawTimeWindow = (!empty($order['order_time_window']) && $order['order_time_window'] !== 'FFFFFFFFFFFF') ? $order['order_time_window'] : $order['poi_time_window'];
    $timeWindow = parseTimeWindow($rawTimeWindow);

    $timeFrame = calculateTimeFrame($estimatedDateFromDB, $userTimeZone, $tnt_margin, $tnt_margin2, $timeWindow);

    $date = date_create_from_format('Y-m-d', $order['fecha'], new DateTimeZone($userTimeZone));

    //If environment Production or Testing
    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];
    loggear("locale seteado enla oredn" . $order['locale']);
    //Translations
    $translations = getTranslations('mail-merchant-2', 'request', $order['locale']);

    //SemanticDate
    $semanticDate = getSemanticDate($date, $order['locale']);

    $orgName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $data = array(
        'logo' => $logoOrg,
        'qtn' => $order['qtn'],
        'items' => $order['items'],
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'poiPhone' => $order["telefono_contacto"],
        'poiEmail' => $poiEmail,
        'date' => $semanticDate,
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'texts' => $translations,
        'eta' => $eta_enabled,
        'timeFrame' => $timeFrame
    );

    $title = _("📦 Pedido {$order['codigo_pedido']} para " . ($orgName ? $orgName : $order['razon_social']) . " está en camino");

    $templateId = SendGridTemplatesIds::getInTransitMerchantId(isProduction());

    foreach ($merchants as $merchant) {
        sendMailTrackAndTraceSendGrid(
            $title,
            $merchant['email'],
            $data,
            $order['id_usuario'],
            false,
            'tnt-merchant-in-transit',
            $templateId
        );
    }
}

function generateMerchantFulfilledEmail($id_pedido, $force = false)
{
    //Order
    $order = getOrderById($id_pedido);

    //Array merchants
    $merchants = getMerchantsByOrderId($id_pedido);
    if (empty(array_filter($merchants))) return;

    //SenderEmail
    $poiEmail = getDestinationEmail($order);
    loggear("Merchant fulfilled email: " . json_encode($merchants), 'TNT');

    //Send mail or not
    $sendEnabled = get_parametro_usuario($order['id_usuario'], 'TNT_MERCHANT_FULFILLED_STATE_ENABLED');

    if ($sendEnabled === '0' && !$force) return;

    //Generate epod link
    $epodLink = generateEpodLink($order);

    //DeliveredData
    $depositExitDateFromDB = $order['fecha_salida_deposito'];
    $orderTimeZone = $order['timezone'];
    $orderStateDelivered = $order['states'][4];

    $deliveredData = calculateDeliveredData($depositExitDateFromDB, $orderTimeZone, $orderStateDelivered);

    //If environment Production or Testing
    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    //Translations
    $translations = getTranslations('mail-merchant-3-entrega', 'request', $order['locale']);

    //SemanticDate
    $semanticDate = getSemanticDate($deliveredData["deliveredDate"], $order['locale']);

    $orgName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $data = array(
        'logo' => $logoOrg,
        'qtn' => $order['qtn'],
        'items' => $order['items'],
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'poiPhone' => $order["telefono_contacto"],
        'poiEmail' => $poiEmail,
        'date' => $semanticDate,
        'deliveryTime' => $deliveredData["deliveredTime"],
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'texts' => $translations,
        'epodLink' => $epodLink
    );

    $templateId = SendGridTemplatesIds::getFulfilledMerchantId(isProduction());

    $title = _("📦 Pedido {$order['codigo_pedido']} para {$orgName} ¡Realizado con éxito!");

    foreach ($merchants as $merchant) {
        sendMailTrackAndTraceSendGrid(
            $title,
            $merchant['email'],
            $data,
            $order['id_usuario'],
            false,
            'tnt-merchant-fulfilled',
            $templateId
        );
    }
}

function generateMerchantNotFulfilledEmail($id_pedido, $rejectedType)
{
    //Order
    $order = getOrderById($id_pedido);

    //Send mail or not
    $sendEnabled = get_parametro_usuario($order['id_usuario'], 'TNT_MERCHANT_' . $rejectedType . '_REJECTED_STATE_ENABLED');

    if ($sendEnabled === '0') return;

    if ($rejectedType === "PARTIAL") return generateMerchantFulfilledEmail($id_pedido, true);

    //Array merchants
    $merchants = getMerchantsByOrderId($id_pedido);
    if (empty(array_filter($merchants))) return;

    //Generate epod link
    $epodLink = generateEpodLink($order);

    //SenderEmail
    $poiEmail = getDestinationEmail($order);
    loggear("Merchant not fulfilled email: " . json_encode($merchants), 'TNT');

    //DeliveredData
    $depositExitDateFromDB = $order['fecha_salida_deposito'];
    $orderTimeZone = $order['timezone'];
    $orderStateDelivered = $order['states'][4];

    $deliveredData = calculateDeliveredData($depositExitDateFromDB, $orderTimeZone, $orderStateDelivered);
    $deliveredDate = $deliveredData['deliveredDate'] ? $deliveredData['deliveredDate'] : $deliveredData['visitDate'];

    //If environment Production or Testing
    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    //Translations
    $translations = getTranslations('mail-merchant-4-NO-entrega', 'request', $order['locale']);

    //SemanticDate
    $semanticDate = getSemanticDate($deliveredDate, $order['locale']);

    $orgName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $data = array(
        'logo' => $logoOrg,
        'qtn' => $order['qtn'],
        'items' => $order['items'],
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'poiPhone' => $order["telefono_contacto"],
        'poiEmail' => $poiEmail,
        'date' => $semanticDate,
        'deliveryTime' => $deliveredData["deliveredTime"],
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'texts' => $translations,
        'epodLink' => $epodLink
    );

    $templateId = SendGridTemplatesIds::getNotFulfilledMerchantId(isProduction());

    $title = _("📦 Pedido {$order['codigo_pedido']} para " . ($orgName ? $orgName : $order['razon_social']) . " no fue realizado");

    foreach ($merchants as $merchant) {
        sendMailTrackAndTraceSendGrid(
            $title,
            $merchant['email'],
            $data,
            $order['id_usuario'],
            false,
            'tnt-merchant-not-fulfilled',
            $templateId
        );
    }
}

function getTntMobileLink($tntUrl)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_POST, 1);
    $data = [
        'dynamicLinkInfo' => [
            'domainUriPrefix' => 'https://tntmobileapp.page.link',
            'link' => $tntUrl,
            'androidInfo' => [
                'androidPackageName' => 'com.quadminds.tntmobileapp'
            ],
            'iosInfo' => [
                'iosBundleId' => 'com.quadminds.tntmobileapp',
                'iosAppStoreId' => '1605792323',

            ],
            "navigationInfo" => [
                "enableForcedRedirect" => true,
            ],
        ]
    ];
    curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($curl, CURLOPT_HTTPHEADER, array(
        'Content-Type: application/json',
    ));
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_URL, "https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=" . getenv("FIREBASE_KEY"));
    $result = curl_exec($curl);
    if (!$result) {
        curl_close($curl);
        return false;
    }
    curl_close($curl);
    return json_decode($result)->shortLink;
}


// Start email
function generateStartEmail($order)
{
    $message_type_by_gender = get_message_type_by_gender();
    if (!checkTrackingNumber($order)) return;

    // Returns if not enabled by config param
    $send_enabled = get_parametro_usuario($order['id_usuario'], 'TNT_RECEIVED_STATE_ENABLED');
    if ($send_enabled === '0') return;

    $eta_disabled = get_parametro_usuario($order['id_usuario'], 'TNT_RECEIVED_STATE_ETA_DISABLED');
    $contactInfo = getContactInfo($order['id_usuario']);
    $notification_operation_type = get_parametro_usuario($order['id_usuario'], 'TNT_NOTIFICATION_OPERATION_TYPE');


    //TimeZone
    $estimatedDateFromDB = $order['fecha_programada_llegada'];
    $userTimeZone = getTZUsuarioById($order['id_usuario']);
    $tnt_margin = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN');
    $tnt_margin2 = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN_2');
    $rawTimeWindow = (!empty($order['order_time_window']) && $order['order_time_window'] !== 'FFFFFFFFFFFF') ? $order['order_time_window'] : $order['poi_time_window'];
    $timeWindow = parseTimeWindow($rawTimeWindow);

    $timeFrame = calculateTimeFrame($estimatedDateFromDB, $userTimeZone, $tnt_margin, $tnt_margin2, $timeWindow);

    $date = date_create_from_format('Y-m-d\TH:i:s', $order['fecha'] . 'T00:00:00');

    $email = getDestinationEmail($order);

    $buttonLink = isProduction() ? 'https://tracking.qmt.app/trace?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'] : 'https://tracking-test.quadminds.com/trace?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'];

    $mobileLink = getTntMobileLink($buttonLink);

    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    $notification_operation_type_secure = $notification_operation_type ? $notification_operation_type : 'request';

    $translations = $order['operacion'] == 1 ? getTranslations('mail-1', $notification_operation_type_secure, $order['locale']) : getTranslations('mail-retiro-1', 'request', $order['locale']);

    $semanticDate = getSemanticDate($date, $order['locale']);

    $templateId = assignSendGridEmailTemplate("startEmail", $order['operacion']);
    $resolved_gender = $message_type_by_gender[$notification_operation_type_secure]['gender'] == 'M' ? 'confirmado' : 'confirmada';
    $displayName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $title = $order['operacion'] == 1 ? _("⚡ Tu {$message_type_by_gender[$notification_operation_type_secure]['label']} {$order['codigo_pedido']} de " . ($displayName ? $displayName : $order['orgName']) . " está {$resolved_gender}") :  _("⚡ Tu solicitud de retiro {$order['codigo_pedido']} de {$order['orgName']} está asignada");

    $data = array(
        'logo' => $logoOrg,
        'items' => $order['items'],
        'link' => $buttonLink,
        'mobileLink' => $mobileLink,
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'qtn' => $order['qtn'],
        'date' => $semanticDate,
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'texts' => $translations,
        'eta' => true,
        'time' => $timeFrame,
        'contact' => $contactInfo,
        'title' => $title
    );

    loggear("[TNT][" . strtoupper(OrderEventTypes::planned) . "] Order Id: " . $order['id'] . " Payload: " . json_encode($data), 'TNT');

    if ($eta_disabled == 1) {
        unset($data['eta']);
    };

    if ($displayName) {
        $data['orgName'] = $displayName;
    };

    sendMailTrackAndTraceSendGrid(
        $title,
        $email,
        $data,
        $order['id_usuario'],
        false,
        'tnt-planned',
        $templateId
    );
}

function generateInTransitEmail($order)
{
    if (!checkTrackingNumber($order)) return;

    // Returns if not enabled by config param
    $send_enabled = get_parametro_usuario($order['id_usuario'], 'TNT_IN_ROUTE_STATE_ENABLED');
    if ($send_enabled === '0') return;

    $eta_disabled = get_parametro_usuario($order['id_usuario'], 'TNT_IN_ROUTE_STATE_ETA_DISABLED');
    $contactInfo = getContactInfo($order['id_usuario']);



    //TimeZone
    $estimatedDateFromDB = $order['fecha_programada_llegada'];
    $userTimeZone = getTZUsuarioById($order['id_usuario']);
    $tnt_margin = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN');
    $tnt_margin2 = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN_2');
    $rawTimeWindow = (!empty($order['order_time_window']) && $order['order_time_window'] !== 'FFFFFFFFFFFF') ? $order['order_time_window'] : $order['poi_time_window'];
    $timeWindow = parseTimeWindow($rawTimeWindow);

    $timeFrame = calculateTimeFrame($estimatedDateFromDB, $userTimeZone, $tnt_margin, $tnt_margin2, $timeWindow);

    $date = date_create_from_format('Y-m-d', $order['fecha'], new DateTimeZone($userTimeZone));

    $email = getDestinationEmail($order);

    $displayName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $buttonLink = isProduction() ? 'https://tracking.qmt.app/trace?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'] : 'https://tracking-test.quadminds.com/trace?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'];
    $mobileLink = getTntMobileLink($buttonLink);

    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    $semanticDate = getSemanticDate($date, $order['locale']);

    $notification_operation_type = get_parametro_usuario($order['id_usuario'], 'TNT_NOTIFICATION_OPERATION_TYPE');

    $notification_operation_type_secure = $notification_operation_type ? $notification_operation_type : 'request';

    $translations = $order['operacion'] == 1 ? getTranslations('mail-2', $notification_operation_type_secure, $order['locale']) : getTranslations('mail-retiro-2', 'request', $order['locale']);
    $message_type_by_gender = get_message_type_by_gender();

    $title = $order['operacion'] == 1 ? _("🚚 Tu {$message_type_by_gender[$notification_operation_type_secure]['label']} {$order['codigo_pedido']} de " . ($displayName ? $displayName : $order['orgName']) . " está en camino")  :  _("🚚 El conductor de {$order['orgName']} está en camino");

    $data = array(
        'logo' => $logoOrg,
        'items' => $order['items'],
        'link' => $buttonLink,
        'mobileLink' => $mobileLink,
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'qtn' => $order['qtn'],
        'date' => $semanticDate,
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'time' => $timeFrame,
        'key' => $order['user_key'],
        'texts' => $translations,
        'eta' => true,
        'contact' => $contactInfo,
        'title' => $title
    );

    loggear("[TNT][" . strtoupper(OrderEventTypes::route_started) . "] Order Id: " . $order['id'] . "Payload: " . json_encode($data), 'TNT');

    if ($displayName) {
        $data['orgName'] = $displayName;
    };

    if ($eta_disabled == 1)
        unset($data['eta']);

    $templateId = assignSendGridEmailTemplate("inTransitEmail", $order['operacion']);

    sendMailTrackAndTraceSendGrid(
        $title,
        $email,
        $data,
        $order['id_usuario'],
        false,
        'tnt-route-started',
        $templateId
    );
}

function generateIsCloseEmail($order)
{
    if (!checkTrackingNumber($order)) return;

    // Returns if not enabled by config param
    $send_enabled = get_parametro_usuario($order['id_usuario'], 'TNT_IS_CLOSE_STATE_ENABLED');
    if ($send_enabled === '0') return;

    $send_enabled = get_parametro_usuario($order['id_usuario'], 'TNT_IS_CLOSE_STATE_ENABLED');
    $contactInfo = getContactInfo($order['id_usuario']);

    $eta_disabled = get_parametro_usuario($order['id_usuario'], 'TNT_IN_ROUTE_STATE_ETA_DISABLED');

    $email = getDestinationEmail($order);

    $buttonLink = isProduction() ? 'https://tracking.qmt.app/trace?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'] : 'https://tracking-test.quadminds.com/trace?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'];
    $mobileLink = getTntMobileLink($buttonLink);

    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    //TimeZone
    $estimatedDateFromDB = $order['fecha_programada_llegada'];
    $userTimeZone = getTZUsuarioById($order['id_usuario']);
    $tnt_margin = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN');
    $displayName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');
    $tnt_margin2 = (int)get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_MARGIN_2');
    $rawTimeWindow = (!empty($order['order_time_window']) && $order['order_time_window'] !== 'FFFFFFFFFFFF') ? $order['order_time_window'] : $order['poi_time_window'];
    $timeWindow = parseTimeWindow($rawTimeWindow);

    $timeFrame = calculateTimeFrame($estimatedDateFromDB, $userTimeZone, $tnt_margin, $tnt_margin2, $timeWindow);

    $date = date_create_from_format('Y-m-d', $order['fecha'], new DateTimeZone($userTimeZone));

    $semanticDate = getSemanticDate($date, $order['locale']);

    $notification_operation_type = get_parametro_usuario($order['id_usuario'], 'TNT_NOTIFICATION_OPERATION_TYPE');

    $notification_operation_type_secure = $notification_operation_type ? $notification_operation_type : 'request';

    $translations = $order['operacion'] == 1 ? getTranslations('mail-3', $notification_operation_type_secure, $order['locale']) : getTranslations('mail-retiro-3', 'request', $order['locale']);
    $message_type_by_gender = get_message_type_by_gender();

    $title = $order['operacion'] == 1 ? _("💥 ¡Tu {$message_type_by_gender[$notification_operation_type_secure]['label']} {$order['codigo_pedido']} de " . ($displayName ? $displayName : $order['orgName']) . "está cerca!") :  _("💥 El conductor de " . ($displayName ? $displayName : $order['orgName']) . " está cerca");

    $data = array(
        'logo' => $logoOrg,
        'items' => $order['items'],
        'link' => $buttonLink,
        'mobileLink' => $mobileLink,
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'qtn' => $order['qtn'],
        'date' => $semanticDate,
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'key' => $order['user_key'],
        'time' => $timeFrame,
        'texts' => $translations,
        'eta' => $eta_disabled,
        'contact' => $contactInfo,
        'title' => $title
    );

    loggear("[TNT][" . strtoupper(OrderEventTypes::arriving) . "] Order Id: " . $order['id'] . "Payload: " . json_encode($data), 'TNT');

    if ($displayName) {
        $data['orgName'] = $displayName;
    };

    if ($eta_disabled == 1)
        unset($data['eta']);

    $templateId = assignSendGridEmailTemplate("isCloseEmail", $order['operacion']);

    sendMailTrackAndTraceSendGrid(
        $title,
        $email,
        $data,
        $order['id_usuario'],
        false,
        'tnt-arriving',
        $templateId
    );
}

function generateDeliveredEmail($order, $force = false)
{
    if (!checkTrackingNumber($order)) return;

    // Returns if not enabled by config param
    $send_enabled = get_parametro_usuario($order['id_usuario'], 'TNT_DELIVERED_STATE_ENABLED');

    if ($send_enabled === '0' && !$force) return;

    $flash = get_parametro_usuario($order['id_usuario'], 'QUADMINDS_FLASH');
    $contactInfo = getContactInfo($order['id_usuario']);


    $email = getDestinationEmail($order);

    $displayName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    //DeliveredData
    $depositExitDateFromDB = $order['fecha_salida_deposito'];
    $orderTimeZone = $order['timezone'];
    $orderStateDelivered = $order['states'][4];

    $deliveredData = calculateDeliveredData($depositExitDateFromDB, $orderTimeZone, $orderStateDelivered);

    $estado = $order['id_subestado'] ? $order['descripcion_estado'] . " - " . $order['descripcion_subestado'] : $order['descripcion_estado'];

    $buttonLink = isProduction() ? 'https://tracking.qmt.app/feedback?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'] : 'https://tracking-test.quadminds.com/feedback?qtn=' . $order['qtn'] . '&organization=' . $order['user_key'];

    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    $semanticDate = getSemanticDate($deliveredData['deliveredDate'], $order['locale']);

    $notification_operation_type = get_parametro_usuario($order['id_usuario'], 'TNT_NOTIFICATION_OPERATION_TYPE');

    $notification_operation_type_secure = $notification_operation_type ? $notification_operation_type : 'request';

    $translations = $order['operacion'] == 1 ? getTranslations('mail-4', $notification_operation_type_secure, $order['locale']) : getTranslations('mail-retiro-4', 'request', $order['locale']);
    $message_type_by_gender = get_message_type_by_gender();

    $resolved_gender = $message_type_by_gender[$notification_operation_type_secure]['gender'] == 'M' ? _('realizado') : _('realizada');

    $resolved = $notification_operation_type_secure == 'request' ? _('entregado') : $resolved_gender;

    $title = $order['operacion'] == 1 ? _("📦 Tu {$message_type_by_gender[$notification_operation_type_secure]['label']} {$order['codigo_pedido']} de " . ($displayName ? $displayName : $order['orgName']) . " fue {$resolved}") :  _("📦 Tu solicitud de retiro de " . ($displayName ? $displayName : $order['orgName']) . " fue realizada");

    $data = array(
        'logo' => $logoOrg,
        'items' => $order['items'],
        'link' => $buttonLink,
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'qtn' => $order['qtn'],
        'date' => $semanticDate,
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'deliveryTime' => $deliveredData['deliveredTime'],
        'state' => $estado,
        'key' => $order['user_key'],
        'texts' => $translations,
        'score' => true,
        'contact' => $contactInfo,
        'title' => $title
    );

    loggear("[TNT][" . strtoupper(OrderEventTypes::delivered) . "] Order Id: " . $order['id'] . "Payload: " . json_encode($data), 'TNT');

    if ($displayName) {
        $data['orgName'] = $displayName;
    };

    if ($order['latitud'] && $order['longitud']) {
        $data['map'] = array('center' => $order['latitud'] . "," . $order['longitud']);
    }

    if ($flash)
        unset($data['eta']);


    $templateId = assignSendGridEmailTemplate("fulfilledEmail", $order['operacion']);

    sendMailTrackAndTraceSendGrid(
        $title,
        $email,
        $data,
        $order['id_usuario'],
        false,
        'tnt-delivered',
        $templateId
    );
}

function generate_feedback_email($id_pedido)
{
    $order = getOrderById($id_pedido);

    checkTrackingNumber($order);

    if (!$order['hasFeedback']) {
        loggear("No Feedback: " . json_encode($order) . "-ID:$id_pedido", 'TNT');
        return;
    }

    $displayName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $feedback_questions = getFeedbackQuestions($id_pedido);
    $feedback = mysql_fetch_assoc(mysql_query("SELECT * FROM pedidos_feedback WHERE id_pedido = $id_pedido"));

    $flash = get_parametro_usuario($order['id_usuario'], 'QUADMINDS_FLASH');

    $m = new Mustache_Engine;
    $template = file_get_contents("/var/www/saas/track_and_trace/partials/mail-notify-feedback.mustache");
    $email = getDestinationEmail($order);
    $destination = get_parametro_usuario($order['id_usuario'], UsuariosParametro::TRACK_AND_TRACE_EMAIL_ORIGIN) ?: false;
    if (!$destination)
        return;

    loggear("email de envio: $destination", 'TNT');

    if (empty($order['timezone']))
        $order['timezone'] = 'America/Buenos_Aires';
    $ahora = new DateTime('now', new DateTimeZone($order['timezone']));

    $estado = $order['id_subestado'] ? $order['descripcion_estado'] . " - " . $order['descripcion_subestado'] : $order['descripcion_estado'];

    if (!$order['fecha_salida_deposito'])
        $fecha_salida = $ahora->format('d/m/Y');
    else {
        $date = new DateTime($order['fecha_salida_deposito'], new DateTimeZone('America/Buenos_Aires'));
        $date->setTimezone(new DateTimeZone($order['timezone']));
        $fecha_salida = $date->format('d/m/Y H:i');
    }


    if (!$order['states'][3])
        $fecha_visita = $ahora->format('d/m/Y');
    else {
        $date = new DateTime($order['states'][3]['timestamp'], new DateTimeZone('America/Buenos_Aires'));
        $date->setTimezone(new DateTimeZone($order['timezone']));
        $fecha_visita = $date->format('d/m/Y H:i');
    }

    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    $data = array(
        'logo' => $logoOrg,
        'items' => $order['items'],
        'from' => $order['razon_social'],
        'fromEmail' => $email,
        'order' => $order['codigo_pedido'],
        'qtn' => $order['qtn'],
        'date' => date('d/m/Y H:i', strtotime($order['fecha_entregado'])),
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'key' => $order['user_key'],
        'texts' => getTranslations('mail-feedback', 'request', $order['locale']),
        'evaluationScores' => [
            'general' => $feedback['general_feedback'],
            'text1' => $feedback['feedback_1'],
            'text2' => $feedback['feedback_2'],
            'text3' => $feedback['feedback_3']
        ],
        'comments' => $feedback['comments'],
        'generalEvaluationText' => _($feedback_questions['general_feedback']),
        'evaluation1Text' => _($feedback_questions['feedback_1']),
        'evaluation2Text' => _($feedback_questions['feedback_2']),
        'evaluation3Text' => _($feedback_questions['feedback_3'])
    );

    if ($displayName) {
        $data['orgName'] = $displayName;
    };


    if ($order['latitud'] && $order['longitud']) {
        $data['map'] = array('center' => $order['latitud'] . "," . $order['longitud']);
    }

    if ($flash)
        unset($data['eta']);

    $render = $m->render($template, $data);

    sendMailTrackAndTraceSendGrid(
        _("✨ ¡Nueva valoración de tu servicio! ✨"),
        $destination,
        $render,
        $order['id_usuario'],
        'Quadminds Notifications',
        'tnt-feedback'
    );
}

function generateNotFulfilledEmail($order, $rejectedType)
{
    if (!checkTrackingNumber($order)) return;

    //Send mail or not
    $sendEnabled = get_parametro_usuario($order['id_usuario'], 'TNT_' . $rejectedType . '_REJECTED_STATE_ENABLED');

    if ($sendEnabled === '0') {
        loggear("[REJECT EMAIL] User does not have config enabled to send rejected emails. User id:  " . $order["id_usuario"], 'TNT');
        return;
    }

    if ($rejectedType === "PARTIAL") return generateDeliveredEmail($order, true);

    $contactInfo = getContactInfo($order['id_usuario']);

    //TimeZone
    $userTimeZone = getTZUsuarioById($order['id_usuario']);

    $date = date_create_from_format('Y-m-d H:i:s', $order['fecha_entregado'], new DateTimeZone($userTimeZone));

    $email = getDestinationEmail($order);

    $logoOrg = BASE_URL . 'img/' . $order['url_logo'];

    $semanticDate = getSemanticDate($date, $order['locale']);

    $reason = isset($order['descripcion_subestado']) ? $order['descripcion_subestado'] : $order['descripcion_estado'];

    $displayName = get_parametro_usuario($order['id_usuario'], 'TNT_ORG_NAME');

    $notification_operation_type = get_parametro_usuario($order['id_usuario'], 'TNT_NOTIFICATION_OPERATION_TYPE');

    $notification_operation_type_secure = $notification_operation_type ? $notification_operation_type : 'request';

    $translations = $order['operacion'] == 1 ? getTranslations('mail-5-NO-entrega', $notification_operation_type_secure, $order['locale']) : getTranslations('mail-5-retiro-NO-entrega', 'request', $order['locale']);
    $message_type_by_gender = get_message_type_by_gender();

    $resolved = $notification_operation_type_secure == 'request' ? _('entregar') : _('realizar');

    $title = $order['operacion'] == 1 ? _("🙁 No pudimos {$resolved} tu {$message_type_by_gender[$notification_operation_type_secure]['label']} {$order['codigo_pedido']} de " . ($displayName ? $displayName : $order['orgName']) . "") :  _("🙁 Tu solicitud de retiro {$order['codigo_pedido']} de " . ($displayName ? $displayName : $order['orgName']) . " no pudo ser completada");

    $data = array(
        'logo' => $logoOrg,
        'items' => $order['items'],
        'reason' => $reason,
        'orgName' => $order['orgName'],
        'customerName' => $order['razon_social'],
        'orderNumber' => $order['codigo_pedido'],
        'date' => $semanticDate,
        'address' => $order['domicilio'],
        'env' => BASE_URL,
        'texts' => $translations,
        'contact' => $contactInfo,
        'title' => $title
    );

    loggear("[TNT][" . strtoupper($rejectedType) . "-REJECTION] Order Id: " . $order['id'] . "Payload: " . json_encode($data), 'TNT');

    if ($displayName) {
        $data['orgName'] = $displayName;
    };

    $templateId = assignSendGridEmailTemplate("notFulfilledEmail", $order['operacion']);

    sendMailTrackAndTraceSendGrid(
        $title,
        $email,
        $data,
        $order['id_usuario'],
        false,
        'tnt-not-fulfilled',
        $templateId
    );
}

// ---------------------------------------------------- Emails ----------------------------------------------------

function saveFeedback($data)
{
    if (!$data["user_ids"]) {
        loggear(
            "[Saving Feedback] [Error] No user ids supplied to order with QTN: " . $data['qtn'],
            "TNT"
        );
    }

    $order = getOrderByQTN($data['qtn'], $data["user_ids"]);

    if (!isset($order['id_pedido'])) {
        loggear(
            "[Saving Feedback] [Error] No order was found with the following QTN: " . $data['qtn'],
            "TNT"
        );
    }

    $orderId = $order['id_pedido'];

    $query = "INSERT INTO pedidos_feedback (id_pedido, general_feedback, feedback_1, feedback_2, feedback_3, comments, timestamp)
                            VALUES ('$orderId','$data[general_feedback]','$data[feedback_1]','$data[feedback_2]','$data[feedback_3]','$data[comments]', NOW())
                             ON DUPLICATE KEY UPDATE
                                general_feedback = '$data[general_feedback]',
                                feedback_1 = '$data[feedback_1]',
                                feedback_2 = '$data[feedback_2]',
                                feedback_3 = '$data[feedback_3]',
                                comments = '$data[comments]',
                                timestamp = NOW()";

    $result = mysql_query($query);

    if ($result && mysql_affected_rows() > 0) {
        generate_feedback_email($orderId);
    } else {
        loggear(
            "[Saving Feedback] [Error] There was an error saving feedback. Order QTN: " . $data['qtn'],
            "TNT"
        );
    }

    return $result;
}

function sendMailTrackAndTrace($titulo, $destinatario, $mensaje, $id_usuario)
{
    if ($destinatario == "" || is_null($destinatario)) {
        $destinatario = "<EMAIL>";
    }
    // take $destinatario as an array
    $destinatario = explode(";", $destinatario);

    $emailOrigin = get_parametro_usuario(
        $id_usuario,
        UsuariosParametro::TRACK_AND_TRACE_EMAIL_ORIGIN
    ) ?: '<EMAIL>';

    $titulo_header = get_parametro_usuario(
        $id_usuario,
        UsuariosParametro::EMAIL_TITLE_HEADER
    ) ?: null;

    foreach ($destinatario as $item) {
        if (!Mail::send(
            $emailOrigin,
            $item,
            $titulo,
            $mensaje,
            [
                'title_header' => $titulo_header
            ]
        )) {
            loggear(
                json_encode([
                    'origen'  => $emailOrigin,
                    'destino' => $item,
                    'titulo'  => $titulo,
                    'mensaje' => $mensaje,
                    'titulo_header' => $titulo_header
                ]),
                'TNT'
            );
        }
    }
}
//busqueda masiva en sendMailTrackAndTraceSendGrid
function sendMailTrackAndTraceSendGrid($subject, $destination, $data, $id_usuario, $title = false, $category = false, $templateId = "")
{

    if (!isProduction()) {
        loggear("Redirect de $<NAME_EMAIL>", 'TNT');
        $destination = '<EMAIL>';
    }

    if ($destination == "" || is_null($destination)) {
        loggear('Notificacion sin destinatario: ' . $subject . " $data", 'TNT');
        return;
    }

    // Fix para algunos clientes que tienen muchas direcciones separadas por ;. Se Depreca el uso de separador.
    $destination = explode(";", $destination)[0];

    $emailOrigin = get_parametro_usuario($id_usuario, UsuariosParametro::TRACK_AND_TRACE_EMAIL_ORIGIN) ?: '<EMAIL>';

    if (!$title)
        $email_title = get_parametro_usuario(
            $id_usuario,
            UsuariosParametro::EMAIL_TITLE_HEADER
        ) ?: "Quadminds Flash";
    else
        $email_title = $title;

    loggear(
        json_encode([
            'origen'  => $emailOrigin,
            'destino' => $destination,
            'titulo'  => $subject,
            'mensaje' => $data,
            'titulo_header' => $email_title,
            'categoria' => $category
        ]),
        'sendgrid_flash'
    );

    try {
        $email = new \SendGrid\Mail\Mail();
        $email->setFrom('<EMAIL>', $email_title);
        $email->setReplyTo($emailOrigin, $email_title);
        $email->setSubject($subject);
        $email->addTo(trim($destination));
        if ($templateId == "") {
            loggear("Using Mustache Template Engine", 'sendgrid_flash');
            $staticTemplate = $data;
            $email->addContent("text/html", $staticTemplate);
        } else {
            loggear("Using Sendgrid dynamic template ID: $templateId", 'sendgrid_flash');
            $email->addDynamicTemplateDatas($data);
            $email->setTemplateId($templateId);
        }
        //Categoria del email
        if ($category) $email->addCategory($category);

        $apikey = getenv('SENDGRID_API_KEY');
        $sendgrid = new \SendGrid($apikey);
        $response = $sendgrid->send($email);
        loggear(
            "Sendgrid | email: $destination - titulo: $subject - StatusCode: " .
                $response->statusCode() . " " .
                json_encode($response->headers()) . " " .
                $response->body(),
            'sendgrid_flash'
        );
    } catch (Exception $e) {
        loggear("Sendgrid -Caught exception: " . $e->getMessage(), 'sendgrid_flash');
    }
}

/**
 * Returns redirect email if user has config, otherwise returns poi's email
 * @param object $order
 * @return string|boolean
 */
function getDestinationEmail($order)
{
    $result = get_parametro_usuario($order['id_usuario'], 'TRACK_AND_TRACE_EMAIL_REDIRECT');
    if ($result)
        return $result;
    else if ($order['email'])
        return $order['email'];
    else
        return false;
}

/**
 * Recibe un string determinando el mail template y retorna un array con las palabras/frases requeridas
 * @param $mailNumber
 */

function getTranslations($mailNumber, $mailType = 'request', $locale_tnt = 'es_AR.UTF-8')
{

    set_locale_tnt($locale_tnt);
    $message_type_by_gender = get_message_type_by_gender();

    $translations = [
        'Preparacion' => _('PREPARACIÓN'),
        'EnCamino' => _('EN CAMINO'),
        'Entregado' => _('ENTREGADO'),
        'hola' => _('Hola'),
        'tu' => _('Tu'),
        'pedido' => $message_type_by_gender[$mailType]['label'],
        'informacionDeSeguimientoBrindadaPor' => _('Información de seguimiento brindada por'),
        'esteCorreo' => _('Este correo ha sido enviado desde una dirección que no acepta correo entrante, por favor no responda este mensaje')
    ];
    $gender = $message_type_by_gender[$mailType]['gender'];
    $msg_operation = $message_type_by_gender[$mailType]['translation'];
    switch ($mailNumber) {
        case 'mail-1':
            $resolved_gender = $gender == 'M' ? _('está confirmado.') : _('está confirmada.');
            $translations += [
                'tuPedido' => _($msg_operation),
                'de' => _('de'),
                'programado' => $resolved_gender,
                'fecha' => _('Fecha'),
                'llegara' => _('Llegará'),
                'el' => _('el'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle'),
                'seguirEnvio' => _('Seguir'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-retiro-1':
            $translations += [
                'tuSolicitud' => _('Tu solicitud de retiro de'),
                'de' => _('de'),
                'asignado' => _('está asignada.'),
                'fecha' => _('Fecha'),
                'llegara' => _('Se realizará'),
                'el' => _('el'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle del retiro'),
                'seguirEnvio' => _('Seguir'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-2':
            $translations += [
                'tuPedido' => _($msg_operation),
                'de' => _('de'),
                'enCamino' => _('está en camino.'),
                'fecha' => _('Fecha'),
                'llegara' => _('Llegará'),
                'el' => _('el'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle'),
                'seguirEnvio' => _('Seguir'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-retiro-2':
            $translations += [
                'conductor' => _('El conductor'),
                'de' => _('de'),
                'solicitud' => _('con la solicitud de retiro'),
                'de' => _('de'),
                'enCamino' => _('está en camino.'),
                'fecha' => _('Fecha'),
                'llegara' => _('Se realizará'),
                'el' => _('el'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle del retiro'),
                'seguirEnvio' => _('Seguir'),

                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-merchant-2':
            $translations += [
                'tuPedido' => _('Tu pedido'),
                'para' => _('para'),
                'esta' => _('está'),
                'enCamino' => _('en camino'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'datosDelEnvio' => _('DATOS DEL ENVÍO:'),
                'destino' => _('Destino'),
                'telefono' => _('Teléfono:'),
                'email' => _('E-mail:'),
                'entregaEstimada' => _('Entrega estimada')
            ];
            break;
        case 'mail-3':
            $translations += [
                'tuPedido' => _($msg_operation),
                'de' => _('de'),
                'cerca' => _('está cerca ¡Preparate!'),
                'fecha' => _('Fecha'),
                'llegara' => _('Llegará'),
                'el' => _('el'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle'),
                'seguirEnvioVivo' => _('Seguir en vivo'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-retiro-3':
            $translations += [
                'conductor' => _('El conductor'),
                'de' => _('de'),
                'solicitud' => _('con la solicitud de retiro'),
                'de' => _('de'),
                'cerca' => _('está cerca ¡Preparate!'),
                'fecha' => _('Fecha'),
                'llegara' => _('Se realizará'),
                'el' => _('el'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle del retiro'),
                'seguirEnvioVivo' => _('Seguir en vivo'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-merchant-3-entrega':
            $translations += [
                'tuPedido' => _('Tu pedido'),
                'para' => _('para'),
                'esta' => _('está'),
                'haSido' => _('ha sido'),
                'entregadoExito' => _('entregado con éxito!'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'epodLink' => _('Descargar constancia'),
                'datosDelEnvio' => _('DATOS DEL ENVÍO:'),
                'entregado' => _('Entregado el'),
                'destino' => _('Destino'),
                'telefono' => _('Teléfono:'),
                'email' => _('E-mail:'),
            ];
            break;
        case 'mail-4':
            $resolved_gender = ($gender === 'M') ? _('fue realizado.') : _('fue realizada.');
            $resolved_delivered = $mailType === 'request' ? _('fue entregado ¡Que lo disfrutes!') : $resolved_gender;
            $translations += [
                'tuPedido' => _($msg_operation),
                'de' => _('de'),
                'entregado' => $resolved_delivered,
                'feedback' => _('FEEDBACK:'),
                'comoEvaluariaSuSatisfaccion' => _('¿Cómo valoras el servicio recibido?'),
                'fecha' => _('Fecha'),
                'llegara' => _('Llegará'),
                'el' => _('el'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-retiro-4':
            $translations += [
                'tuSolicitud' => _('Tu solicitud de retiro de'),
                'de' => _('de'),
                'realizada' => _('fue realizada.'),
                'feedback' => _('FEEDBACK:'),
                'comoEvaluariaSuSatisfaccion' => _('¿Cómo valoras el servicio recibido?'),
                'fecha' => _('Fecha'),
                'direccionEntrega' => _('Dirección de retiro'),
                'detallePedido' => _('Detalle del retiro'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-merchant-4-NO-entrega':
            $translations += [
                'noHemosPodidoEntregar' => _('No hemos podido entregar'),
                'tuPedido' => _('tu pedido'),
                'para' => _('para'),
                'codigoSeguimiento' => _('Código de seguimiento'),
                'epodLink' => _('Descargar constancia'),
                'datosDelEnvio' => _('DATOS DEL ENVÍO:'),
                'entregado' => _('Entregado el'),
                'destino' => _('Destino'),
                'telefono' => _('Teléfono:'),
                'email' => _('E-mail:'),
            ];
            break;
        case 'mail-5-NO-entrega':
            $resolved_gender = ($gender === 'M') ? _('no pudo ser realizado.') : _('no pudo ser realizada.');
            $resolved_not_delivered = ($mailType === 'request') ? _('no pudo ser entregado.') : $resolved_gender;
            $translations += [
                'tuPedido' => _($msg_operation),
                'de' => _('de'),
                'noEntregado' => $resolved_not_delivered,
                'motivo' => _('Motivo:'),
                'fecha' => _('Fecha'),
                'direccionEntrega' => _('Dirección'),
                'detallePedido' => _('Detalle'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-5-retiro-NO-entrega':
            $translations += [
                'tuSolicitud' => _('Tu solicitud de retiro'),
                'de' => _('de'),
                'noRealizada' => _('no pudo ser realizada.'),
                'motivo' => _('Motivo:'),
                'fecha' => _('Fecha'),
                'direccionEntrega' => _('Dirección de entrega'),
                'detallePedido' => _('Detalle de pedido'),
                'inconveniente' => _('En caso de tener algún inconveniente contactate con nosotros:')
            ];
            break;
        case 'mail-feedback':
            $translations += [
                'preTitle' => _('Feedback recibido de: '),
                'about' => _('Sobre el pedido '),
                'delivered' => _('entregado el '),
                'at' => _(', en '),
                'comments' => _('Comentario '),
                'goToFeedback' => _('Te recomendamos ingresar a la plataforma para '),
                'goToFeedbackButton' => _('Ver todo el feedback')

            ];
            break;
    }
    return $translations;
}

function getTZUsuarioById($id_usuario)
{
    $tz_usuario = mysql_result(mysql_query("SELECT timezone FROM usuarios WHERE id = $id_usuario"), 0, 0);

    if (!$tz_usuario || $tz_usuario == '')
        return 'America/Buenos_Aires';

    return $tz_usuario;
}

/**
 * Processes tempate string and replace variables with order data values
 * @param string $template. ie: "Hola {{nombre}}! tu pedido #{{codigo}}..."
 * @param array $orderData. ie: ["nombre" => "Juan", ...]
 * @return string ie: "Hola Juan! ..."
 */
function buildSmsMessage($template, $orderData)
{
    try {
        $variables = getNotificationVariables($orderData);
        $mustache = new Mustache_Engine();
        return $mustache->render($template, $variables);
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Generates sms notifications and publish them to pub sub
 * @param string $orderId
 * @param string $state
 * @return void
 */
function generateSmsNotification($orderData, $state)
{

    $rutaPhp = "/var/www/saas/services/SaasPubSub/dispatch_tnt_sms_pubsub.php";

    $od = escapeshellarg(strval($orderData['id_pedido']));
    $st = escapeshellarg($state);

    // $command = "cd /var/www/saas/services/SaasPubSub && php $rutaPhp $od $st > /dev/null 2>&1 &";
    // TODO: remove to avoid file size increase
    $command = "cd /var/www/saas/services/SaasPubSub && php $rutaPhp $od $st >> /tmp/exec_pubsub_notificar_evento.log 2>&1 &";

    loggear($command, "exec_pubsub_notificar_evento");
    exec($command);
}

function getNotificationVariables($orderData)
{
    $fecha = date('d/m/Y', strtotime($orderData['fecha']));

    $variables = array(
        "order" => array(
            "code" => $orderData['codigo_pedido'],
            "delivery" => array(
                "date" => $fecha
            )
        ),
        "customer" => array(
            "name" => $orderData['razon_social'],
            "address" => $orderData['domicilio']
        ),
        "tracking" => array("url" => $orderData['link']),
        "company" => array("name" => $orderData['orgName'])
    );
    return $variables;
}

/**
 * Returns overall status
 * @param int $id ce_estado
 * @return array|bool $overallStatus [id, description, status]
 */
function getOverallStatus($id)
{
    $query = "SELECT id, afirmativo as `status`, descripcion as `description` FROM ce_estados WHERE id = $id";

    $result = mysql_query($query);

    if (!$result) return [];

    if (mysql_num_rows($result) == 0) return [];

    $overallStatus = [];
    $overallStatus = mysql_fetch_assoc($result);
    return $overallStatus;
}

/**
 * Returns detailed sub status
 * @param int $subStatusId ce_subestado
 * @return array|bool [id, description]
 */
function getDetailedStatus($subStatusId)
{
    $query = "SELECT id, descripcion as `description` FROM ce_subestados WHERE id = $subStatusId";

    $result = mysql_query($query);

    if (!$result) return [];

    if (mysql_num_rows($result) == 0) return [];

    $subStatus = [];
    $subStatus = mysql_fetch_assoc($result);
    return $subStatus;
}

/**
 * Returns both overall and detailed order status.
 * @param int $statusId ce_estado id
 * @param int $subStatusId ce_subestado id
 * @return array [overallStatus, detailedStatus]
 */
function getOrderStatus($statusId, $subStatusId)
{
    if (!isset($statusId)) return [];

    return [
        'overallStatus' => getOverallStatus($statusId),
        'detailedStatus' => getDetailedStatus($subStatusId)
    ];
}

/**
 * Generates webhook notifications and publish them to pub sub
 * @param string $orderId
 * @param string $state
 * @return void
 */
function generateWebhookNotification($orderData, $state)
{
    if (!$orderData)
        return;

    // Imports external publisher
    require_once '/var/www/saas/services/SaasPubSub/src/EventsPublisher.php';
    $eventPublisher = new \SaasPubSub\EventsPublisher();

    $payload = array();

    $exposedOrderData = buildOrderDataToBeExposed($orderData, $state);

    $payload = ["order" => $exposedOrderData];

    switch ($state) {
        case OrderEventTypes::planned:
            $eventPublisher->publishOrderPlanned($payload, $orderData['poi_id_usuario'], 'webhook');
            break;
        case OrderEventTypes::route_started:
            $eventPublisher->publishOrderRouteStarted($payload, $orderData['poi_id_usuario'], 'webhook');
            break;
        case OrderEventTypes::arriving:
            $eventPublisher->publishOrderArriving($payload, $orderData['poi_id_usuario'], 'webhook');
            break;
        case OrderEventTypes::delivered:
            $eventPublisher->publishOrderDelivered($payload, $orderData['poi_id_usuario'], 'webhook');
            break;
        case OrderEventTypes::failed:
            $eventPublisher->publishOrderFailed($payload, $orderData['poi_id_usuario'], 'webhook');
            break;
        default:
            return;
    }
    return;
}

/**
 * @param int|string $orderId
 * @return bool
 */
function checkIfExistsInEventService($orderId)
{
    $userId = getOrderUserId($orderId);
    $existsInEventService = \UsuariosParametro::getValorByUsuarioAndParametro($userId, \UsuariosParametro::EXISTS_IN_EVENT_SERVICE);
    return boolval($existsInEventService);
}



/**
 * This helper function is to avoid expensive query joins
 * @param int|string $orderId
 * @return string|false
 */
function getOrderUserId($orderId)
{
    $query = "SELECT p.id_usuario
                    FROM pedidos p
                  WHERE p.id = $orderId";

    $result = mysql_query($query);

    if (!$result) return false;

    if (mysql_num_rows($result) == 0) return false;

    if (mysql_num_rows($result) > 0) {
        $order = mysql_fetch_assoc($result);
        return $order['id_usuario'];
    }
    return false;
}

/**
 * Assign semantic operation
 * @param object $orderData query result
 * @return object
 */
function assignSemanticStatus($state)
{
    // Switch between different order states
    switch ($state) {
        case 1:
            $status = "preparing";
            break;
        case 2:
            $status = "inTransit";
            break;
        case 3:
            $status = "arriving";
            break;
        case 4:
            $status = "delivered";
            break;
        default:
            $status = "preparing";
    }
    return $status;
}

/**
 * Checks if order has a tracking number assigned.
 * @param object $order
 * @return bool
 */
function checkTrackingNumber($order)
{
    if (!$order['qtn']) {
        loggear("Order does not have a tracking number assigned. Order ID: " . $order["id"], 'TNT');
        return false;
    }

    return true;
}

/**
 * Returns track and trace contact info
 * @param int|string $userId
 * @return array
 */
function getContactInfo($userId)
{
    return [
        "email" => get_parametro_usuario($userId, UsuariosParametro::TRACK_AND_TRACE_EMAIL_ORIGIN),
        "phone" => get_parametro_usuario($userId, UsuariosParametro::TRACK_AND_TRACE_CONTACT_PHONE),
    ];
}

/**
 * Dispatch push notification to Push Service
 * @param array $orderData
 * @param OrderEventTypes $event
 */
function generatePushNotification($orderData, $event = "not-specified")
{
    switch ($event) {
        case OrderEventTypes::planned:
            $title = _("Tu pedido {$orderData['qtn']} ahora se esta preparando");
            $message = _("Tu pedido se esta preparando.
                    Puedes ver el estado en el link de seguimiento {$orderData['link']}");
            break;
        case OrderEventTypes::route_started:
            $title = _("Tu pedido {$orderData['qtn']} esta en camino");
            $message = _("Ya el remitente tiene tu pedido y esta en camino 🚚.
                    Puedes ver el estado en el link de seguimiento {$orderData['link']}");
            break;
        case OrderEventTypes::arriving:
            $title = _("¡Tu pedido {$orderData['qtn']} esta llegando!");
            $message = _("El conductor esta cerca, preparate para recibir tu pedido 📦");
            break;
        case OrderEventTypes::delivered:
            $title = _("Tu pedido fue entregado");
            $message = _("Si necesitas ayuda, comunícate con el proveedor desde el siguiente link {$orderData['link']}");
            break;
        case OrderEventTypes::failed:
            $title = _("No pudimos entregar tu pedido");
            $message = _("Si necesitas ayuda, comunícate con el proveedor desde el siguiente link {$orderData['link']}");
            break;
        default:
            $title = _("Tu pedido {$orderData['qtn']} ahora se esta preparando");
            $message = _("Tu pedido se esta preparando.
                    Puedes ver el estado en el link de seguimiento {$orderData['link']}");
            break;
    }

    $consumerApiKey = isProduction() ? CONSUMER_API_KEY : TEST_CONSUMER_API_KEY;
    // Dispatch push notifications
    sendPushNotificationToTopic($consumerApiKey, $orderData['qtn'], $message, $title);
}

/**
 * Returns order data to be exposed.
 * TODO this kind of DTO contract should be in its own class
 * @param array $orderData
 * @return array $exposedOrderData
 */
function buildOrderDataToBeExposed($orderData, $event = "not-specified")
{
    // Datetimes are both exposed with user's timezone and UTC
    $userTimeZone = getTZUsuarioById($orderData['id_usuario']);
    // Delivery date
    $deliveryDate = DateTime::createFromFormat('Y-m-d', $orderData['fecha'], new DateTimeZone($userTimeZone));
    $deliveryDateUTC = DateTime::createFromFormat('Y-m-d', $orderData['fecha'], new DateTimeZone("UTC"));
    // Etas
    if ($orderData["fecha_programada_llegada"] && $orderData["fecha_programada_llegada"] !== '0000-00-00 00:00:00') {
        $eta = DateTime::createFromFormat('Y-m-d H:i:s', $orderData["fecha_programada_llegada"], new DateTimeZone("America/Buenos_Aires"));
        $eta->setTimezone(new DateTimeZone($userTimeZone));
        $etaUTC = DateTime::createFromFormat('Y-m-d H:i:s', $orderData["fecha_programada_llegada"], new DateTimeZone("UTC"));
    }


    $orderTobeExposed = [
        "event" => $event,
        "deliveryDate" => $deliveryDate->format('Y-m-d'),
        "deliveryDateUTC" => $deliveryDateUTC->format('Y-m-d'),
        "code" => $orderData["codigo_pedido"],
        "qtn" => $orderData["qtn"],
        "trackingLink" => $orderData["link"],
        "operation" => $orderData["operacion"],
        "states" => $orderData["states"],
        "deliveryState" => [
            "description" => $orderData["descripcion_estado"],
            "reason" => $orderData["descripcion_subestado"]
        ],
        "eta" => isset($eta) ? $eta->format('Y-m-d H:i:s') : null,
        "etaUTC" => isset($etaUTC) ? $etaUTC->format('Y-m-d H:i:s') : null,
        "order" => [
            "id" => $orderData['id'],
            "code" => $orderData["codigo_pedido"],
            "qtn" => $orderData["qtn"],
        ],
        "poi" => [
            "id" => $orderData["id_cliente"],
            "code" => $orderData["codigo_cliente"],
            "name" => $orderData["razon_social"],
            "address" => $orderData["domicilio"],
            "lat" => $orderData["latitud"],
            "lng" => $orderData["longitud"],
            "email" => $orderData["email"],
            "phone" => $orderData["telefono_contacto"],
        ],
        "route" => [
            "id" => $orderData["routeId"],
            "code" => $orderData["routeCode"]
        ],
        "driver" => [
            "id" => $orderData["driverId"],
            "name" => $orderData["driverName"],
            "phone" => $orderData["driverPhone"],
            "identification" => $orderData["driverDocument"]
        ],
        "user" => [
            "id" =>  $orderData["id_usuario"],
            "timezone" => $orderData["timezone"],
            "companyName" => $orderData["orgName"],
            "locale" => $orderData["locale"]
        ]
    ];

    if (isset($orderData['vehicleId'])) {
        $orderTobeExposed['vehicle'] = [
            "id" => $orderData['vehicleId'],
            "name" => $orderData['vehicleName'],
            "plate" => $orderData['vehiclePlate'],

        ];
    }
    return $orderTobeExposed;
}


/**
 * It generates a link to the Epod app, which will render a PDF with the delivery information
 *
 * @param array The data to be encrypted.
 *
 * @return string epod link.
 */
function generateEpodLink($data)
{
    if (!$data['user_key'] || !$data['qtn']) return "";

    // Epod encryption data
    $organizationKey = $data['user_key'];
    $qtn = $data['qtn'];
    // Epod query params
    $photos = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_PHOTOS');
    $map = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_MAP');
    $signature = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_SIGNATURE');
    $orderitems = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_ORDER_ITEMS');
    $comment = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_COMMENT');
    $feedback = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_FEEDBACK');
    $template = get_parametro_usuario($data['id_usuario'], 'EPOD_SELECTED_TEMPLATE');
    $temp = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_TEMP');
    $legals = get_parametro_usuario($data['id_usuario'], 'EPOD_RENDER_LEGALS');

    $epodQueryParamsData = [
        'photos'  => $photos ?: 'false',
        'map'  => $map ?: 'false',
        'signature'  => $signature ?: 'false',
        'orderitems'  => $orderitems ?: 'false',
        'comment'  => $comment ?: 'false',
        'feedback'  => $feedback ?: 'false',
        'template'  => $template ?: '1',
        'temperature' => $temp ?: 'false',
        'legals' => $legals ?: 'false'
    ];
    $epodQueryParams = http_build_query($epodQueryParamsData);

    $epodData = json_encode([
        'organizationKey' => $organizationKey,
        'qtn' => $qtn,
        'issuer' => 'SAAS',
    ]);

    $baseUrl = isProduction() ? "https://epod.quadminds.com" : "https://epod-test.quadminds.com";

    $encryptedData = encryptEpodData($epodData);

    // Ensure right encoding
    $encryptedData = urlencode($encryptedData);

    $epodLink = "{$baseUrl}/api/certification/{$qtn}/print-pdf?{$epodQueryParams}&key={$encryptedData}&issuer=saas";

    return $epodLink;
}

/**
 * ` = openssl_random_pseudo_bytes(openssl_cipher_iv_length("AES-256-CBC"));`
 *
 * This line of code generates a random string of characters that will be used as the initialization
 * vector (IV) for the encryption. The IV is a random string of characters that is used to ensure that
 * the same plaintext will not produce the same ciphertext
 *
 * @param array The data to be encrypted.
 *
 * @return string encrypted string.
 */
function encryptEpodData($data)
{
    $secret = isProduction() ? "5tKBxqDQzWriw9rTlUVSz7lspbhuNMkE" : "8i5p3taLO9Nklq8JW3uGGSJ9OS8ZpE2I";
    $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length("AES-256-CBC"));
    return bin2hex($iv) . openssl_encrypt($data, "AES-256-CBC", $secret, 0, $iv);
}

/**
 * Retrieves order IDs associated with a given route ID.
 *
 * @param int $id_reparto The ID of the route.
 * @return array Order information including departure date, order ID, latitude, and longitude.
 */
function getOrdersIdsByRouteId($id_reparto)
{
    $orders = [];
    $row = [];
    $orders_query = mysql_query("SELECT r.fecha_salida_deposito, p.id AS id_pedido, pp.latitud, pp.longitud
                                FROM repartos r
                                LEFT JOIN clientes_repartos cr ON r.id = cr.id_reparto
                                LEFT JOIN pedidos p ON cr.id = p.id_cliente_reparto
                                LEFT JOIN puntos_partida pp ON (pp.id = SUBSTRING(r.inicio_id, 2))
                                WHERE r.id = $id_reparto");

    if (isTest()) {
        loggear("getOrdersIdsByRouteId: " . "SELECT r.fecha_salida_deposito, p.id AS id_pedido, pp.latitud, pp.longitud
                        FROM repartos r
                        LEFT JOIN clientes_repartos cr ON r.id = cr.id_reparto
                        LEFT JOIN pedidos p ON cr.id = p.id_cliente_reparto
                        LEFT JOIN puntos_partida pp ON (pp.id = SUBSTRING(r.inicio_id, 2))
                        WHERE r.id = $id_reparto", "tnt_hook");
    }

    if (mysql_num_rows($orders_query)) {
        while ($row = mysql_fetch_assoc($orders_query)) {
            $orders[] = $row;
        };
    }

    if (isTest()) loggear("getOrdersIdsByRouteId: " . json_encode($orders), "tnt_hook");

    return $orders;
}
