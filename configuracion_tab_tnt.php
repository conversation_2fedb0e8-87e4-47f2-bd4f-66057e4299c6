<?php

include_once('util_db.php');
include_once('util_menu_rework.php');
include_once('util_session.php');
require_once 'track_and_trace/util_track_and_trace.php';
require_once 'classes/DomainObjects/Usuario.php';
require_once 'classes/DomainObjects/UsuariosParametro.php';
require_once 'classes/Settings/Notifications/NotificationSettings.php';
require_once 'classes/Settings/Notifications/ChannelTypes.php';
require_once 'classes/Settings/Notifications/OrderEventTypes.php';
require_once 'classes/Settings/Notifications/SmsProviders.php';
require_once 'classes/Settings/Notifications/ChannelTypes.php';
require_once 'classes/Settings/Notifications/util_notifications.php';
require_once 'classes/DomainEvents/DomainEventPublisher.php';
require_once 'classes/DomainEvents/Users/<USER>/PublishToExternalPubSub.php';

use DomainEvents\DomainEventPublisher;

manejar_sesion();
$usuario_id = $_SESSION['usuario_id'];

rastrear('configuracion_tab_tnt.php', '', $usuario_id);

$operation = _post('operation');
$received_state = _post('received_state');
$in_route_state = _post('in_route_state');
$eta = _post('eta');
$eta_received = _post('eta_received');
$delivered_state = _post('delivered_state');
$is_close_state = _post('is_close_state');
$not_fulfilled_state = _post('not_fulfilled_state');
$not_fulfilled_partial_state = _post('not_fulfilled_partial_state');
$email_origin = trim(_post('email_origin'));
$orgDisplayName = _post('orgDisplayName');
$contact_phone = _post('contact_phone');
$contact_tnt = _post('contact_tnt');
$margen_tnt = _post('margen_tnt');
$margen_tnt2 = _post('margen_tnt2');
$merchant_received_state = _post('merchant_received_state');
$eta_merchant_received = _post('eta_merchant_received');
$merchant_not_fulfilled_partial_state = _post('merchant_not_fulfilled_partial_state');
$merchant_fulfilled_state = _post('merchant_fulfilled_state');
$merchant_not_fulfilled_state = _post('merchant_not_fulfilled_state');
$sms_received_state = _post('sms_received_state');
$sms_in_route_state = _post('sms_in_route_state');
$sms_arriving_state = _post('sms_arriving_state');
$sms_delivered_state = _post('sms_delivered_state');
$sms_provider = false;
$notification_operation_type = _post('notification_operation_type');

if ($operation == 'save') {
    $user = Usuario::getInstance($usuario_id);
    $user->setParam('TNT_RECEIVED_STATE_ENABLED', strval($received_state));
    $user->setParam('TNT_IN_ROUTE_STATE_ENABLED', strval($in_route_state));
    $user->setParam('TNT_IN_ROUTE_STATE_ETA_DISABLED', strval($eta));
    $user->setParam('TNT_RECEIVED_STATE_ETA_DISABLED', strval($eta_received));
    $user->setParam('TNT_DELIVERED_STATE_ENABLED', strval($delivered_state));
    $user->setParam('TNT_IS_CLOSE_STATE_ENABLED', strval($is_close_state));
    $user->setParam('TNT_FULL_REJECTED_STATE_ENABLED', strval($not_fulfilled_state));
    $user->setParam('TNT_PARTIAL_REJECTED_STATE_ENABLED', strval($not_fulfilled_partial_state));
    $user->setParam('TRACK_AND_TRACE_EMAIL_ORIGIN', strval($email_origin));
    $user->setParam('TRACK_AND_TRACE_CONTACT_PHONE', strval($contact_phone));
    $user->setParam('TNT_ORG_NAME', strval($orgDisplayName));
    // QS-3621 TNT 2.0 epic. No longer optional param
    // $user->setParam('TRACK_AND_TRACE_DISABLED_CONTACT', strval($contact_tnt));
    $user->setParam('TNT_MERCHANT_RECEIVED_STATE_ENABLED', strval($merchant_received_state));
    $user->setParam('TNT_MERCHANT_FULFILLED_STATE_ENABLED', strval($merchant_fulfilled_state));
    $user->setParam('TNT_MERCHANT_RECEIVED_STATE_ETA_ENABLED', strval($eta_merchant_received));
    $user->setParam('TNT_MERCHANT_PARTIAL_REJECTED_STATE_ENABLED', strval($merchant_not_fulfilled_partial_state));
    $user->setParam('TNT_MERCHANT_FULL_REJECTED_STATE_ENABLED', strval($merchant_not_fulfilled_state));

    if ($margen_tnt != 0) {
        $user->setParam('TRACK_AND_TRACE_MARGIN', strval($margen_tnt));
    }
    if ($margen_tnt2 != 0) {
        $user->setParam('TRACK_AND_TRACE_MARGIN_2', strval($margen_tnt2));
    }

    // QM Events notification settings
    $user->setParam('TNT_SMS_RECEIVED_STATE_ENABLED', strval($sms_received_state));
    $user->setParam('TNT_SMS_IN_ROUTE_STATE_ENABLED', strval($sms_in_route_state));
    $user->setParam('TNT_SMS_ARRIVING_STATE_ENABLED', strval($sms_arriving_state));
    $user->setParam('TNT_SMS_DELIVERED_STATE_ENABLED', strval($sms_delivered_state));

    $existsInEventService = UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, UsuariosParametro::EXISTS_IN_EVENT_SERVICE);

    // Only publish new notification settings if user exists in event service
    if (boolval($existsInEventService)) {
        $eventSettingsByChannel = [];
        // Fill notification settings if user has a configured SMS Provider
        if (boolval(UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'SMS_PROVIDER'))) {

            $eventSettingsByChannel = array_merge(array(
                ChannelTypes::sms => [
                    OrderEventTypes::planned => boolval($sms_received_state),
                    OrderEventTypes::route_started => boolval($sms_in_route_state),
                    OrderEventTypes::arriving => boolval($sms_arriving_state),
                    OrderEventTypes::delivered => boolval($sms_delivered_state),
                ]
            ), $eventSettingsByChannel);
        }

        $smsNotificationSettings = buildNotificationSettings($eventSettingsByChannel);
        $userDomainListenerId = DomainEventPublisher::instance()->subscribe(new DomainEvents\Users\PublishToExternalPubSub());
        $user->changedNotificationSettings($smsNotificationSettings);
    }

    $user->setParam('TNT_NOTIFICATION_OPERATION_TYPE', strval($notification_operation_type));
    removeUserCache($usuario_id);
    die("1");
}

$r_parametros = mysql_query("SELECT codigo_parametro, valor
                      FROM usuarios_parametros
                      WHERE id_usuario = $usuario_id
                      AND codigo_parametro IN (
                       'TNT_RECEIVED_STATE_ENABLED',
                       'TNT_RECEIVED_STATE_ETA_DISABLED',
                       'TNT_IN_ROUTE_STATE_ENABLED',
                       'TNT_IN_ROUTE_STATE_ETA_DISABLED',
                       'TNT_DELIVERED_STATE_ENABLED',
                       'TNT_IS_CLOSE_STATE_ENABLED',
                       'TNT_FULL_REJECTED_STATE_ENABLED',
                       'TNT_PARTIAL_REJECTED_STATE_ENABLED',
                       'TRACK_AND_TRACE_EMAIL_ORIGIN',
                       'TRACK_AND_TRACE_CONTACT_PHONE',
                       'TNT_ORG_NAME',
                       'TRACK_AND_TRACE_MARGIN',
                       'TRACK_AND_TRACE_MARGIN_2',
                       'TNT_MERCHANT_RECEIVED_STATE_ENABLED',
                       'TNT_MERCHANT_RECEIVED_STATE_ETA_ENABLED',
                       'TNT_MERCHANT_FULFILLED_STATE_ENABLED',
                       'TNT_MERCHANT_PARTIAL_REJECTED_STATE_ENABLED',
                       'TNT_MERCHANT_FULL_REJECTED_STATE_ENABLED',
                       'TNT_SMS_RECEIVED_STATE_ENABLED',
                       'TNT_SMS_IN_ROUTE_STATE_ENABLED',
                       'TNT_SMS_ARRIVING_STATE_ENABLED',
                       'TNT_SMS_DELIVERED_STATE_ENABLED',
                       'SMS_PROVIDER',
                       'TNT_NOTIFICATION_OPERATION_TYPE'
                      )");

// UI toggles initial state
$received_state = 'checked';
$eta_received = '';
$in_route_state = 'checked';
$eta = '';
$delivered_state = 'checked';
$is_close_state = 'checked';
$contact_tnt = 'checked';
$not_fulfilled_state = 'checked';
$not_fulfilled_partial_state = 'checked';
$merchant_received_state = 'checked';
$eta_merchant_received = '';
$merchant_fulfilled_state = 'checked';
$merchant_not_fulfilled_partial_state = 'checked';
$merchant_not_fulfilled_state = 'checked';
$sms_received_state = '';
$sms_in_route_state = '';
$sms_arriving_state = '';
$sms_delivered_state = '';
$notification_message_type_initial = 'request';


if (mysql_num_rows($r_parametros)) {
    while ($row = mysql_fetch_assoc($r_parametros)) {
        $codigo_parametro = $row['codigo_parametro'];
        $valor = $row['valor'];
        switch ($codigo_parametro) {
            case 'TNT_RECEIVED_STATE_ENABLED':
                $received_state = $valor ? 'checked' : '';
                break;
            case 'TNT_RECEIVED_STATE_ETA_DISABLED':
                $eta_received = $valor ? '' : 'checked';
                break;
            case 'TNT_IN_ROUTE_STATE_ENABLED':
                $in_route_state = $valor ? 'checked' : '';
                break;
            case 'TNT_IN_ROUTE_STATE_ETA_DISABLED':
                $eta = $valor ? '' : 'checked';
                break;
            case 'TNT_DELIVERED_STATE_ENABLED':
                $delivered_state = $valor ? 'checked' : '';
                break;
            case 'TNT_IS_CLOSE_STATE_ENABLED':
                $is_close_state = $valor ? 'checked' : '';
                break;
            case 'TNT_FULL_REJECTED_STATE_ENABLED':
                $not_fulfilled_state = $valor ? 'checked' : '';
                break;
            case 'TNT_PARTIAL_REJECTED_STATE_ENABLED':
                $not_fulfilled_partial_state = $valor ? 'checked' : '';
                break;
            case 'TRACK_AND_TRACE_EMAIL_ORIGIN':
                $email_origin = $valor;
                break;
            case 'TRACK_AND_TRACE_CONTACT_PHONE':
                $contact_phone = $valor;
                break;
            case 'TNT_ORG_NAME':
                $orgDisplayName = $valor;
                break;
            case 'TRACK_AND_TRACE_MARGIN':
                $margen_tnt = $valor;
                break;
            case 'TRACK_AND_TRACE_MARGIN_2':
                $margen_tnt2 = $valor;
                break;
            case 'TRACK_AND_TRACE_DISABLED_CONTACT':
                $contact_tnt = $valor ? 'checked' : '';
                break;
            case 'TNT_MERCHANT_RECEIVED_STATE_ENABLED':
                $merchant_received_state = $valor ? 'checked' : '';
                break;
            case 'TNT_MERCHANT_RECEIVED_STATE_ETA_ENABLED':
                $eta_merchant_received = $valor ? 'checked' : '';
                break;
            case 'TNT_MERCHANT_FULFILLED_STATE_ENABLED':
                $merchant_fulfilled_state = $valor ? 'checked' : '';
                break;
            case 'TNT_MERCHANT_PARTIAL_REJECTED_STATE_ENABLED':
                $merchant_not_fulfilled_partial_state = $valor ? 'checked' : '';
                break;
            case 'TNT_MERCHANT_FULL_REJECTED_STATE_ENABLED':
                $merchant_not_fulfilled_state = $valor ? 'checked' : '';
                break;
            case 'TNT_SMS_RECEIVED_STATE_ENABLED':
                $sms_received_state = $valor ? 'checked' : '';
                break;
            case 'TNT_SMS_IN_ROUTE_STATE_ENABLED':
                $sms_in_route_state = $valor ? 'checked' : '';
                break;
            case 'TNT_SMS_ARRIVING_STATE_ENABLED':
                $sms_arriving_state = $valor ? 'checked' : '';
                break;
            case 'TNT_SMS_DELIVERED_STATE_ENABLED':
                $sms_delivered_state = $valor ? 'checked' : '';
                break;
            case 'SMS_PROVIDER':
                $sms_provider = $valor ? $valor : false;
                break;
            case 'TNT_NOTIFICATION_OPERATION_TYPE':
                $notification_message_type_initial = $valor;
                break;
        }
    }
}

// Tooltips
$ayuda_programado = _('Esta notificación se dispara al momento de generar la ruta que contiene el pedido.');
$ayuda_en_camino = _('Esta notificación se dispara cuando el chofer indica el comienzo de la ruta mediante la aplicación.');
$ayuda_eta = _('Incluir el horario estimado de arribo en la notificacion de pedido en camino. ' .
    'Para que este horario se calcule correctamente, es necesario que el chofer realice la ruta en el mismo orden en el que fue planificada.');
$ayuda_eta_received = _('Incluir el horario estimado de arribo en la notificacion de pedido programado. ' .
    'Para que este horario se calcule correctamente, es necesario que el chofer realice la ruta en el mismo orden en el que fue planificada.');
$ayuda_entregado = _('Esta notificación se dispara cuando el chofer confirma la entrega del pedido mediante la aplicación.');
$ayuda_cerca = _('Esta notificación se dispara cuando el chofer presiona durante unos segundos sobre un cliente para indicarle que es el próximo que va a visitar.');
$ayuda_email_origin = _('Este es el correo electrónico que utilizaremos para el envío de notificaciones.');
$ayuda_contact_phone = _('Este es el número de teléfono que mostraremos en las notiificaciones para que los clientes se puedan contactar.');
$ayuda_contact = _('Permite a sus clientes contactarse con usted.');
$ayuda_margin = _('Ejemplo 60 minutos: ETA 10.00 AM. Ventana de Entrega 9:00 - 11:00 AM.');
$ayuda_entregada_parcial = _('Esta notificación se dispara cuando el chofer confirma que la entrega fue realizada de modo parcial.');
$ayuda_no_entregada = _('Esta notificación se dispara cuando el chofer confirma que la entrega NO fue realizada.');
$ayuda_sms_config = _('Para enviar notificaciones por SMS debes primero configurar el proveedor de envío.');
//TODO!!
$ayuda_no_entrega = '';
$ayuda_sms_in_route = '';
$ayuda_sms_received = '';
$$ayuda_sms_delivered = '';
$ayuda_sms_arriving = '';

// Razon social
$result_user = mysql_query("SELECT * FROM usuarios WHERE id = '$usuario_id'");

$row_user = mysql_fetch_assoc($result_user);

$razonSocial = $row_user['nombre'] == NULL ? "" : $row_user['nombre'];

$razon_social = str_replace('"', "", $razonSocial);

set_locale_tnt($row_user['locale']);

$message_type_by_gender = get_message_type_by_gender();

?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html>

<head>
    <? include_once('analytics.php') ?>
</head>

<body class='home'>
    <div id='contentwrapper' class="container-config-section">
        <div style="margin: auto; padding: 50px 40px 40px">
            <div style="display:flex; flex-direction: column; align-items: center; justify-content: center; ">
                <div class="subtitulos" style="margin-bottom: 10px"><?= _('Notificaciones al Destinatario'); ?></div>
                <div class="tnt-descripcion">
                    <p style="text-align: center; margin-bottom: 4%"><?= _('En esta sección podés seleccionar los emails de seguimiento que envía la plataforma.'); ?></p>
                </div>
            </div>
            <form class="form-horizontal">
                <div class="input-block">
                    <div class="form-group option-tnt">
                        <div class="col-md-offset-1 col-md-4">
                            <label class="control-label">
                                <?= _('Definir tipo de mensaje') ?>
                                <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7; margin-left: 4px"><?= _("NUEVO") ?></span>
                            </label>
                            <label class="control-label label-eta" style="margin-bottom: 16px"><?= _('Ésta es la palabra que llegará en los e-mails al cliente junto al número de orden.') ?></label>
                            <button id="email-test" class="button btn btn-primary" type="button" style="width:155px; font-size: 12px;" onclick="sendTestMail()"><?= _('Enviar e-mail de prueba') ?></button>
                        </div>
                        <div class="col-md-4">
                            <select id="notification-type" class="form-control">
                                <option value="order" <?= $notification_message_type_initial == 'order' ? ' selected="selected"' : ''; ?>><?= $message_type_by_gender['order']['label'] ?></option>
                                <option value="request" <?= $notification_message_type_initial == 'request' ? ' selected="selected"' : ''; ?>><?= $message_type_by_gender['request']['label'] ?></option>
                                <option value="service" <?= $notification_message_type_initial == 'service' ? ' selected="selected"' : ''; ?>><?= $message_type_by_gender['service']['label'] ?></option>
                                <option value="visit" <?= $notification_message_type_initial == 'visit' ? ' selected="selected"' : ''; ?>><?= $message_type_by_gender['visit']['label'] ?></option>
                            </select>
                            <label class="control-label label-eta" style="margin-bottom: 16px"><?= _('Ej: Tu “pedido de [Empresa] #[código]” está confirmado o Tu “orden de [Empresa] #[código]” está realizada.') ?></label>
                        </div>
                    </div>
                </div>
                <hr style="border-top: 1px solid #bbb;">
                <div class="input-block">
                    <div class="form-group option-tnt">
                        <div class="col-md-5"></div>
                        <div class="col-md-2 tnt-column-title">e-mail</div>
                        <div class="col-md-5 tnt-column-title" style="display: flex; align-items:center;">
                            sms
                            <a onclick="triggerSmsConfigTab()" id="sms-config-cog" style="display: flex;">
                                <span data-toggle="tooltip" data-placement="right" title="<?= $ayuda_sms_config ?>" class="glyphicon glyphicon-cog" style="margin-left: 5px; color: #0062A2;" aria-hidden="true"></span>
                                <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7; margin-left: 4px;"><?= _("NUEVO")?></span>
                            </a>
                        </div>
                    </div>
                    <div class="form-group option-tnt">
                        <div class="col-md-offset-1 col-md-4">
                            <label class="control-label"><?= _('Pedido programado') ?></label>
                            <div data-toggle="tooltip" data-placement="left" title="<?= $ayuda_eta_received ?>">
                                <input id='eta_received' type="checkbox" onchange="handleSelectMargin()" <?= $eta_received ?>>
                                <label class="control-label label-eta"><?= _('Incluir hora estimada de arribo (ETA)') ?></label>
                            </div>
                            <div data-toggle="tooltip" data-placement="left" title="">
                                <label class="control-label label-eta"><?= _('Configurar acción “Modificar entrega“') ?></label>
                                <span onclick="triggerDeliveryModificationConfigTab('received')" data-toggle="tooltip" data-placement="right" title="" class="glyphicon glyphicon-cog" style="margin-left: 5px; color: #0062A2; cursor:pointer;" aria-hidden="true"></span>
                                <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7;"><?= _("NUEVO")?></span>
                            </div>
                        </div>
                        <div class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_programado ?>">
                            <input id='received-state' type="checkbox" onchange="receivedStateChanged()" data-toggle="switch" <?= $received_state ?>>
                        </div>
                        <div class="col-md-5" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_sms_received ?>">
                            <input id='sms-received-state' type="checkbox" onchange="receivedStateChanged()" data-toggle="switch" <?= $sms_received_state ?>>
                        </div>
                    </div>
                    <div class="form-group option-tnt">
                        <div class="col-md-offset-1 col-md-4">
                            <label class="control-label"><?= _('Pedido en camino') ?></label>
                            <div>
                                <input id='eta' type="checkbox" onchange="handleSelectMargin()" <?= $eta ?>>
                                <label class="control-label label-eta"><?= _('Incluir hora estimada de arribo (ETA)') ?></label>
                            </div>
                            <div data-toggle="tooltip" data-placement="left" title="">
                                <label class="control-label label-eta"><?= _('Configurar acción “Modificar entrega“') ?></label>
                                <span onclick="triggerDeliveryModificationConfigTab('in_route')" data-toggle="tooltip" data-placement="right" title="" class="glyphicon glyphicon-cog" style="margin-left: 5px; color: #0062A2; cursor:pointer;" aria-hidden="true"></span>
                                <span class="badge badge-pill badge-primary" style="font-size: 10px; color: #fff; background-color: #1473F7; margin-left: 4px;"><?= _("NUEVO")?></span>
                            </div>
                        </div>
                        <div class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_en_camino ?>">
                            <input id='in-route-state' type="checkbox" onchange="inRouteStateChanged()" data-toggle="switch" <?= $in_route_state ?>>
                        </div>
                        <div class="col-md-5" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_sms_in_route ?>">
                            <input id='sms-in-route-state' type="checkbox" onchange="inRouteStateChanged()" data-toggle="switch" <?= $sms_in_route_state ?>>
                        </div>
                    </div>
                    <div class="form-group option-tnt">
                        <label class="col-md-offset-1 col-md-4 control-label"><?= _('Pedido próximo a entregarse') ?></label>
                        <div class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_cerca ?>">
                            <input id='is-close-state' type="checkbox" data-toggle="switch" <?= $is_close_state ?>>
                        </div>
                        <div class="col-md-5" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_sms_arriving ?>">
                            <input id='sms-arriving-state' type="checkbox" data-toggle="switch" <?= $sms_arriving_state ?>>
                        </div>
                    </div>
                    <div class="form-group option-tnt" style="margin-top: 30px;">
                        <label class="col-md-offset-1 col-md-4 control-label"><?= _('Pedido entregado') ?></label>
                        <div class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_entregado ?>">
                            <input id='delivered-state' type="checkbox" data-toggle="switch" <?= $delivered_state ?>>
                        </div>
                        <div class="col-md-5" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_sms_delivered ?>">
                            <input id='sms-delivered-state' type="checkbox" data-toggle="switch" <?= $sms_delivered_state ?>>
                        </div>
                    </div>
                    <div class="form-group option-tnt" style="margin-top: 30px;">
                        <label class="col-md-offset-1 col-md-4 control-label">
                            <?= _('Entrega parcial') ?>
                            <span class="badge badge-pill badge-primary" style="color: #fff; background-color: #1473F7"><?= _("NUEVO")?></span>
                        </label>

                        <div class="col-md-5" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_entregada_parcial ?>">
                            <input id='not-fulfilled-partial-state' type="checkbox" data-toggle="switch" <?= $not_fulfilled_partial_state ?>>
                        </div>
                    </div>
                    <div class="form-group option-tnt" style="margin-top: 30px;">
                        <label class="col-md-offset-1 col-md-4 control-label">
                            <?= _('Entrega fallida') ?>
                            <span class="badge badge-pill badge-primary" style="color: #fff; background-color: #1473F7"><?= _("NUEVO")?></span>
                        </label>
                        <div class="col-md-5" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_no_entrega ?>">
                            <input id='not-fulfilled-state' type="checkbox" data-toggle="switch" <?= $not_fulfilled_state ?>>
                        </div>
                    </div>
                </div>
                <hr style="border-top: 1px solid #bbb;">
                <div class="row" style="display: flex; align-items: flex-start;">
                <div class="input-block" style="flex: 1 2 50%;">
                    <div class="form-group option-tnt" style="margin-top: 35px; display:flex; flex-direction: column;">
                        <label class="col-md-offset-1 col-md-14 control-label"><?= _('Ventana horaria de llegada estimada') ?>
                            <p class="help-text">Configura el tiempo antes y después 
                            de la Hora Estimada de Arribo (ETA) que notificarás a los destinos. </p>
                        </label>
                        <br>
                    </div>
                </div>
                <div style="display: flex;flex: 1 2 100%; align-items: flex-end;justify-content: space-around;align-content: flex-start;flex-wrap: wrap;flex-direction: row;">
                <div style="display:flex; flex-direction: column;">
                    <div class="form-group option-tnt" style="margin-top: 25px; display:flex; flex-direction: column;">
                        <label class="col-md-offset-1 col-md-10 control-label"><?= _('Tiempo antes del ETA') ?>
                        </label>
                        <br>
                        <div class="col-md-4" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_margin ?>">
                            <select id='margen_tnt' class="formText sm" aria-label="Default select example">
                                <option value="900" <?= $margen_tnt == '900' ? ' selected="selected"' : ''; ?>>15 <?= _("minutos")?></option>
                                <option value="1800" <?= $margen_tnt == '1800' ? ' selected="selected"' : ''; ?>>30 <?= _("minutos")?></option>
                                <option value="3600" <?= (!$margen_tnt || $margen_tnt == '3600') ? 'selected="selected"' : '' ?>>60 <?= _("minutos") ?> <?= _("(Recomendado)") ?></option>
                                <option value="4500" <?= $margen_tnt == '4500' ? ' selected="selected"' : ''; ?>><?= _("Una hora y ")?> 15 <?= _("minutos")?></option>
                                <option value="5400" <?= $margen_tnt == '5400' ? ' selected="selected"' : ''; ?>><?= _("Una hora y ")?>30 <?= _("minutos")?></option>
                                <option value="7200" <?= $margen_tnt == '7200' ? ' selected="selected"' : ''; ?>><?= _("Dos horas")?></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div style="display:flex; flex-direction: column;">
                <div class="form-group option-tnt" style="margin-top: 25px; display:flex; flex-direction: column;">
                        <label class="col-md-offset-1 col-md-11 control-label"><?= _('Tiempo después del ETA') ?>
                        </label>
                        <br>
                        <div class="col-md-4" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_margin ?>">
                            <select id='margen_tnt2' class="formText sm" aria-label="Default select example">
                                <option value="900" <?= $margen_tnt2 == '900' ? ' selected="selected"' : ''; ?>>15 <?= _("minutos")?></option>
                                <option value="1800" <?= $margen_tnt2 == '1800' ? ' selected="selected"' : ''; ?>>30 <?= _("minutos")?></option>
                                <option value="3600" <?= (!$margen_tnt2 || $margen_tnt == '3600') ? 'selected="selected"' : '' ?>>60 <?= _("minutos") ?> <?= _("(Recomendado)") ?></option>
                                <option value="4500" <?= $margen_tnt2 == '4500' ? ' selected="selected"' : ''; ?>><?= _("Una hora y ")?> 15 <?= _("minutos")?></option>
                                <option value="5400" <?= $margen_tnt2 == '5400' ? ' selected="selected"' : ''; ?>><?= _("Una hora y ")?>30 <?= _("minutos")?></option>
                                <option value="7200" <?= $margen_tnt2 == '7200' ? ' selected="selected"' : ''; ?>><?= _("Dos horas")?></option>
                            </select>
                        </div>
                    </div>
                </div>
                <P id="margin-help"></P>
                </div>
            </div>
                <hr style="border-top: 1px solid #bbb;">
                <div id="tnt-contact-info" class="input-block">
                    <div class="form-group option-tnt" style="display: flex; margin-top: 2%">
                        <label class="col-md-offset-1 col-md-4 control-label"><?= _('Nombre de contacto') ?></label>
                        <input id='orgDisplayName' type="text" class="formText form-control input-sm" style="width: 240px;height: 35px;margin-left:14px" value="<?= $orgDisplayName ?>">
                        <div id="error-orgDisplayName"></div>
                    </div>
                    <div class="form-group option-tnt" style="display: flex; margin-top: 2%">
                        <label class="col-md-offset-1 col-md-4 control-label"><?= _('Email de notificaciones') ?></label>
                        <div class="col-md-4" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_email_origin ?>">
                            <input id='email-origin' type="text" class="formText form-control input-sm" style="width: 240px;height: 35px;" value="<?= $email_origin ?>">
                            <div id="error-email-config"></div>
                        </div>
                    </div>
                    <div class="form-group option-tnt" style="display: flex; margin-top: 2%">
                        <label class="col-md-offset-1 col-md-4 control-label"><?= _('Número de contacto') ?></label>
                        <div class="col-md-4" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_contact_phone ?>">
                            <input type="tel" class="inputTelefono form-control" style="width: 240px;height: 35px;" id="contact-phone" autocomplete="off" value="<?= $contact_phone ?>">
                            <div id="error-contact-phone-config"></div>
                            <div id="lib-error-contact-phone-config" style="color:red; margin-top: 5px;"></div>
                        </div>
                    </div>
                </div>

                <hr style="border-top: 1px solid #bbb;">
                <div style="width: 90%; margin-left: 8%; display:flex; flex-direction:column">
                    <div class="subtitulos" style="margin-bottom: 10px;"><?= _('Notificaciones al Remitente'); ?></div>
                    <div class="tnt-descripcion">
                        <p style="text-align: center; margin-bottom: 4%"><?= _('En esta sección se selecciona los emails de seguimiento que envía la plataforma.'); ?></p>
                    </div>

                    <div>
                        <div style="display:flex; align-items: baseline">
                            <label><?= _('Pedido en camino') ?></label>
                            <div style="margin-right: auto; margin-left:22%" ; class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_en_camino ?>">
                                <input id='merchant-received-state' type="checkbox" onchange="receivedMerchantStateChanged()" data-toggle="switch" <?= $merchant_received_state ?>>
                            </div>
                            <div style="margin: auto" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_eta_received ?>">
                                <input id='eta_merchant_received' type="checkbox" onchange="handleSelectMargin()" <?= $eta_merchant_received ?>>
                                <label class="control-label"><?= _('Incluir hora estimada de arribo') ?></label>(ETA)
                            </div>
                        </div>

                        <div style="display:flex; align-items: baseline; margin-top:15px;">
                            <label><?= _('Pedido Entregado') ?></label>
                            <div style="margin-right: auto; margin-left:22%" ; class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_entregado ?>">
                                <input id='merchant-fulfilled-state' type="checkbox" data-toggle="switch" <?= $merchant_fulfilled_state ?>>
                            </div>
                        </div>

                        <div style="display:flex; margin-top:15px; align-items: baseline;">
                            <label><?= _('Entrega Parcial') ?></label>
                            <div style="margin-left:24%" class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_entregada_parcial ?>">
                                <input id='merchant-not-fulfilled-partial-state' type="checkbox" data-toggle="switch" <?= $merchant_not_fulfilled_partial_state ?>>
                            </div>
                        </div>

                        <div style="display:flex; margin-top:15px; align-items: baseline;">
                            <label><?= _('Entrega Fallida') ?></label>
                            <div style="margin-left:24%" class="col-md-2" data-toggle="tooltip" data-placement="top" title="<?= $ayuda_no_entregada ?>">
                                <input id='merchant-not-fulfilled-state' type="checkbox" data-toggle="switch" <?= $merchant_not_fulfilled_state ?>>
                            </div>

                        </div>
                    </div>
                    <hr style="border-top: 1px solid #bbb;">
                    <div class="form-group">
                        <div class="text-center col-md-12" style="display:flex; justify-content:center">
                            <input id="tnt-save-config-btn" type="button" value="<?= _('Guardar')?>" class="button btn btn-success" style="width:100px" onclick="grabarTNT();">
                        </div>
                    </div>
                    <div id="tnt_response" style="text-align: center;">&nbsp;</div>
                </div>

            </form>
        </div>
    </div><!-- end div contentwrapper -->
</body>
<script>
    var sms_provider = <?= json_encode($sms_provider) ?>;
    var idUsuario = <?= json_encode($usuario_id) ?>;
    var emailOrigin = <?= json_encode($email_origin) ?>;
    var razonSocial = <?= json_encode($razon_social) ?>;

    $(document).ready(function() {
        handleSelectMargin();

        initializePhoneInputHandler();

        /* // Enables save button after all contact info inputs are set
        $('#tnt-contact-info').on('keyup', () => {
            if ($("#contact-phone").val() && $("#email-origin").val()) {
                $('#tnt-save-config-btn').attr('disabled', false);
            } else {
                $('#tnt-save-config-btn').attr('disabled', 'disabled');
            }
        }); */

        if (!sms_provider) {
            $('#sms-received-state').prop("disabled", true);
            $('#sms-in-route-state').prop("disabled", true);
            $('#sms-arriving-state').prop("disabled", true);
            $('#sms-delivered-state').prop("disabled", true);
            $('#sms-config-cog span').tooltip('show')
        }

        $('#margen_tnt, #margen_tnt2').on('change', actualizarVentanaEntrega);
    });

    function formatHour(date) {
        let horas = date.getHours();
        let minutos = date.getMinutes();
        let ampm = horas >= 12 ? 'p. m.' : 'a. m.';

        horas = horas % 12;
        horas = horas ? horas : 12; 
        minutos = minutos < 10 ? '0' + minutos : minutos;

        return horas + ':' + minutos + ' ' + ampm;
    }

    function actualizarVentanaEntrega() {
        const eta = new Date();
        eta.setHours(12, 0, 0); 

        const margenAntes = parseInt($('#margen_tnt').val()) || 0;
        const margenDespues = parseInt($('#margen_tnt2').val()) || 0;

        const horaInicio = new Date(eta.getTime() - margenAntes * 1000);
        const horaFin = new Date(eta.getTime() + margenDespues * 1000);

        if (margenAntes === 0 && margenDespues === 0) {
            $('#margin-help').html('Con esta configuración, si el ETA para un destino es a las 12:00, el mensaje dirá: “Llegará el jueves 29 de agosto a las 12:00”');
            return;
        }
        const mensaje = `Con esta configuración, si el ETA para un destino es a las 12:00, el mensaje dirá: “Llegará el jueves 29 de agosto entre las ${formatHour(horaInicio)} y la ${formatHour(horaFin)}”`;
        $('#margin-help').html(mensaje);
    }

    function initializePhoneInputHandler() {
        // Initializes phone input
        const phoneInput = document.querySelector("#contact-phone");
        const errorMsg = document.querySelector("#lib-error-contact-phone-config");
        const errorMap = ["Número inválido", "Codigo de país inválido", "Muy corto", "Muy largo", "Número inválido"];

        const iti = window.intlTelInput(phoneInput, {
            utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.5/build/js/utils.js",
            initialCountry: "auto",
            geoIpLookup: (callback) => {
                fetch('https://ipinfo.io/json', {
                    cache: 'reload'
                }).then(response => {
                    if (response.ok) {
                        return response.json()
                    }
                    throw new Error('Failed: ' + response.status)
                }).then(ipjson => {
                    callback(ipjson.country)
                }).catch(e => {
                    callback('ar')
                })
            },
        });
        // Expose api
        window.iti = iti;

        const reset = () => {
            phoneInput.classList.remove("has-error");
            errorMsg.innerHTML = "";
            errorMsg.classList.add("hide");
        };


        // on blur: validate
        phoneInput.addEventListener('blur', () => {
            reset();
            if (phoneInput.value.trim()) {
                if (iti.isValidNumber()) {
                    validMsg.classList.remove("hide");
                } else {
                    phoneInput.classList.add("has-error");
                    const errorCode = iti.getValidationError();
                    errorMsg.innerHTML = errorMap[errorCode];
                    errorMsg.classList.remove("hide");
                }
            }
        });
    }

    function handleSelectMargin() {
        if ($('#eta_received').prop('checked') || $('#eta').prop('checked') || $('#eta_merchant_received').prop('checked')) {
            $('#margen_tnt').attr('disabled', false);
            $('#margen_tnt2').attr('disabled', false);
            actualizarVentanaEntrega();
        } else {
            $('#margen_tnt').attr('disabled', true);
            $('#margen_tnt2').attr('disabled', true);
            $('#margin-help').html('');
        }
    }

    function sendTestMail() {
        const notificationType = document.getElementById('notification-type').value;
        const body = {
            operacion: 'sendTest',
            notificationType,
        }
        const options = {
            headers: {
                'Accept': 'application/json'
            },
            method: 'POST',
            body: JSON.stringify(body)
        };
        console.log(options);
        fetch(`/track_and_trace/send_mail.php`, options).then(res => console.log('Test', res));
    }
</script>

</html>