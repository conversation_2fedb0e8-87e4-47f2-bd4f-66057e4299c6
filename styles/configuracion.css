/*GENERAL*/
.well, .row {
    margin: 0;
    padding: 0;
}
#contentwrapper {
    padding: 0;
}

.table-seguridad{
    position: absolute;
    top:20px;
}
.title {
    height: 100px;
    position: relative;
}
.title>div {
    position: absolute;
    top:30%;
    font-size: 30px;
}
span, .glyphicon {
    vertical-align: middle;
}
.buttonBlock {
    min-width: 220px;
    display: flex;
    flex-flow: column nowrap;
}


/* Left tabs custom CSS */
#tabNav li {
    background: #EAEAEA;
    margin-bottom: 2px;
}
#tabNav li.active a span{
    color: #337AB7;
}

#tabNav>li>a {
    margin: 0;
    border: 0;
    border-radius: 0;
}

#tabNav>li.active>a {
    margin: 0;
    border: 0;
    border-radius: 0;
}

.blockCont{
    background-color: #fdfdfd ;
    height: max-content;
    position: relative;
}
.blockCont>div:not(.loader){
    width: 100% !important;
    position: relative;
    min-height: 400px;
}
#form_response {
    font-weight: bold;
    padding-top: 22px;
}
#form_response_email {
    font-weight: bold;
    padding: 10px 0;
}
.btnTitle {
    font-size: 1em;
    font-weight: bold;
    color: #37474F;
}
/*CONTRASEÑA*/
.formText {
    width: 100%;
    height: 28px;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin: 4px -32px 0 0;
}
.subtitulos {
    font-size: 17px;
    font-weight: bold;
    color: #0062A2;
    text-align: center;
    text-transform: uppercase;
}
.subtitulos_app {
    font-size: 17px;
    font-weight: bold;
    color: #0062A2;
    text-align: center;
    text-transform: uppercase;
    margin-top:50px;
}
.subtitulos2 {
    font-size: 14px;
    font-weight: bold;
    color: #0062A2;
    text-align: center;
}
select {
    height: 30px;
    width: 100%;
    border: 1px solid #ddd;
    border-radius: 3px;
}
.areaCont {
    width: 60%;
    margin: auto;
    padding-top: 30px;
}
/*EMAILS*/

.areaEmails {
    width: 90%;
    margin: auto;
    padding-top: 30px;
}
#direcciones {
    border-collapse:collapse;
    text-align: center;
}
#direcciones td{
    font-size:0.8em;
    background-color: #fff;
    padding: 2px 0;
    border-bottom:1px solid #eee;
}
#direcciones th {
    color:#222;
    font-size:0.8em;
    padding: 2px 0;
}
#direcciones tr.alt td {
    color:#000;
    background-color:#808080;
}
.borrar {
    cursor: pointer;
}
th {
    text-align: center !important;
}
/*CALENDARIO*/
.areaCalendario{
    width: 90%;
    margin: auto;
    padding-top: 30px;
}
#calendario>div {
    margin: auto;
}
.day {
    cursor: pointer;
}
.day:hover {
    background-color: #e4e4e4;
}
.ui-datepicker .ui-datepicker-calendar .ui-state-highlight a {
    background: #743620 none;
    color: white;
}
.ui-datepicker {
    margin: auto;
}
table.ui-datepicker-calendar {border-collapse: separate;}
.ui-datepicker-calendar td {border: 1px solid transparent;}

.mt{
    margin-top:20px;
}

#changes-saved{
    display: none;
    text-align: center;
}

.form-horizontal .control-label{
    text-align:left;
}

.label-eta{
    font-size: 12px;
    font-weight: normal;
}

.tnt-column-title{
    font-size: 16px;
    font-weight: bold;
    padding-left: 34px;
}

/*Mobile config*/
.mobile-config-container{
    margin: 4rem 5rem;
}
.rejected-reasons-description{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
    display: flex; 
    margin: 2rem;
    justify-content: center
}
.rejected-reasons-description p {
    text-align: center;
    color: #333333;
}
.rejected-reasons-component {
    display: flex; 
    justify-content: center;
}
.rejected-reasons-container {
    display: flex; 
    flex-direction:column; 
}
.option-title{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;;
    font-weight: bold;
    color: #333333;
    font-size: 16px;
    margin-bottom:4px;
    margin-top: 17px;
}

.separator{
    margin-top: 11px;
    margin-bottom: 11px;
}

badge-blue{
    background-color: #007bff;
    color:#fff;
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: bold;
    line-height: 1;
    vertical-align: baseline;
    white-space: nowrap;
    text-align: center;
    border-radius: 10px;
}

.option-details{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 19px;
    letter-spacing: 0em;
    text-align: left;
}

#margen_tnt,
#margen_tnt2{
    width: 222px;
    height: 35px;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
}

#email-origin{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
    color: rgb(51, 51, 51);
}

#margin-help, 
.help-text{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 13px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: left;
    margin-top:5px;
}

.tnt-descripcion{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
}

.option-seguridad{
    width: 50%;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
    text-align: left;
}
.option-image{
    color: #78909C;
}



.billing_label{
    width: 70%;
    margin-left: 135PX;
    margin-right: auto;
    margin-top: 25px;
}

.billing_section{
    border: 1px solid #8898AA;
    margin-top: 35px;
    width: 98%;
}


.cambio_pass{
    margin-left: 10px;
    margin-top:5px;
}

#imageFileClickPencil{
    display:flex;
    margin-left: auto;
    top: 100px;
    left: 100px;
    background: white;
    padding: 3px;
    border-radius: 5px;
    cursor: pointer;
}
.razon-social-functions{
    display: none;
    padding: 0 0 0 0;
    margin-left: 10px;
    margin-top:5px;
    border: none;
}

.section-logo{
    display:block;
    width: 120px;
    margin-top: 20px;
}

.input_password{
    width: 222px;height: 41px;
}

.subtitulo-2-seguridad{
    width: 60%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 25px;
    margin-bottom:-16px;
}

.description-seguridad{
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 15px;
    font-style: normal;
    font-weight: 300;
    line-height: 17px;
    letter-spacing: 0em;
}

#showControlsRazonSocial{
    background:white;
    padding: 0 0 0 0;
    margin-left: 10px;
    margin-top:5px;
    border: none;
}

#razon_social{
    width: 222px;
    height: 41px;
}

.billing_button{
    width: 100%;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top:10px;
}

.rejected-reasons-header{
    display: flex;
    flex-direction: column;
}

.rejected-reasons-header h4{
    text-align: left;
    font-weight: bold;
    letter-spacing: 0px;
    color: #333333;
}

.rejected-reasons-list {
    display: flex; flex-direction:column; 
    height: 200px; overflow: auto;  
}
.rejected-reason-span{
    display: flex; padding:5px;
}
.rejected-reason-span input {
    width: 532px; 
    height: 41px;
    border: 1px solid #DDDDDD;
    border-radius: 3px;
    margin-right:20px
}
.rejected-reason-remove{
    display: flex; 
    align-items: center;  
    cursor: pointer;
}
.rejected-reasons-action-buttons {
    display: flex; 
    margin-top: 2rem;
}
.rejected-reasons-add-btn {
    width: 164px; 
    height: 41px;  
    margin-right: 15px;
}
.rejected-reasons-save-btn{
    width: 92px; height: 41px;
}
.mobile-config-ajax-messages {
    margin: 1rem; 
    font-size: 20px
}

.macro-state-options{
    display: flex;
    flex-direction: column;
}

.option-container{
    display: flex;
    justify-content: space-between;
}

.option-description {
    width: 450px;
}

.option-switch{
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.option-switch label.btn {
    width: 50px;
}

.option-switch label.btn-primary.active {
    background-color: #337AB7;
    border-color: #2E6DA4;
    /*border-radius: 3px 0px 0px 3px;*/
}

.macro-state-title{
    justify-content: center;
    text-align: center;
}

.light-blue{
    background-color: #337ab7 !important;
}
input:focus, textarea:focus, select:focus{
    outline: none;
}

.input-block{
    margin: 25px 0;
}

.iti-flag {
  background-image: url('https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/12.1.6/img/flags.png');
}

.areaDispositivos{
    width: 90%;
    margin: auto;
    padding-top: 10px;
}

.areaAPI{
    margin: auto;
    padding-top: 35px;
}
.container-config-section{
    padding: 40px; 
    margin: auto; 
    background:#FFF;
    min-height: 500px !important;
}

.contenido-config-section{
    width: 85%;
}

.config-merchants, 
.config-integrations,
.config-tnt-entregas{
    width: 960px;
}