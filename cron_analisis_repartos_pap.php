<?php

include_once('util_db.php');
include_once('util_historico.php');

// ini_set('display_errors', 1);
// error_reporting(E_ALL);

if(isset($argv[1])){
    $fecha = $argv[1];
} else {
    $fecha = date("Y-m-d", strtotime("-1 day"));
}

$start = microtime(true);
$chunk_count = 1;

$recalculoQuery = "SELECT
    d.id_maquina, d.codigo_tipo_dispositivo, d.sub_tipo_dispositivo,
    r.codigo,r.fecha,  r.id, r.inicio_id, r.id_usuario, r.id_dispositivo, r.telefono_asignado, r.fecha_inicio, r.fecha_presentacion, r.fecha_salida_deposito,
    cr.fecha_visitado, cr.id_cliente,
    c.razon_social, SUBSTRING_INDEX(c.codigo,'-',1) as codigo_cliente, c.latitud as cliente_latitud, c.longitud as cliente_longitud,
    pp.latitud as partida_latitud , pp.longitud as partida_longitud, pp.id as punto_partida_id
FROM repartos r
    INNER JOIN clientes_repartos cr ON r.id = cr.id_reparto
    INNER JOIN clientes c ON c.id = cr.id_cliente
    INNER JOIN puntos_partida pp on pp.id = SUBSTRING(r.inicio_id FROM 2)
    INNER JOIN dispositivos d ON d.id = r.id_dispositivo
WHERE r.fecha >= '$fecha'
    AND r.estado = 'TERMINADO'
    AND r.fecha_salida_deposito is not null
    -- AND cr.fecha_visitado is not null
    AND r.id_usuario = 3050
    AND r.inicio_id = 'D8572'
    AND NOT EXISTS (
        SELECT 1
        FROM analisis_repartos_pap arp
        WHERE arp.id_reparto = r.id
    )
    -- AND ST_Distance_Sphere(point(pp.latitud, pp.longitud), point(c.latitud,c.longitud))/1000 > 100
GROUP BY r.id
HAVING COUNT(cr.id) = 1";

$r_query = mysql_query($recalculoQuery);
if(!$r_query){
    echo mysql_error();
    die;
}

$rutas_procesar = [];
$total_rutas = 0;

$distancias_here = [];
$shm_key = ftok(__FILE__, 't');
$shm_id = shmop_open($shm_key, "c", 0644, 262144);
shmop_write($shm_id, serialize($distancias_here), 0);

$rutas_error = [
    'no_start' => 0,
    'short_path' => 0,
    'path_not_found' => 0,
    'invalid_path' => 0,
    'here_error' => 0,
    'total_errors' => 0
];

$rutas_procesadas = 0;

while($row = mysql_fetch_assoc($r_query)){
    $total_rutas++;
    //Calculo la fecha y hora de salida
    if ($row['fecha_visitado']) {
        $fecha_inicio = date("Y-m-d H:i:s", strtotime($row['fecha_presentacion']));
        $fecha_limite_fin_carga = date("Y-m-d H:i:s", strtotime($row['fecha_visitado']));
    } else {
        $fecha_inicio = date("Y-m-d H:i:s", strtotime($row['fecha_presentacion']));
        $fecha_limite_fin_carga = date("Y-m-d H:i:s", strtotime("+24 hours", strtotime($row['fecha_presentacion'])));
    }
    $r_fin_carga = mysql_query("SELECT time_stamp FROM eventos
        WHERE id_dispositivo = $row[id_dispositivo]
        AND time_stamp BETWEEN '$fecha_inicio' AND '$fecha_limite_fin_carga'
        AND codigo_tipo_evento = 'FZONA'
        AND (data IN (SELECT descripcion FROM puntos_partida WHERE 
                id_usuario = 3050 and codigo_erp IS NOT NULL) OR data LIKE 'Terminal%') 
        ORDER BY time_stamp DESC
        LIMIT 1"
    );

    if (mysql_num_rows($r_fin_carga)) {
         echo "Reparto ".$row['id']." Busqueda Inicio: ".$fecha_inicio." Busqueda Fin: ".$fecha_limite_fin_carga." ";
         echo "Salida en reparto: ".$row['fecha_salida_deposito']." ";
        $salida_zona = mysql_result($r_fin_carga,0,0);;
        $row['fecha_salida_deposito'] = $salida_zona;
         echo "Salida de zona: ".$salida_zona." Nueva Salida Reparto: ".$row['fecha_salida_deposito'];
         echo PHP_EOL;
    } else {
        $rutas_error['total_errors']++;
        $rutas_error['no_start']++;
         echo "Salida no encontrada para ruta: ".$row['id'];
         echo PHP_EOL;
        continue;
    }

    // Si no tiene llegada a depósito y tenia configurado el regreso de ruta, salteo el reparto
    // if(!$row['fecha_salida_deposito'] || !$row['inicio_id'])
    //     continue;
    $rutas_procesar []= $row;
}

$rutas_count = count($rutas_procesar);
$rutas_chunked = array_chunk($rutas_procesar, round($rutas_count/$chunk_count));

mysql_close();
$csv = [];

for ($i = 0; $i < count($rutas_chunked); $i++) {
    $pid = pcntl_fork();
    if ($pid == -1) {
        die('No se pudo realizar el fork');
    }
    if (!$pid) {
        
        // No sacar o no funciona el fork
        mysql_connect($db_host,$username,$password);
        mysql_select_db($database);

        $raw_data = shmop_read($shm_id, 0, 1024);

        $distancias_here = unserialize(shmop_read($shm_id, 0, 1024));

        cli_set_process_title("cron_analisis_repartos_pap child $i");
        foreach($rutas_chunked[$i] as $row) {
            $distanceObject = estimateRouteDistance(
                $row['id_maquina'],
                $row['codigo_tipo_dispositivo'], 
                $row['fecha_salida_deposito'], 
                $row['fecha_visitado'],
                ["latitud" => $row['partida_latitud'], "longitud" => $row['partida_longitud']],
                ["latitud" => $row['cliente_latitud'], "longitud" => $row['cliente_longitud']]
            );

            if(!$distanceObject){
                $rutas_error['total_errors']++;
                $rutas_error['here_error']++;
                 echo ",Recorrido no encontrado ruta: ".$row['id'];
                 echo PHP_EOL;
                continue;
            }

            if(isset($distanceObject['error'])){
                $rutas_error['total_errors']++;
                switch ($distanceObject['type']) {
                    case 0:
                        $rutas_error['path_not_found']++;
                        break;
                    case 1:
                        $rutas_error['invalid_path']++;
                        break;
                    case 2:
                        $rutas_error['short_path']++;
                        break;
                    default:
                        break;
                }

                 echo ",Recorrido invalido ruta: ".$row['id'];
                 echo " ".$distanceObject['error'];
                 echo PHP_EOL;
                continue;
            }

            if(isset($distanceObject['response'])){
                if(isset($distancias_here[$row['id']])) {
                    $directionsDistance = $distancias_here[$row['id']]['length'];
                    $polyline = $distancias_here[$row['id']]['polyline'];
                } else {
                    $route_response = estimateRouteLenght(
                        $row['fecha_salida_deposito'],
                        ["latitud" => $row['partida_latitud'], "longitud" => $row['partida_longitud']],
                        ["latitud" => $row['cliente_latitud'], "longitud" => $row['cliente_longitud']]
                    );
                    if($route_response){
                        $directionsDistance = $route_response['routes'][0]['sections'][0]['summary']['length'];
                        $directionsDistance = $directionsDistance/1000;
                        $directionsDistance = round($directionsDistance, 2);
                        $distancias_here[$row['id']]['lenght'] = $directionsDistance;
                        
                        $polyline = $route_response['routes'][0]['sections'][0]['polyline'];
                        $distancias_here[$row['id']]['polyline'] = $polyline;
                        shmop_write($shm_id, serialize($distancias_here), 0);
                    } else {
                        $rutas_error['total_errors']++;
                        $rutas_error['here_error']++;
                         echo ",Error estimatedRouteLenght response : " . json_encode($route_response);
                        continue;
                    }
                }


                // tengo que hacer todo esto porque el polyline que genera heremaps es muy grande
                // decodifico el polyline que devuelve here porque es muy extenso, lo simplifico y lo vuelvo a encodear.
                $posicionesAntes = decodeHerePolyline($polyline);
                $tolerancia = 0.0003; // Podés ajustar este valor
                $posiciones = douglasPeuckerFull($posicionesAntes, $tolerancia);
                $plain_gps = [];
                //Construyo el PathString para enviar a Here.
                for($i = 0; $i < count($posiciones);$i++){
                    $path_string .= $posiciones[$i]['latitud_gps'].",".$posiciones[$i]['longitud_gps'].",".$posiciones[$i]['ult_medicion_gps']."\n";
                    $plain_gps []= [$posiciones[$i]['latitud_gps'], $posiciones[$i]['longitud_gps']];
                }
                $polyline = encodePolyline($plain_gps, 5, 0, 0);


                $rutas_procesadas++;

                $urlMapa = "https://image.maps.hereapi.com/mia/v3/base/mc/overlay:padding=64/1920x1080/png?";
                $urlMapa .= "apiKey=ajd4lWSQ6N5Z8kKvD-Ef26zTut-OJe0Bdh-JfCQFQg4";
                $urlMapa .= "&overlay=line:$polyline;color=%2300DD00";
                $urlMapa .= "&overlay=line:".$distanceObject['polyline'].";color=%23DD0000";
                $urlMapa .= "&style=lite.day&scaleBar=km";

                $idReparto = $row['id'];
                $fechaReparto = $row['fecha'];
                $idPuntoPartida = $row['punto_partida_id'];
                $idCliente = $row['id_cliente'];
                $idUsuario = $row['id_usuario'];
                // Distancia real
                $distanciaReal = round(($distanceObject['response']['route'][0]['summary']['distance'] / 1000), 2);
                // Distancia  teorica
                $distanciaTeorica = isset($directionsDistance) ? $directionsDistance : 0;
                // Duracion del recorrido     
                $duracion = gmdate("H:i:s", $distanceObject['response']['route'][0]['summary']['trafficTime']);

                // $csv []= $row['id'].",".$row['codigo_cliente'].",".$row['razon_social'].",".$row['id_cliente'].",".$row['inicio_id'].",".($distance/1000).",".$row['fecha_salida_deposito'].",".$row['fecha_visitado'].",".(isset($directionsDistance) ? $directionsDistance : '-').",".$urlMapa;
                mysql_query("INSERT INTO analisis_repartos_pap 
                (id_reparto,fecha, id_punto_partida, id_cliente, id_usuario, distancia_ejecutada, distancia_teorica, duracion, link_mapa)
                    VALUES($idReparto, '$fechaReparto', $idPuntoPartida, $idCliente, $idUsuario, $distanciaReal, $distanciaTeorica, '$duracion', '$urlMapa')");

                echo $urlMapa;

                echo $row['id'].",".$row['codigo_cliente'].",".$row['razon_social'].",".$row['id_cliente'].",".$row['inicio_id'].",".($distanciaReal/1000).",".$row['fecha_salida_deposito'].",".$row['fecha_visitado'].",".(isset($directionsDistance) ? $directionsDistance : '-');
                echo PHP_EOL;
            }else{
                $rutas_error['total_errors']++;
                $rutas_error['here_error']++;
                 echo ",Error estimatedRouteDistance response : " . json_encode($distanceObject);
            }

             echo "Id Usuario : ".$row['id_usuario']." - Id Reparto : ".$row['id']." - Codigo: ".$row['codigo']." - Dispositivo: ".$row['id_dispositivo']." - Telefono: ".$row['telefono_asignado']." - Hora salida: ".$row['hora_salida_deposito']." - Hora Regreso: ".$row['hora_llegada_deposito']." - Vuelta a Deposito: ".($row['fin_id'] ? "SI" : "NO")." || ";
             echo ' Distancia ejecutada : ' . ($distanciaReal/1000);
             echo ' Distancia teorica : ' . (isset($directionsDistance) ? $directionsDistance : '-');
             
             echo $distanceObject['response'] ? $distanceObject['response']['route'][0]['summary']['distance'] : json_encode($distanceObject);
             echo PHP_EOL;
        }

        exit($i);
    }
}


while (pcntl_waitpid(0, $status) != -1) {
    $status = pcntl_wexitstatus($status);
}

$elapsed = microtime(true) - $start;
$rate = $rutas_count/$elapsed;

foreach($csv as $line){
    echo $line;
    echo PHP_EOL;
}

// Close shared memory
shmop_delete($shm_id);
shmop_close($shm_id);

echo PHP_EOL;
echo date("Y-m-d H:i:s")." Fin Analisis rutas";
echo PHP_EOL;
echo "Rutas totales: $total_rutas";
echo PHP_EOL;
echo "Rutas a Procesar: $rutas_count";
echo PHP_EOL;
echo "Rutas procesadas: $rutas_procesadas";
echo PHP_EOL;
echo "Rutas con Errores: ".json_encode($rutas_error);
echo PHP_EOL;
echo "($rutas_count rutas evaluadas en $elapsed segundos, $rate rutas/segundo)";
// No sacar o no funcionan los siguientes crons
mysql_connect($db_host,$username,$password);
mysql_select_db($database);

function estimateRouteDistance($id_dispositivo, $tipo_dispositivo, $date_from, $date_to, $origin, $destination){
    $here_apikey = 'ajd4lWSQ6N5Z8kKvD-Ef26zTut-OJe0Bdh-JfCQFQg4';
    $here_matchRoute_url = 'https://routematching.hereapi.com/v8/calculateroute.json?routeMatch=1&mode=fastest;truck;traffic:disabled&routeAttributes=sm';
    $url = $here_matchRoute_url."&apikey=".$here_apikey;
    $path_string = "LATITUDE,LONGITUDE,TIMESTAMP\n";

    if(strtotime('NOW') - strtotime($date_from) > 48 * 60 * 60) {
        $gps = getGpsHistorico($id_dispositivo, $date_from, $date_to, $tipo_dispositivo == 'NEXTEL' ? 'sat_gps>3' : null);
    } else {
        $query_gps = "select latitud_gps, longitud_gps, ult_medicion_gps
            from   dispositivos_gps_hist
            where  id_dispositivo = $id_dispositivo
            and    ult_medicion_gps >= '$date_from'
            and    ult_medicion_gps <= '$date_to'";
        if($tipo_dispositivo == 'NEXTEL')
            $query_gps .= " and    sat_gps > 3";
        $query_gps .= " order by ult_medicion_gps";

        $gps_result = mysql_query($query_gps);
        $gps = [];
        
        $first_point = false;
        while($punto = mysql_fetch_array($gps_result, MYSQL_ASSOC)){
            $gps []= $punto;
        }
    }
    // Valido que haya suficientes puntos de recorrido
    $path_count =count($gps);
    $min_path = ((strtotime($date_to)-strtotime($date_from))/180);

    if(!$gps || $path_count == 0) return ["error" => "GPS path not found.", "type" => 0];
    if($path_count < $min_path) return ["error" => "Invalid GPS Path. Path Count: ".$path_count." Min Path: ".$min_path, "type" => 1];


    //Valido que el punto de inicio y el punto de fin del recorrido esten cerca del origen y el destino
    $first_point = $gps[0];
    $last_point = $gps[count($gps)-1];

    $distance_start_origin = distance($first_point['latitud_gps'],$first_point['longitud_gps'],$origin['latitud'],$origin['longitud']);
    $distance_end_destination = distance($last_point['latitud_gps'],$last_point['longitud_gps'],$destination['latitud'],$destination['longitud']);
    if($distance_end_destination > 2000 || $distance_start_origin > 2000){
        return ["error" => ",Recorrido incorrecto: distancia inicio origen: ".$distance_start_origin.", distancia final destino: ".$distance_end_destination, "type" => 2];
    }
    
    // disminuyo el detalle de puntos para que el polyline no sea extenso
    $tolerancia = 0.0003; // Podés ajustar este valor
    $gps = douglasPeuckerFull($gps, $tolerancia);

    
    $plain_gps = [];
    //Construyo el PathString para enviar a Here.
    for($i = 0; $i < count($gps);$i++){
        $path_string .= $gps[$i]['latitud_gps'].",".$gps[$i]['longitud_gps'].",".$gps[$i]['ult_medicion_gps']."\n";
        $plain_gps []= [$gps[$i]['latitud_gps'], $gps[$i]['longitud_gps']];
    }
    $polyline = encodePolyline($plain_gps, 5, 0, 0);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $path_string);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/octet-stream'));
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); 
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    $curl_output = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    switch ($http_code) {
        case 200:
            $result = json_decode($curl_output, TRUE);
            $result['url'] = $url;
            $result['polyline'] = $polyline;
            return $result;
        default:
            $result = json_decode($curl_output, TRUE);
            return $result;
    }
}

function estimateRouteLenght($date_from, $origin, $destination){
    $here_apikey = 'ajd4lWSQ6N5Z8kKvD-Ef26zTut-OJe0Bdh-JfCQFQg4';
    $here_routing_url = 'https://router.hereapi.com/v8/routes?';
    
    $url = $here_routing_url."apikey=".$here_apikey;
    $url .= "&transportMode=truck";
    $url .= "&routingMode=short";
    $url .= "&origin=".$origin['latitud'].",".$origin['longitud'];
    $url .= "&destination=".$destination['latitud'].",".$destination['longitud'];   
    $url .= "&return=summary,polyline";
    $url .= "&departureTime=".date("Y-m-d\TH:i:sP", strtotime($date_from));
    $url .= "&vehicle[shippedHazardousGoods]=combustible";
    $url .= "&vehicle[grossWeight]=40000";
    $url .= "&vehicle[trailerCount]=1";
    $url .= "&vehicle[type]=Tractor";

        
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/octet-stream'));
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); 
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    $curl_output = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    switch ($http_code) {
        case 200:
            $result = json_decode($curl_output, TRUE);
            $result['url'] = $url;
            return $result;
        default:
            return false;
    }
}
	
function distance($lat1, $lng1, $lat2, $lng2, $miles = false)
{
    $pi80 = M_PI / 180;
    $lat1 *= $pi80;
    $lng1 *= $pi80;
    $lat2 *= $pi80;
    $lng2 *= $pi80;
    $r = 6372.797; // mean radius of Earth in km
    $dlat = $lat2 - $lat1;
    $dlng = $lng2 - $lng1;
    $a = SIN($dlat / 2) * SIN($dlat / 2) + COS($lat1) * COS($lat2) * SIN($dlng / 2) * SIN($dlng / 2);
    $c = 2 * ATAN2(SQRT($a), SQRT(1 - $a));
    $km = $r * $c;

    return ($miles ? ($km * 0.621371192) : $km*1000);
}

//Encoder de polyline de HERE
function encodePolyline(
    array $coordinates,
    int $precision = null,
    int $thirdDim = null,
    int $thirdDimPrecision = 0
) {
    if (is_null($precision)) {
        $precision = 5;
    }

    $multiplierDegree = 10 ** $precision;
    $multiplierZ = 10 ** $thirdDimPrecision;
    $encodedHeaderList = encodeHeader($precision, $thirdDim, $thirdDimPrecision);
    $encodedCoords = [];

    $lastLat = 0;
    $lastLng = 0;
    $lastZ = 0;

    foreach ($coordinates as $location) {
        $lat = (int)round($location[0] * $multiplierDegree);
        $encodedCoords[] = encodeScaledValue($lat - $lastLat);
        $lastLat = $lat;

        $lng = (int)round($location[1] * $multiplierDegree);
        $encodedCoords[] = encodeScaledValue($lng - $lastLng);
        $lastLng = $lng;

        if ($thirdDim) {
            $z = (int)round($location[2] * $multiplierZ);
            $encodedCoords[] = encodeScaledValue($z - $lastZ);
            $lastZ = $z;
        }
    };

    return implode('', array_merge([$encodedHeaderList], $encodedCoords));
}

function encodeHeader(int $precision, int $thirdDim, int $thirdDimPrecision)
{
    if ($precision < 0 || $precision > 15) {
        throw new Exception('precision out of range. Should be between 0 and 15');
    }
    if ($thirdDimPrecision < 0 || $thirdDimPrecision > 15) {
        throw new Exception('thirdDimPrecision out of range. Should be between 0 and 15');
    }
    if ($thirdDim < 0 || $thirdDim > 7 || $thirdDim === 4 || $thirdDim === 5) {
        throw new Exception('thirdDim should be between 0, 1, 2, 3, 6 or 7');
    }

    $res = ($thirdDimPrecision << 7) | ($thirdDim << 4) | $precision;
    return encodeUnsignedNumber(1) . encodeUnsignedNumber($res);
}

function encodeUnsignedNumber(float $val)
{
    $res = '';
    $numVal = (float)$val;
    while ($numVal > 0x1F) {
        $pos = ($numVal & 0x1F) | 0x20;
        $pos = (int)$pos;
        $res .= "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"[$pos];
        $numVal >>= 5;
    }
    $numVal = (int)$numVal;
    return $res . "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"[$numVal];
}

function encodeScaledValue(float $value)
{
    $negative = $value < 0;
    $value <<= 1;
    if ($negative) {
        $value = ~$value;
    }

    return encodeUnsignedNumber($value);
}

function getPerpendicularDistanceFull($pt, $lineStart, $lineEnd) {
    if ($lineStart['longitud_gps'] == $lineEnd['longitud_gps'] && $lineStart['latitud_gps'] == $lineEnd['latitud_gps']) {
        return sqrt(pow($pt['longitud_gps'] - $lineStart['longitud_gps'], 2) + pow($pt['latitud_gps'] - $lineStart['latitud_gps'], 2));
    }

    $dx = $lineEnd['longitud_gps'] - $lineStart['longitud_gps'];
    $dy = $lineEnd['latitud_gps'] - $lineStart['latitud_gps'];

    $mag = sqrt($dx * $dx + $dy * $dy);
    $dx /= $mag;
    $dy /= $mag;

    $pvx = $pt['longitud_gps'] - $lineStart['longitud_gps'];
    $pvy = $pt['latitud_gps'] - $lineStart['latitud_gps'];

    $pvdot = $dx * $pvx + $dy * $pvy;

    $dsx = $pvdot * $dx;
    $dsy = $pvdot * $dy;

    $ax = $pvx - $dsx;
    $ay = $pvy - $dsy;

    return sqrt($ax * $ax + $ay * $ay);
}

function douglasPeuckerFull($points, $epsilon) {
    $dmax = 0;
    $index = 0;
    $end = count($points) - 1;

    for ($i = 1; $i < $end; $i++) {
        $d = getPerpendicularDistanceFull($points[$i], $points[0], $points[$end]);
        if ($d > $dmax) {
            $index = $i;
            $dmax = $d;
        }
    }

    if ($dmax > $epsilon) {
        $recResults1 = douglasPeuckerFull(array_slice($points, 0, $index + 1), $epsilon);
        $recResults2 = douglasPeuckerFull(array_slice($points, $index), $epsilon);
        return array_merge(array_slice($recResults1, 0, -1), $recResults2);
    } else {
        return [$points[0], $points[$end]];
    }
}

function decodeHerePolyline(string $encoded) {
    $result = [];

    $encodingTable = array_flip(str_split("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"));
    $index = 0;

    // Primer número (formato flexible) – lo salteamos
    $index++; // version
    $headerValue = decodeUnsignedNumber($encoded, $index, $encodingTable);
    $precision = $headerValue & 15;
    $thirdDim = ($headerValue >> 4) & 7;
    $thirdDimPrecision = ($headerValue >> 7) & 15;
    $factor = pow(10, $precision);

    $lastLat = 0;
    $lastLng = 0;

    while ($index < strlen($encoded)) {
        $deltaLat = decodeScaledValue($encoded, $index, $encodingTable);
        $deltaLng = decodeScaledValue($encoded, $index, $encodingTable);

        $lastLat += $deltaLat;
        $lastLng += $deltaLng;

        $result[] = [
            'latitud_gps' => number_format($lastLat / $factor, 6, '.', ''),
            'longitud_gps' => number_format($lastLng / $factor, 6, '.', '')
        ];

        if ($thirdDim) {
            // Si hay tercera dimensión, salteamos también
            decodeScaledValue($encoded, $index, $encodingTable);
        }
    }

    return $result;
}

function decodeUnsignedNumber($encoded, &$index, $encodingTable) {
    $result = 0;
    $shift = 0;
    while (true) {
        $value = $encodingTable[$encoded[$index++]];
        $result |= ($value & 0x1F) << $shift;
        if ($value < 0x20) break;
        $shift += 5;
    }
    return $result;
}

function decodeScaledValue($encoded, &$index, $encodingTable) {
    $value = decodeUnsignedNumber($encoded, $index, $encodingTable);
    $isNegative = $value & 1;
    $value >>= 1;
    return $isNegative ? ~$value : $value;
}