<?php
require '../../erp_chess/api/vendor/autoload.php';
include_once('../../util_db.php');
include_once('../../services/geocoding/geocoding_util.php');
include_once('../../services/geocoding/geocoding_settings.php');
/*
 *
 */
define("MAIN_USUARIO_FRESENIUS_PRODUCTOS", 5488);
/*
 *
/*
 * MAPPING USUARIOS FRESENIUS
 */
define(
    "MAPPING_USUARIOS_FRESENIUS",
    serialize(
        array(
            '3412A001SKDC' => 5488,
            '3416A007SKDC' => 5488,
            '3417A008ROPT' => 5488,
            '3417A008COPT' => 5488,
            '3412A001BOLI' => 5488,
            '3416A007BOLI' => 5488,
            '3412A001PTEF' => 5488,
            '3412A001RTPT' => 5488,
            '3420A013CCCC' => 7017,
            '3017A008CODR' => 5492,
            '3017A008COBL' => 5492,
            '3017A008ROBL' => 5490,
            '3017A008RODR' => 5490,
            '3017A008RGNO' => 17903,
            '3017A008SABL' => 17903
        )
    )
);
/*
 *
 */

define('URBANO_COLOR','#FF0000');
define('URBANO_MARCA', 'Bultos');
define('AMOUNT_BULK', 40);
define(
    'CORTES_ID',
    serialize(
        array(
            'bultos' => 5,
            'peso'   => 2
        )
    )
);

/*
 * INPUT HANDLING AND LOGGING
 */
$json = file_get_contents('php://input');
$fileName = '/var/www/saas/upload_maestros/'.basename(__FILE__, '.php').'_'.(new DateTime())->format('YmdHis').'.json';
file_put_contents($fileName, $json);
logDatosInterfaces($fileName,basename(__FILE__), MAIN_USUARIO_FRESENIUS_PRODUCTOS);
$data = json_decode($json, TRUE);
if (json_last_error() != JSON_ERROR_NONE) {
    die("Error al parsear el JSON (".json_last_error_msg().")");
}
if ($data['key'] != "FB3PxZbmvnQ2BqK93K3uBxn9g8Wp6RiYonL44tbrPRVffsC40SV34fjuZv5NP5vf") {
    loggear("Key erronea", __FILE__);
    die ("No autorizado");
}
/*
 *
 */

/*
 * SETUP
 */
$dataCollection = collect($data);
$usuariosMap = unserialize(MAPPING_USUARIOS_FRESENIUS);
/*
 *
 */

/*
 * MAIN LOOP
 */
$dataCollection->only('envios')
     ->flatten(1)
     ->map(
     /**
      * @param $envio
      */
         function ($envio) use ($usuariosMap) {
             $constructedUsuarioIdentifier = trim($envio['centro']).trim($envio['pExpedicion']).trim($envio['almacen']);
             $cortesWithId = unserialize(CORTES_ID);
             $idUsuario = $usuariosMap[$constructedUsuarioIdentifier];
             $detalleEnvio = array_shift($envio['detalles']);
             $sumatoryAllWidth = getAllWidthFrom($detalleEnvio);
             $cortes = [
                 'bultos' => $envio['bultos'],
                 'peso'   => $sumatoryAllWidth
             ];

             $cliente = extractClienteFromEnvio($envio);
             $pedido = extractInfoPedidoFromEnvio($detalleEnvio, $envio);
             $parametros = extractParametrosFromEnvio($detalleEnvio);
             $productos = extractProductosFromEnvio($detalleEnvio['subDetalles']);

             $idCliente = handleCliente($cliente, array_keys($cliente), $idUsuario);
             $idPedido = getIdPedido($pedido['codigo_pedido'], $idCliente, $idUsuario);
             $productos = array_map(
                 function ($producto) use ($idUsuario) {
                     $idProducto = getIdProductoByCodigo($producto['codigo'], $idUsuario);
                     if (!$idProducto) {
                         $idProducto = insertProducto(
                             $producto['codigo'],
                             $producto['descripcion'],
                             $idUsuario
                         );
                     }
                     $producto['id_producto'] = $idProducto;

                     return $producto;
                 },
                 $productos
             );
             if ($idPedido) {
                 $mustReInsertEntity = [];
                 $existingPedido = getExistingPedido($idPedido, array_keys($pedido));
                 if ($modifiedPedido = pedidoNeedsUpdate($pedido, $existingPedido)) {
                     updatePedido($idPedido, $modifiedPedido);
                 }
                 $hashes = [
                     'pedidos_cortes' => getHashFromItem($cortes),
                     'pedidos_detalles' => getHashFromItem($detalleEnvio),
                     'pedidos_parametros' => getHashFromItem($parametros),
                 ];
                 $dbHashes = getDbHashedByEntity(
                     $idPedido,
                     ['pedidos_cortes', 'pedidos_detalles', 'pedidos_parametros']
                 );
                 foreach ($hashes as $entity => $hash) {
                     ($hash == $dbHashes[$entity]) ?: $mustReInsertEntity[] = wipeEntity($idPedido, $entity);
                 }
                 if (!empty($mustReInsertEntity)) {
                     foreach ($mustReInsertEntity as $entity) {
                         switch ($entity) {
                             case 'pedidos_cortes':
                                 setAndSaveCortes($cortes, $idPedido, $cortesWithId);
                                 updateEntityHash('pedidos_cortes', $hashes['pedidos_cortes'], $idPedido);
                                 break;
                             case 'pedidos_detalles':
                                 setAndSaveDetalles($productos, $idPedido);
                                 updateEntityHash('pedidos_detalles', $hashes['pedidos_detalles'], $idPedido);
                                 break;
                             case 'pedidos_parametros':
                                 setAndSaveParametros($parametros, $idPedido,$cortes,$idCliente,$idUsuario);
                                 updateEntityHash(
                                     'pedidos_parametros',
                                     $hashes['pedidos_parametros'],
                                     $idPedido
                                 );
                                 break;
                         }
                     }
                 }
             } else {
                 $idPedido = setAndSavePedido($pedido, $idCliente, $idUsuario);
                 setAndSaveCortes($cortes, $idPedido, $cortesWithId);
                 setAndSaveDetalles($productos, $idPedido);
                 setAndSaveParametros($parametros, $idPedido,$cortes,$idCliente,$idUsuario);
                 $hashes = [
                     'pedidos_cortes' => getHashFromItem($cortes),
                     'pedidos_detalles' => getHashFromItem($detalleEnvio),
                     'pedidos_parametros' => getHashFromItem($parametros),
                 ];
                 updateEntityHash('pedidos_cortes', $hashes['pedidos_cortes'], $idPedido);
                 updateEntityHash('pedidos_detalles', $hashes['pedidos_detalles'], $idPedido);
                 updateEntityHash('pedidos_parametros', $hashes['pedidos_parametros'], $idPedido);
             }
         }
     );
/*
 * END MAIN LOOP
 */

/*
 * FUNCTIONS
 */

/**
 * @param $envio
 * @return array
 */
function extractClienteFromEnvio($envio)
{
    return [
        'codigo' => trim($envio['cDestinatario']),
        'razon_social' => trim($envio['destinatario']),
        'domicilio' => trim($envio['domicilioEntrega']).', '.trim($envio['ciudad']).', '.trim($envio['provincia']).', '.
            'Argentina',
        'telefono_contacto' => $envio['telefono'] ?: 'No Informado',
    ];
}

/**
 * @param $detalleEnvio
 * @param $envio
 * @return array
 */
function extractInfoPedidoFromEnvio($detalleEnvio, $envio)
{
    $convertDate = function ($date) {
        $datetime = DateTime::createFromFormat('d/m/Y', $date);

        return $datetime->format('Y-m-d');
    };
    $pedido = [
        'codigo_pedido' => trim($detalleEnvio['entregaSAP']),
        'fecha_pedido' => $convertDate($detalleEnvio['fecha']),
        'agrupacion' => $envio['pExpedicion']
    ];

    return $pedido;
//    return (strpos($envio['almacen'], 'SKDC') === false) ? $pedido : array_merge(
//        $pedido,
//        ['agrupacion' => $envio['pExpedicion']]
//    );
}

/**15000
 * @param $envio
 * @return array
 */
function extractParametrosFromEnvio($envio)
{
    return [
        'remito' => trim($envio['remito']),
    ];
}

/**
 * @param $envioSubdetalles
 * @return array
 */
function extractProductosFromEnvio($envioSubdetalles)
{
    $envioSubdetallesCollection = collect($envioSubdetalles);

    return $envioSubdetallesCollection->map(
        function ($item) {
            return [
                'codigo' => trim($item['cMaterial']),
                'descripcion' => trim($item['material']),
                'cantidad' => trim($item['cantidad']),
            ];
        }
    )
                                      ->all();
}

/**
 * @param $cliente
 * @param $clienteFieldsWithDbMapping
 * @param $idUsuario
 * @return int
 */
function handleCliente($cliente, $clienteFieldsWithDbMapping, $idUsuario)
{
    list($idCliente, $habilitado) = array_values(clienteExists($cliente['codigo'], $idUsuario));
    if ($idCliente) {
        ($habilitado == 1) ?: habilitarCliente($idCliente);
        $existingCliente = getExistingCliente($idCliente, $clienteFieldsWithDbMapping);
        if ($modifiedCliente = clienteNeedsUpdate($cliente, $existingCliente)) {
            updateCliente($modifiedCliente, $idCliente);

            return $idCliente;
        }

        return $idCliente;
    } else {
        $cliente = setupClienteForInsert($cliente, $idUsuario);
        $idCliente = insertCliente($cliente);

        return $idCliente;
    }
}

/**
 * @param $codigoCliente
 * @param $idUsuario
 * @return array|bool
 */
function clienteExists($codigoCliente, $idUsuario)
{

    $codigoCliente = mysql_real_escape_string($codigoCliente);
    $queryResult = mysql_query(
        "SELECT c.id, c.habilitado
                                FROM clientes c
                                WHERE c.codigo = '$codigoCliente'
                                AND c.habilitado = 1
                                AND c.id_usuario = $idUsuario LIMIT 1"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return false;
    }

    return mysql_fetch_assoc($queryResult);
}

/**
 * @param $idCliente
 * @param $fields
 * @return array
 */
function getExistingCliente($idCliente, $fields)
{
    $queryResult = mysql_query(
        "SELECT ".implode(',', $fields)." FROM clientes
                                WHERE id = $idCliente"
    );

    return mysql_fetch_assoc($queryResult);
}

/**
 * @param $dataCliente
 * @param $clienteActual
 * @return array|bool|Closure
 */
function clienteNeedsUpdate($dataCliente, $clienteActual)
{
    $noUpdateZeroCoordinate = function ($value, $key) {
        if ($key == 'latitud') {
            return ($value) ? true : false;
        }
        if ($key == 'longitud') {
            return ($value) ? true : false;
        }

        return true;
    };
    if (!arrayHaveSameKeys($dataCliente, $clienteActual) || !$camposModificados = array_filter(
            getArrayModifiedKeys($dataCliente, $clienteActual),
            $noUpdateZeroCoordinate,
            ARRAY_FILTER_USE_BOTH
        )
    ) {
        return false;
    }
    $ahora = date('Y-m-d H:i:s');
    $camposModificados['update_timestamp'] = $ahora;

    return $camposModificados;
}

/**
 * @param $new
 * @param $original
 * @return array|bool
 */
function getArrayModifiedKeys($new, $original)
{
    $compareStrings = function ($string1, $string2) {
        return (strcmp((string)$string1, (string)$string2) === 0) ? true : false;
    };
    $camposModificados = [];
    foreach ($original as $key => $value) {
        if (!$compareStrings($value, $new[$key])) {
            $camposModificados[$key] = $new[$key];
        }
    }
    if (empty($camposModificados)) {
        return false;
    }

    return $camposModificados;
}

/**
 * @param $camposModificados
 * @param $idCliente
 */
function updateCliente($camposModificados, $idCliente)
{
    $query = "UPDATE clientes SET";
    $comma = " ";
    foreach ($camposModificados as $key => $val) {
        if (!empty($val)) {
            $query .= $comma.$key." = '".mysql_real_escape_string(trim($val))."'";
            $comma = ", ";
        }
    }
    $query = $query." WHERE id = '".$idCliente."'";
    mysql_query($query);
}

/**
 * @param $idCliente
 */
function habilitarCliente($idCliente)
{
    $ahora = date('Y-m-d H:i:s');
    mysql_query(
        "UPDATE clientes SET habilitado = 1, update_timestamp = '$ahora',estado = 'HABILITADO' WHERE id = $idCliente"
    );
}

/**
 * @param $cliente
 * @param $idUsuario
 * @return array
 */
function setupClienteForInsert($cliente, $idUsuario)
{
    $cliente['latitud'] = 0;
    $cliente['longitud'] = 0;
    if (!($cliente['latitud'] && $cliente['longitud'])) {
        $geocodeResult = geocode_here_single_string($cliente['domicilio']);
        if (gecode_google_is_success($geocodeResult['AD_RESULT'][0])) {
            $cliente['latitud'] = $geocodeResult['AD_RESULT'][0]['latitud'];
            $cliente['longitud'] = $geocodeResult['AD_RESULT'][0]['longitud'];
        }
    }
    $fixedFields = array(
        'domicilio_original' => $cliente['domicilio'],
        'update_timestamp' => (new DateTime())->format('Y-m-d H:i:s'),
        'id_usuario' => $idUsuario,
    );

    return array_merge($cliente, $fixedFields);
}

/**
 * @param $cliente
 * @return int
 */
function insertCliente($cliente)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($cliente));
    $value = $cleanStrings(array_values($cliente));
    $query = "INSERT INTO clientes ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    $success = mysql_query($query);

    return ($success) ? mysql_insert_id() : 0;
}

/**
 * @param $codigoPedido
 * @param $idCliente
 * @param $idUsuario
 * @return int|string
 */
function getIdPedido($codigoPedido, $idCliente, $idUsuario)
{

    $codigoPedido = mysql_real_escape_string($codigoPedido);
    $queryResult = mysql_query(
        "SELECT id
                                FROM pedidos p
                                WHERE p.id_cliente = $idCliente
                                AND p.codigo_pedido = '$codigoPedido'
                                AND p.id_usuario = $idUsuario"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return 0;
    }

    return mysql_result($queryResult, 0, 0);
}

/**
 * @param $idPedido
 * @param $fields
 * @return array
 */
function getExistingPedido($idPedido, $fields)
{
    $queryResult = mysql_query(
        "SELECT ".implode(',', $fields)."
                                FROM pedidos
                                WHERE id = $idPedido"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return array();
    }

    return mysql_fetch_assoc($queryResult);
}

/**
 * @param $idPedido
 * @return array
 */
function getExistingPedidosCortesRecord($idPedido)
{
    $cortesPedido = [];
    $queryResult = mysql_query(
        "SELECT pc.id_corte, pc.valor
                                FROM pedidos_cortes pc
                                WHERE pc.id_pedido = $idPedido"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return $cortesPedido;
    }
    while ($row = mysql_fetch_assoc($queryResult)) {
        $cortesPedido[] = $row;
    }

    return $cortesPedido;
}

/**
 * @param $idPedido
 * @return array
 */
function getExistingPedidosParametrosRecord($idPedido)
{
    $parametrosPedido = [];
    $queryResult = mysql_query(
        "SELECT pp.parametro, pp.valor
                                FROM pedidos_parametros pp
                                WHERE pp.id_pedido = $idPedido
                                ORDER BY pp.parametro DESC"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return $parametrosPedido;
    }
    while ($row = mysql_fetch_assoc($queryResult)) {
        $parametrosPedido[] = $row;
    }

    return $parametrosPedido;
}

/**
 * @param $idPedido
 * @return array
 */
function getExistingPedidosDetallesRecord($idPedido)
{
    $pedidosDetallesPedido = [];
    $queryResult = mysql_query(
        "SELECT pd.codigo_producto as codigo, pd.cantidad
                                FROM pedidos_detalles pd
                                WHERE pd.id_pedido = $idPedido
                                ORDER BY pd.codigo_producto DESC"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return $pedidosDetallesPedido;
    }
    while ($row = mysql_fetch_assoc($queryResult)) {
        $pedidosDetallesPedido[] = $row;
    }

    return $pedidosDetallesPedido;
}

/**
 * @param $newPedidoData
 * @param $existingPedidoData
 * @return array|bool
 */
function pedidoNeedsUpdate($newPedidoData, $existingPedidoData)
{
    if (!arrayHaveSameKeys($newPedidoData, $existingPedidoData) ||
        !$camposModificados = getArrayModifiedKeys($newPedidoData, $existingPedidoData)
    ) {
        return false;
    }

    return $camposModificados;
}

/**
 * @param $array1
 * @param $array2
 * @return bool
 */
function arrayHaveSameKeys($array1, $array2)
{
    return !array_diff_key($array1, $array2) && !array_diff_key($array2, $array1);
}

/**
 * @param $idPedido
 * @param $camposModificados
 */
function updatePedido($idPedido, $camposModificados)
{
    $query = "UPDATE pedidos SET";
    $comma = " ";
    foreach ($camposModificados as $key => $val) {
        if (!empty($val)) {
            $query .= $comma.$key." = '".mysql_real_escape_string(trim($val))."'";
            $comma = ", ";
        }
    }
    $query = $query." WHERE id = ".$idPedido;
    mysql_query($query);
}

/**
 * @param $newPedidoData
 * @param $existingPedidoParametrosData
 * @param $parametros
 * @return array
 */
function pedidoParametrosNeedsUpdate($newPedidoData, $existingPedidoParametrosData, $parametros)
{
    $pedidoParametrosModificados = [];
    $newPedidoParametros = [];
    foreach ($parametros as $parametro) {
        $newParametro = [];
        $newParametro['parametro'] = $parametro;
        $newParametro['valor'] = $newPedidoData[$parametro];
        $newPedidoParametros[] = $newParametro;
    }
    for ($i = 0; $i < count($parametros); $i++) {
        if ($newPedidoParametros[$i]['valor'] != $existingPedidoParametrosData[$i]['valor']) {
            $pedidoParametrosModificados[] = $newPedidoParametros[$i];
        }
    }

    return $pedidoParametrosModificados;
}

/**
 * @param $idPedido
 * @param $camposModificados
 */
function updatePedidoParametros($idPedido, $camposModificados)
{
    $query = "UPDATE pedidos_parametros SET";
    $comma = " ";
    foreach ($camposModificados as $key => $val) {
        if (!empty($val)) {
            $query .= $comma.$key." = '".mysql_real_escape_string(trim($val))."'";
            $comma = ", ";
        }
    }
    $query = $query." WHERE id_pedido = ".$idPedido." AND parametro = ".$camposModificados['parametro'];
    mysql_query($query);
}

/**
 * @param $newPedidoData
 * @param $existingPedidoCortesData
 * @param $cortes
 * @return array
 */
function pedidoCortesNeedsUpdate($newPedidoData, $existingPedidoCortesData, $cortes)
{
    $pedidoCortesModificados = [];
    $newPedidoCortes = [];
    foreach ($cortes as $key => $value) {
        $corte = [];
        $corte['id_corte'] = $value;
        $corte['valor'] = $newPedidoData[$key];
        $newPedidoCortes[] = $corte;
    }
    for ($i = 0; $i < 3; $i++) {
        if ($newPedidoCortes[$i]['valor'] != $existingPedidoCortesData[$i]['valor']) {
            $pedidoCortesModificados[] = $newPedidoCortes[$i];
        }
    }

    return $pedidoCortesModificados;
}

/**
 * @param $idPedido
 * @param $camposModificados
 */
function updatePedidoCorte($idPedido, $camposModificados)
{
    $query = "UPDATE pedidos_cortes SET";
    $comma = " ";
    foreach ($camposModificados as $key => $val) {
        if (!empty($val)) {
            $query .= $comma.$key." = '".mysql_real_escape_string(trim($val))."'";
            $comma = ", ";
        }
    }
    $query = $query." WHERE id_pedido = ".$idPedido." AND id_corte = ".$camposModificados['id_corte'];
    mysql_query($query);
}

/**
 * @param $newPedidoData
 * @param $existingPedidoDetalles
 * @param $productosCodigo
 * @return array
 */
function pedidoDetalleNeedsUpdate($newPedidoData, $existingPedidoDetalles, $productosCodigo)
{
    $pedidoParametrosModificados = [];
    $newPedidoParametros = [];
    foreach ($productosCodigo as $codigo) {
        $newParametro = [];
        $newParametro['codigo_producto'] = $codigo;
        $newParametro['cantidad'] = $newPedidoData[$codigo];
        $newPedidoParametros[] = $newParametro;
    }
    for ($i = 0; $i < count($productosCodigo); $i++) {
        if ($newPedidoParametros[$i]['cantidad'] != $existingPedidoDetalles[$i]['cantidad']) {
            $pedidoParametrosModificados[] = $newPedidoParametros[$i];
        }
    }

    return $pedidoParametrosModificados;
}

/**
 * @param $item
 * @return string
 */
function getHashFromItem($item)
{
    return sha1(serialize($item));
}

/**
 * @param $idPedido
 * @param $entities
 * @return array
 */
function getDbHashedByEntity($idPedido, $entities)
{
    $hashesByEntity = [];
    $query = "SELECT parametro, valor
              FROM pedidos_parametros
              WHERE id_pedido = %s
              AND parametro IN (%s)";
    $queryResult = mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                "'".implode("','", $entities)."'",
            ]
        )
    );
    if (mysql_num_rows($queryResult) == 0) {
        return [];
    }
    while ($row = mysql_fetch_assoc($queryResult)) {
        $hashesByEntity[$row['parametro']] = $row['valor'];
    }

    return $hashesByEntity;
}

/**
 * @param $idPedido
 * @param $entity
 * @return mixed
 */
function wipeEntity($idPedido, $entity)
{
    mysql_query("DELETE FROM $entity WHERE id_pedido = $idPedido");

    return $entity;
}

/**
 * @param $pedidoData
 * @param $idCliente
 * @param $idUsuario
 * @return mixed
 */
function setupPedidoForInsert($pedidoData, $idCliente, $idUsuario)
{
    $ahora = date('Y-m-d H:i:s');
    $pedido = $pedidoData;
    $pedido['id_cliente'] = $idCliente;
    $pedido['fecha_insercion'] = $ahora;
    $pedido['id_usuario'] = $idUsuario;

    return $pedido;
}

/**
 * @param $pedido
 * @return int
 */
function insertPedido($pedido)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedido));
    $value = $cleanStrings(array_values($pedido));
    $query = "INSERT INTO pedidos ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
    $idPedido = mysql_insert_id();

    return $idPedido;
}

/**
 * @param $pedidoData
 * @param $idPedido
 * @param $cortes
 * @return array
 */
function setupPedidoCortesForInsert($pedidoData, $idPedido, $cortes)
{
    $pedidoCortes = [];
    foreach ($cortes as $key => $value) {
        $corte = [];
        $corte['id_pedido'] = $idPedido;
        $corte['id_corte'] = $value;
        $corte['valor'] = $pedidoData[$key];
        $pedidoCortes[] = $corte;
    }

    return $pedidoCortes;
}

/**
 * @param $pedidoCortesData
 */
function insertarPedidoCortes($pedidoCortesData)
{
    foreach ($pedidoCortesData as $pedidoCorte) {
        insertPedidoCorte($pedidoCorte);
    }
}

/**
 * @param $pedidoCorte
 */
function insertPedidoCorte($pedidoCorte)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedidoCorte));
    $value = $cleanStrings(array_values($pedidoCorte));
    $query = "INSERT INTO pedidos_cortes ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
}

/**
 * @param $pedidoData
 * @param $idPedido
 * @param $parametros
 * @return array
 */
function setupPedidoParametrosForInsert($pedidoData, $idPedido, $parametros)
{
    $pedidoParametros = [];
    foreach ($parametros as $parametro) {
        $newParametro = [];
        $newParametro['id_pedido'] = $idPedido;
        $newParametro['parametro'] = $parametro;
        $newParametro['valor'] = $pedidoData[$parametro];
        $pedidoParametros[] = $newParametro;
    }

    return $pedidoParametros;
}

/**
 * @param $pedidoParametrosData
 */
function insertarPedidoParametros($pedidoParametrosData)
{
    foreach ($pedidoParametrosData as $pedidoParametro) {
        insertPedidoParametro($pedidoParametro);
    }
}

/**
 * @param $pedidoParametros
 */
function insertPedidoParametro($pedidoParametros)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedidoParametros));
    $value = $cleanStrings(array_values($pedidoParametros));
    $query = "INSERT INTO pedidos_parametros ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
}

/**
 * @param $cortes
 * @param $idPedido
 * @param $cortesWithId
 */
function setAndSaveCortes($cortes, $idPedido, $cortesWithId)
{
    insertarPedidoCortes(setupPedidoCortesForInsert($cortes, $idPedido, $cortesWithId));
}

/**
 * @param $productos
 * @param $idPedido
 */
function setAndSaveDetalles($productos, $idPedido)
{
    insertarPedidoDetalles(setupPedidoDetallesForInsert($productos, $idPedido));
}

/**
 * @param $parametros
 * @param $idPedido
 */
function setAndSaveParametros($parametros, $idPedido,$cortes,$idCliente,$idUsuario)
{
    $dia = date('Y-m-d');
    $bultos = 0;
    $r_bultos = mysql_query("SELECT pc.valor
                                  FROM pedidos p 
                                  INNER JOIN pedidos_cortes pc ON pc.id_pedido = p.id
                                  WHERE p.id_usuario = $idUsuario
                                  AND p.fecha_insercion BETWEEN '$dia 00:00:00' AND '$dia 23:59:59'
                                  AND pc.id_corte = '5' 
                                  AND p.id_cliente = $idCliente");
    while ($row = mysql_fetch_assoc($r_bultos)){
        $bultos = $bultos + intval($row['valor'],10);
    }
    $bultos = $bultos+$cortes['bultos'];
    $mustAssignColor = ($bultos >= AMOUNT_BULK) ? true : false;
    (!$mustAssignColor) ?: saveParametroColor($idPedido);
    insertarPedidoParametros(setupPedidoParametrosForInsert($parametros, $idPedido, array_keys($parametros)));
}

/**
 * @param $pedido
 * @param $idCliente
 * @param $idUsuario
 * @return int
 */
function saveParametroColor($idPedido)
{
    $query = "INSERT INTO pedidos_parametros (id_pedido, parametro, valor) VALUES ('%s', '%s', '%s')";
    mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                'URBANO_COLOR',
                URBANO_COLOR,
            ]
        )
    );
    mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                'URBANO_MARCA',
                URBANO_MARCA,
            ]
        )
    );
}
function setAndSavePedido($pedido, $idCliente, $idUsuario)
{
    $pedido = setupPedidoForInsert($pedido, $idCliente, $idUsuario);
    $idPedido = insertPedido($pedido);

    return $idPedido;
}

/**
 * @param $codigoProducto
 * @param $idUsuario
 * @return int|string
 */
function getIdProductoByCodigo($codigoProducto, $idUsuario)
{
    $query = "SELECT id FROM maestro_productos WHERE codigo = '%s' AND id_usuario = '%s'";
    $id = ($queryResult = mysql_query(sprintf($query, trim($codigoProducto), $idUsuario))) &&
    mysql_num_rows($queryResult) ? mysql_result($queryResult, 0, 0) : 0;

    return $id;
}

/**
 * @param $codigoProducto
 * @param $descripcion
 * @param $idUsuario
 * @return int
 */
function insertProducto($codigoProducto, $descripcion, $idUsuario)
{
    $queryInsertProducto = "INSERT INTO maestro_productos (codigo, descripcion, id_usuario, id_estado_producto) VALUES ('%s', '%s','%s', '%s')";

    return ($result = mysql_query(
        sprintf($queryInsertProducto, $codigoProducto, $descripcion, $idUsuario, 1)
    )) ? mysql_insert_id() : 0;
}

/**
 * @param $productos
 * @param $idPedido
 * @return array
 */
function setupPedidoDetallesForInsert($productos, $idPedido)
{
    $pedidoDetalles = [];
    foreach ($productos as $producto) {
        $detalle = [];
        $detalle['id_pedido'] = $idPedido;
        $detalle['id_producto'] = $producto['id_producto'];
        $detalle['cantidad'] = $producto['cantidad'];
        $pedidoDetalles[] = $detalle;
    }

    return $pedidoDetalles;
}

/**
 * @param $pedidoDetallesData
 */
function insertarPedidoDetalles($pedidoDetallesData)
{
    foreach ($pedidoDetallesData as $pedidoDetalle) {
        insertPedidoDetalle($pedidoDetalle);
    }
}

/**
 * @param $pedidoDetalle
 */
function insertPedidoDetalle($pedidoDetalle)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedidoDetalle));
    $value = $cleanStrings(array_values($pedidoDetalle));
    $query = "INSERT INTO pedidos_detalles ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
}

/**
 * @param $array
 * @param $keysToKeep
 * @return array
 */
function keepArrayKeys($array, $keysToKeep)
{
    return array_filter(
        $array,
        function ($item) use ($keysToKeep) {
            return in_array($item, $keysToKeep);
        },
        ARRAY_FILTER_USE_KEY
    );
}

/**
 * @param $entity
 * @param $hash
 * @param $idPedido
 */
function updateEntityHash($entity, $hash, $idPedido)
{
    $query = "INSERT INTO pedidos_parametros (id_pedido, parametro, valor)
             VALUES ('%s', '%s', '%s') ON DUPLICATE KEY UPDATE valor = VALUES(valor)";
    mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                $entity,
                $hash,
            ]
        )
    );
}

/**
 * @param $logData
 * @param $logName
 * @param $idUsuario
 */
function logDatosInterfaces($logData, $logName, $idUsuario)
{
    $log_interfaces_ts = date("Y-m-d H:i:s");
    mysql_query(
        "INSERT INTO log_interfaces (id_usuario, interfaz, fecha_ejecucion, valor) 
                VALUES($idUsuario, '$logName', '$log_interfaces_ts', '$logData')"
    );
}

/**
 * @param $detalles Es el detalle del pedido
 * @return float devuelvo la sumatoria de todos los pesos de cada producto
 */
function getAllWidthFrom($detalles){

    $peso = 0;

    foreach($detalles['subDetalles'] as $subDetalle){
        $peso += $subDetalle['peso'];
    }

    return floatval($peso);
}

/*
 *
 */

