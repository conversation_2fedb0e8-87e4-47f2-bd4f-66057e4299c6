<?php
require_once 'db/camiones.php';
include_once('../util_html.php');
include_once('../util_db.php');
include_once('../util_menu.php');
include_once('../util_session.php');
include_once('../integration_urls.php');
include_once('../auth_system_urls.php');
include_once('./dynamic_routing_modals_envs.php');
//error_reporting(E_ALL);
//ini_set('display_errors', 1);
manejar_sesion();
$usuario_id = $_SESSION['usuario_id'];
rastrear('monitor2.php', '', $usuario_id);
$apli = get_id_aplicativo();
$website = get_website_url();
$hoy = date("Y-m-d");
$hoy_ddmmyy = date("d-m-Y");

$filtroZonas = call_user_func(function ($idUsuario) {
    return (get_usuario_has_parametro($idUsuario, 'FILTRO_ZONAS_INDIVIDUAL')) ? 'Zonas' : 'Tipo zonas';
}, $usuario_id);

$filtroTurnos = call_user_func(function ($idUsuario) {
    return get_usuario_has_parametro($idUsuario, 'FILTRO_TURNOS');
}, $usuario_id);

$router_version = get_parametro_usuario($usuario_id, 'CHAMELEON_ROUTER_VERSION')
    ? get_parametro_usuario($usuario_id, 'CHAMELEON_ROUTER_VERSION')
    : 'routing';

$routing_mode = usuario_parametro($usuario_id, 'CHAMELEON_ROUTING_MODE')
    ? usuario_parametro($usuario_id, 'CHAMELEON_ROUTING_MODE')
    : 'truck';

$flash_uploader_mode = get_parametro_usuario($usuario_id, 'FLASH_UPLOADER_MODE')
    ? get_parametro_usuario($usuario_id, 'FLASH_UPLOADER_MODE')
    : 'SIMPLE';

$url_import['url_cargas_upload'] =  get_parametro_usuario($usuario_id, 'URL_CARGAS_UPLOAD');
$url_import['url_cargas_download'] =  get_parametro_usuario($usuario_id, 'URL_CARGAS_DOWNLOAD');
$url_import['url_cargas_upload_espontaneos'] =   get_parametro_usuario($usuario_id, 'URL_CARGAS_UPLOAD_ESPONTANEOS');

$compactacion_enwis = get_parametro_usuario($usuario_id, "COMPACTACION_ENWIS");
if (!$compactacion_enwis) {
    $compactacion_enwis = 0;
}

$invertirOrdenPlanificacion = get_parametro_usuario($usuario_id, "CHAMELEON_INVERTIR_ORDEN_PLANIFICACION");
$importarPedidosCustomWs = (bool)get_usuario_has_parametro($usuario_id, "CHAMELEON_IMPORTAR_PEDIDOS_CUSTOM_WS");
$ocultarImportar = (bool)get_usuario_has_parametro($usuario_id, "CHAMELEON_OCULTAR_IMPORTAR");

$flash = get_parametro_usuario($usuario_id, "QUADMINDS_FLASH");
$chameleon_no_programados_auto = get_parametro_usuario($usuario_id, "CHAMELEON_NO_PROGRAMADOS_AUTO");
$chameleon_filter_get_pedidos = get_usuario_has_parametro($usuario_id, "CHAMELEON_FILTER_GET_PEDIDOS");

$pedidos_manuales_corporate = get_parametro_usuario($usuario_id, "PEDIDOS_MANUALES_CORPORATE")
    ? get_parametro_usuario($usuario_id, "PEDIDOS_MANUALES_CORPORATE")
    : 0;
$no_select_all_order_tags = (bool)get_parametro_usuario($usuario_id, 'CHAMELEON_NO_SELECT_ALL_ORDER_TAGS');

$enableReprogramacion = get_parametro_usuario($usuario_id, "ENABLE_REPROGRAMACION");
$enableOrdersTemplate = get_parametro_usuario($usuario_id, "ENABLE_ORDERSTEMPLATE");

$enableIntegrations = get_parametro_usuario($usuario_id, "EXISTS_IN_AUTH_SYSTEM");
$fuerzaVentas = get_parametro_usuario($usuario_id, "PEDIDOS_FUERZA_VENTAS");

$groupByDistance = usuario_parametro($usuario_id, "CHAMELEON_GROUP_BY_DISTANCE");
$minServiceTime = usuario_parametro($usuario_id, "DEMORA_CLIENTE");
$singleRouteOptimizationObjective = usuario_parametro($usuario_id, "CHAMELEON_SINGLE_ROUTE_OBJECTIVE") ? usuario_parametro($usuario_id, "CHAMELEON_SINGLE_ROUTE_OBJECTIVE") : "Distancias";
$optimizationWaitingPenalty = usuario_parametro($usuario_id, "CHAMELEON_OPTIMIZATION_WAITING_PENALTY") ? usuario_parametro($usuario_id, "CHAMELEON_OPTIMIZATION_WAITING_PENALTY") : 0;
$routeCost = get_parametro_usuario($usuario_id, "ROUTE_COST");

$enablePublishingModal = get_parametro_usuario($usuario_id, "ENABLE_PUBLISHING_MODAL");
$enableDynamicRoutingModal = get_parametro_usuario($usuario_id, "ENABLE_DYNAMIC_ROUTING_MODAL");
$dynamicRoutingModalsUrl = isProduction()? DYNAMIC_ROUTING_MODALS_URL: DYNAMIC_ROUTING_MODALS_TEST_URL;
$dynamicRoutingModalScriptsUrl = isProduction()? DYNAMIC_ROUTING_MODAL_SCRIPTS_URL : DYNAMIC_ROUTING_MODAL_SCRIPTS_TEST_URL;

$authServiceUrl = isProduction() ? AUTH_SERVICE_URL : AUTH_SERVICE_TEST_URL;
$integrationsServiceUrl = isProduction() ? INTEGRATIONS_SERVICE_URL : INTEGRATIONS_SERVICE_TEST_URL;
$dias_trial = null;
if ($flash) {
    $r_trial = mysql_query("SELECT fecha_vencimiento FROM usuarios_trial WHERE id_usuario = $usuario_id");
    if (mysql_num_rows($r_trial)) {
        $fecha_vencimiento = mysql_result($r_trial, 0);
        $datediff = strtotime($fecha_vencimiento) - time();
        $dias_trial = round($datediff / (60 * 60 * 24)) + 1;
    }
}
?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>

<head>
    <title>QuadMinds</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <link rel="icon" href="/img/icon_quadminds.ico" type="image/x-icon" />
    <link href="https://cdn.jsdelivr.net/npm/material-components-web@11.0.0/dist/material-components-web.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://stackpath.bootstrapcdn.com/bootstrap/3.3.1/css/bootstrap.min.css" />
    <link rel="stylesheet" type="text/css" href="/css/bootstrap-timepicker.min.css" />
    <link rel="stylesheet" type="text/css" href="css/spin.css" />
    <link rel="stylesheet" type="text/css" href="/css/jquery-ui-1.11.4.css" />
    <link rel="stylesheet" type="text/css" href="css/jquery.comiseo.daterangepicker.css" />
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.15/css/bootstrap-multiselect.css" />
    <link rel="stylesheet" type="text/css" href="/css/glyphicons.css" />
    <link rel="stylesheet" type="text/css" href="/css/bootstrap-select.min.css" />
    <link rel="stylesheet" type="text/css" href="css/chameleon.css" />
    <link rel="stylesheet" type="text/css" href="/css/datatables/jquery.dataTables.bootstrap.css">
    <link rel="stylesheet" type="text/css" href="css/bootstrap-toggle.min.css">
    <link rel="stylesheet" type="text/css" href="css/bootstrap-toggle.min.css">
    <link rel="stylesheet" type="text/css" href="css/fill.css?v=1" />
    <link rel="stylesheet" type="text/css" href="css/geocode-dialog.css?v=1" />
    <link rel="stylesheet" type="text/css" href="/css/font-awesome.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.5/css/intlTelInput.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Round">
    <script src="https://cdn.jsdelivr.net/npm/material-components-web@11.0.0/dist/material-components-web.js"></script>
    <link rel="stylesheet" href="css/amsify.suggestags.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/mdbassit/Coloris@v0.10.2/dist/coloris.min.css" />
    <link rel="stylesheet" type="text/css" href="css/filters-toolbar.css?v=1" />
    <link rel="stylesheet" type="text/css" href="css/markers/animations.css" />
    <link rel="stylesheet" type="text/css" href="css/markers/cortes-marker.css" />
    <link rel="stylesheet" type="text/css" href="css/markers/channel-marker.css" />
    <link rel="stylesheet" type="text/css" href="css/markers/poi-marker.css" />
    <link rel="stylesheet" type="text/css" href="css/map-toolbar.css" />
    <link rel="stylesheet" type="text/css" href="css/options-toolbar.css" />
    <link rel="stylesheet" type="text/css" href="css/monorepo-modals.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@100;300;400;500;600;700&display=swap" rel="stylesheet">
    <script type="text/javascript" src="/js/jquery.min.js"></script>
    <script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDx6VPu5_OR99X2k7jz5OVN9BpJ9nHqikc&libraries=places,drawing,geometry&map_ids=8345d9e0619bd4a1,bed23118bc12e505"></script>
    <script type="text/javascript" src="https://stackpath.bootstrapcdn.com/bootstrap/3.3.1/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.27.0/moment.min.js"></script>
    <script type="text/javascript" src="/js/moment-timezone-with-data.min.js"></script>
    <script type="text/javascript" src="/js/bootstrap-timepicker.min.js"></script>
    <script>
        var _tooltip = jQuery.fn.tooltip;
    </script>
    <script type="text/javascript" src="/js/jquery-ui-1.11.4.min.js"></script>
    <script>
        jQuery.fn.tooltip = _tooltip;
    </script>
    <script type="text/javascript" src="/js/containsLatLng.js"></script>
    <script type="text/javascript" src="/js/ajaxfileupload.js"></script>
    <script type="text/javascript" src="js/jquery.comiseo.daterangepicker.min.js"></script>
    <script type="text/javascript" src="/js/jquery.ui.datepicker-<?= _("es") ?>.js"></script>
    <script type="text/javascript" src="/js/oms.js"></script>
    <script type="text/javascript" src="js/infobox.js"></script>
    <script type="text/javascript" src="/scripts/functions.js"></script>
    <script type="text/javascript" src="js/geocode_dialog_functions.js?v=2"></script>
    <script type="text/javascript" src="js/filters-toolbar.js"></script>
    <script type="text/javascript" src="js/markers/advanced-marker.js"></script>
    <script type="text/javascript" src="js/markers/cortes-marker.js"></script>
    <script type="text/javascript" src="js/map-widgets.js"></script>
    <script type="text/javascript" src="js/view-list-devices.js"></script>
    <script type="text/javascript" src="js/map-toolbar.js"></script>
    <script type="text/javascript" src="js/options-toolbar.js"></script>
    <script type="text/javascript" src="js/chameleon_functions.js?v=2"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-multiselect/0.9.15/js/bootstrap-multiselect.js"></script>
    <script type="text/javascript" src="js/spin.umd.js"></script>
    <script type="text/javascript" src="/js/jquery.runner-min.js"></script>
    <script type="text/javascript" src="/js/jquery.hoverIntent.minified.js"></script>
    <script type="text/javascript" src="/js/bootstrap-select.min.js"></script>
    <script type="text/javascript" src="/js/FileSaver.min.js"></script>
    <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/downloadjs/1.4.8/download.min.js"></script>
    <script type="text/javascript" src="js/jquery.amsify.suggestags.js"></script>
    <?php if (!$flash) : ?>
        <script type="text/javascript" src="js/file-input.js"></script>
    <?php endif ?>
    <script type="text/javascript" src="js/debounce.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.0/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.5/js/intlTelInput.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/libphonenumber-js@1.9.2/bundle/libphonenumber-min.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript" src="/js/datatables/jquery.dataTables.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript" src="/js/datatables/jquery.dataTables.bootstrap.js"></script>
    <script type="text/javascript" charset="utf-8" language="javascript" src="js/bootstrap-toggle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mousetrap/1.6.5/mousetrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/mdbassit/Coloris@v0.10.2/dist/coloris.min.js"></script>
    <script type="module" src="/js/here_polyline.js"></script>
    <script type="module" src="js/routing.js"></script>
    <?php if (isProduction()) : ?>
        <script async type="text/javascript" src="https://app.quadminds.com/webcomponents/optimizer/add-drivers-dialog.js"></script>
        <script async type="text/javascript" src="https://app.quadminds.com/webcomponents/optimizer/vendor.js"></script>
        <script async type="text/javascript" src="https://platform.quadminds.com/webcomponents/order-templates/main.js"></script>
        <script async type="text/javascript" src="https://platform.quadminds.com/webcomponents/order-templates/vendor.js"></script>
        <script async type="text/javascript" src="https://app.quadminds.com/webcomponents/optimizer/zones-interface.js"></script>
        <script async type="text/javascript" src="https://platform.quadminds.com/webcomponents/order-list/main.js"></script>
        <script async type="text/javascript" src="https://platform.quadminds.com/webcomponents/order-list/vendor.js"></script>
    <?php else : ?>
        <script async type="text/javascript" src="https://app-test.quadminds.com/webcomponents-test/optimizer/add-drivers-dialog.js"></script>
        <script async type="text/javascript" src="https://app-test.quadminds.com/webcomponents-test/optimizer/vendor.js"></script>
        <script async type="text/javascript" src="https://testing.quadminds.com/webcomponents/order-templates/main.js"></script>
        <script async type="text/javascript" src="https://testing.quadminds.com/webcomponents/order-templates/vendor.js"></script>
        <script async type="text/javascript" src="https://storage.googleapis.com/webcomponents-test/webcomponents-test/optimizer/zones-interface.js"></script>
        <script async type="text/javascript" src="https://testing.quadminds.com/webcomponents/order-list/main.js"></script>
        <script async type="text/javascript" src="https://testing.quadminds.com/webcomponents/order-list/vendor.js"></script>
    <?php endif ?>
    <script type="text/javascript">
        var id_usuario = <?php echo $usuario_id; ?>;
        var url_import = '<?php echo $url_import['url_cargas_upload']; ?>';
        var url_export = '<?php echo $url_import['url_cargas_download']; ?>';
        var url_import_espontaneos = '<?php echo $url_import['url_cargas_upload_espontaneos']; ?>';
        var zonasFilterMode = '<?php echo $filtroZonas == "Zonas" ? "byCode" : "byType"; ?>';
        var hoy = '<? echo $hoy ?>';
        var router_version = '<?php echo $router_version; ?>';
        var routing_mode = '<?php echo $routing_mode; ?>';
        var quadminds_flash = '<?php echo $flash; ?>';
        var user_tz = '<?php echo getTZUsuario(); ?>';
        var user_country = '<?php echo getCountry(); ?>';
        var flash_uploader_mode = '<?php echo $flash_uploader_mode; ?>';
        var day_trials = '<?php echo $dias_trial; ?>';
        var no_agrupar_todos = '<?php echo $no_select_all_order_tags; ?>';
        var ped_manuales = '<?php echo $pedidos_manuales_corporate; ?>';
        var group_distance = '<?php echo $groupByDistance; ?>';
        var min_service_time = '<?php echo $minServiceTime; ?>';
        var single_route_optimization_objective = '<?php echo $singleRouteOptimizationObjective; ?>';
        var optimization_waiting_penalty = '<?php echo $optimizationWaitingPenalty; ?>';

        // URLs by env
        const integrationsServiceUrl = '<? echo $integrationsServiceUrl; ?>';
        const authServiceUrl = '<? echo $authServiceUrl; ?>';

        window.ENABLE_PUBLISHING_MODAL = false;
        window.ENABLE_DYNAMIC_ROUTING_MODAL = false;
    </script>
    <style>
        .ui-daterangepickercontain {
            z-index: 15002;
        }

        #selectedVehiclePolygon {
            font-family: fontAwesome
        }
    </style>
    <? include_once('../analytics.php') ?>

    <!-- MONOREPO DYNAMIC ROUTING MODAL -->
    <?php if ($enablePublishingModal || $enableDynamicRoutingModal) : ?>
      <script type="text/javascript">
        // Load the remote script
        var script = document.createElement('script');
        let scriptUrl = '<?php echo $dynamicRoutingModalScriptsUrl; ?>';
        scriptUrl = `${scriptUrl}?=${new Date().getTime()}`
        
        script.src = scriptUrl;
        script.async = true;
        script.id = 'dynamic-routing-scripts';

        script.onload = function () {
          <?php if ($enablePublishingModal) : ?>
            window.ENABLE_PUBLISHING_MODAL = true;
            Object.defineProperty(window, 'publishingCoordinator', {
                get: function () {
                    if (!this._publishingCoordinator) {
                        this._publishingCoordinator = new DynamicRoutingScripts.PublishingCoordinator(
                            $('#dynamic-routing-modal'),
                            'dynamic-routing-iframe'
                        );
                    }
                    return this._publishingCoordinator;
                }
            });
          <?php endif; ?>

          <?php if ($enableDynamicRoutingModal) : ?>
            window.ENABLE_DYNAMIC_ROUTING_MODAL = true;
            Object.defineProperty(window, 'dynamicRoutingCoordinator', {
                get: function () {
                    if (!this._dynamicRoutingCoordinator) {
                        this._dynamicRoutingCoordinator = new DynamicRoutingScripts.DynamicRoutingCoordinator(
                            $('#dynamic-routing-modal'),
                            'dynamic-routing-iframe'
                        );
                    }
                    return this._dynamicRoutingCoordinator;
                }
            });
          <?php endif; ?>
        };

        document.head.appendChild(script);
      </script>
    <?php endif; ?>

    <!-- Restyling -->
    <link rel="stylesheet" type="text/css" href="/css/tailwind/styles.css?v=1" />
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.3.0/fonts/remixicon.css" rel="stylesheet">
    <!--  -->
</head>

<body>
    <div id="mapTypeControl" class="mapTypeControl">
        <i class="material-icons-round">nights_stay</i>
        <!-- <b><?= _("Modo Oscuro") ?></b> -->
        <div id="mapTypeControlSwitch" class="mdc-switch">
            <div class="mdc-switch__track"></div>
            <div class="mdc-switch__thumb-underlay">
                <div class="mdc-switch__thumb"></div>
                <input type="checkbox" class="mdc-switch__native-control" role="switch" aria-checked="false">
            </div>
        </div>
    </div>
    <div class='filters-toolbar filters-toolbar--small !pb-0 h-0' id='filtersToolbar'>
        <div class='filters-toolbar__content'>
            <button class='px-2 filters-toolbar__label'>
                <span class="text-2xl text-primaryq-300 pt-1 !ml-1.5 !mr-0"><i class="ri-map-2-line"></i></span>
                <span class="font-poppins text-gray-600 !text-lg"><?= _("Filtrar Mapa") ?></span>
            </button>
            <div class='filters-toolbar__menu-container'>
                <!-- ROW 1 -->
                <div class="filters-toolbar__menu">
                    <div class="filters-toolbar__menu__identifier">
                        <img src="images/eye-line.svg" alt='eye-icon' />
                        <span><?= _("Mostrar") ?></span>
                    </div>
                    <!-- <div class='filters-toolbar__menu__item visibility-toggle'> -->
                    <!--     <div class="input-group toggle-button"> -->
                    <!--         <input id="chkPendings" type="checkbox" aria-label="" data-style="toggle" data-toggle="toggle" data-on=" " data-off=" "> -->
                    <!--         <label for="chkPendings"><?= _("Sólo Órdenes Pendientes") ?></label> -->
                    <!--     </div> -->
                    <!-- </div> -->
                    <div class='filters-toolbar__menu__item visibility-toggle'>
                        <div class="input-group toggle-button">
                            <input id="chkAssigned" type="checkbox" aria-label="" data-style="toggle" data-toggle="toggle" data-on=" " data-off=" " data-gtm="Planificador_Content_FiltrosMapa_SoloOrdenesPorAsignar">
                            <label for="chkAssigned"><?= _("Sólo Órdenes por Asignar") ?></label>
                        </div>
                    </div>
                    <div class='filters-toolbar__menu__item visibility-toggle'>
                        <div class="input-group toggle-button">
                            <input id="chkSingleRoute" type="checkbox" aria-label="" data-style="toggle" data-toggle="toggle" data-on=" " data-off=" " data-gtm="Planificador_Content_FiltrosMapa_SoloRutaSeleccionada">
                            <label for="chkSingleRoute"><?= _("Sólo Ruta Seleccionada") ?></label>
                        </div>
                    </div>
                    <div class='filters-toolbar__menu__item'>
                        <div class="input-group toggle-button">
                            <input id="chkCanal" type="checkbox" aria-label="" data-style="toggle" data-toggle="toggle" data-on=" " data-off=" " data-gtm="Planificador_Content_FiltrosMapa_Canales">
                            <label for="chkCanal"><?= _("Canales") ?></label>
                        </div>
                    </div>
                    <div class='filters-toolbar__menu__item'>
                        <div class="input-group toggle-button">
                            <input id="chkCortes" type="checkbox" aria-label="" data-style="toggle" data-toggle="toggle" data-on=" " data-off=" " data-gtm="Planificador_Content_FiltrosMapa_Dimensiones">
                            <label for="chkCortes"><?= _("Dimensiones") ?></label>
                        </div>
                    </div>
                </div>
                <!-- ROW 2 -->
                <div class="filters-toolbar__menu">
                    <div class="filters-toolbar__menu__identifier">
                        <img src="images/filter-line.svg" alt='filter-line-icon' />
                        <span><?= _("Filtrar") ?></span>
                    </div>
                    <div class='filters-toolbar__menu__item' id='canalSelectWrapper'>
                        <label for="select-channels" class="label--emphasis"><?= _("Canales") ?></label>
                        <div id="canalSelectContainer"></div>
                    </div>
                    <div class='filters-toolbar__menu__item' id='cortesSelectWrapper'>
                        <label for="select-channels" class="label--emphasis"><?= _("Dimensiones") ?></label>
                        <div id="cortesSelectContainer"></div>
                    </div>
                    <div class='filters-toolbar__menu__item filters-toolbar__type__select' id='ordersTypeSelectWrapper'>
                        <label class="label--emphasis"><?= _("Tipo") ?></label>
                        <select multiple='multiple'>
                            <option value='1' data-gtm="Planificador_Content_FiltrosMapa_Tipo_Entregas" selected><?= _("Entregas") ?></option>
                            <option value='3' data-gtm="Planificador_Content_FiltrosMapa_Tipo_Retiros" selected><?= _("Retiros") ?></option>
                            <option value='5' data-gtm="Planificador_Content_FiltrosMapa_Tipo_Cobros" selected><?= _("Cobros") ?></option>
                        </select>
                    </div>
                    <div class='filters-toolbar__menu__item' id='agrupadoresSelectWrapper'>
                        <label for="agrupadores" class="label--emphasis"><?= _("Agrupadores") ?></label>
                        <div id="selectAgrupadoresContainer"></div>
                    </div>
                </div>
            </div>
            <button class='px-2 filters-toolbar__expand'>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.172 12L8.22198 7.04999L9.63598 5.63599L16 12L9.63598 18.364L8.22198 16.95L13.172 12Z" fill="#263238" fill-opacity="0.6" />
                </svg>
            </button>
        </div>
    </div>

    <div id="mapWidgets" class="flex space-x-4 items-start mt-[10px] mr-[10px] justify-end z-10 h-0">
        <!-- <div class="flex flex-col items-end justify-end space-x-4 space-y-4 2xl:flex-row 2xl:space-x-4 2xl:space-y-0 2xl:items-center"> -->
        <div class="flex flex-col items-end justify-end space-y-4">
            <div id="distanceAndTimeWidget" class="bg-white rounded-lg shadow-qm py-0.5 px-3 flex space-x-3 items-stretch">
                <div id="distanceTotal" class="flex items-center space-x-2">
                    <span class="pt-1 text-2xl text-primaryq-300"><i class="ri-pin-distance-line"></i></span>
                    <span class="text-xl font-semibold text-gray-600 font-poppins"><?= _("Distancia") ?></span>
                    <span class="text-xl text-primaryq font-medium !ml-4">
                        <span id="distanceTotalValue">0</span> km
                    </span>
                </div>
                <div class="relative">
                    <div class="absolute w-[1px] h-full bg-gray-50"></div>
                </div>
                <div id="timeTotal" class="flex items-center space-x-2">
                    <span class="pt-1 text-2xl text-primaryq-300"><i class="ri-timer-line"></i></span>
                    <span class="text-xl font-semibold text-gray-600 font-poppins"><?= _("Tiempo") ?></span>
                    <span class="text-xl text-primaryq font-medium !ml-4">
                        <span id="timeTotalValue">00:00</span> hs
                    </span>
                </div>
            </div>

            <div id="clientsWidget" class="bg-white rounded-lg shadow-qm py-0.5 px-3 flex space-x-2 items-center">
                <span class="pt-1 text-2xl text-primaryq-300"><i class="ri-group-line"></i></span>
                <span class="text-xl font-semibold text-gray-600 font-poppins"><?= _("Clientes Asignados") ?></span>
                <div class="!ml-4 text-xl font-medium flex items-end">
                    <span class="text-primaryq" id="totalesAsignadosClientes">0</span>
                    <span class="text-gray-300">/<span id="totalesClientes">0</span></span>
                </div>

                <div id="clientes-pedidos">
                    <button class="space-x-2 bg-error-200 rounded-full !ml-4 items-center px-2 hidden" id="geocode-clients-qty">
                        <span class="text-xl text-error-50 pt-1 !-mt-1 !-mb-1"><i class="ri-information-fill"></i></span>
                        <span class="text-xl font-medium text-white" id="clientsAssignedValue">0</span>
                    </button>
                </div>

            </div>
        </div>

        <div id="capabilitiesWidget" class="bg-white rounded-lg shadow-qm flex flex-col transition w-[256px]">
            <div id="widgetContent" class="flex flex-col">
            </div>

            <button class="flex items-center justify-center transition rounded-b-lg hover:bg-gray-50">
                <span class="text-xl text-gray-600 transition mapWidgetsECButton"><i class="ri-arrow-down-s-line"></i></span>
            </button>
        </div>

        <div class="flex flex-col items-center bg-white rounded-lg shadow-qm">
            <div class="flex items-center px-2 py-1 space-x-2 border-b border-gray-50">
                <span class="text-2xl text-primaryq-300"><i class="ri-map-pin-time-line"></i></span>
                <label class="relative inline-flex items-center cursor-pointer !mb-0">
                    <input id="showDistanceTimeWidget" name="showDistanceTimeWidget" type="checkbox" value="" class="sr-only peer" data-gtm="Planificador_Content_FiltrosTiempoViaje" checked>
                    <div class="w-[32px] h-[16px] bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:!top-[1px] after:!left-[1px] after:bg-white  after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-primaryq-500 peer-checked:after:ml-[2px]"></div>
                </label>
            </div>
            <div class="flex items-center px-2 py-1 space-x-2">
                <span class="text-2xl text-primaryq-300"><i class="ri-bar-chart-horizontal-fill"></i></span>
                <label class="relative inline-flex items-center cursor-pointer !mb-0">
                    <input id="showCapabilitiesWidget" name="showCapabilitiesWidget" type="checkbox" value="" class="sr-only peer" data-gtm="Planificador_Content_FiltrosVariablesdeCarga" checked>
                    <div class="w-[32px] h-[16px] bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:!top-[1px] after:!left-[1px] after:bg-white  after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-primaryq-500 peer-checked:after:ml-[2px]"></div>
                </label>
            </div>
        </div>

    </div>

    <div class='toolbar' id='toolbarNew'>
        <div class='toolbar__content'>
            <button class='px-2 toolbar__label'>
                <span class="text-2xl text-primaryq-300 pt-1 !ml-1.5 !mr-0"><i class="ri-artboard-2-line"></i></span>
                <span class="font-poppins text-gray-600 !text-lg">Herramientas</span>
            </button>
            <div class='toolbar__menu'>
                <div class='toolbar__selected-tool'>
                    <img src="images/colores/icon_hand_grey.svg" />
                </div>
                <div class='toolbar__menu__item toolbar__menu__item--active'>
                    <button class='toolbar__menu__button' data-event='move' data-gtm="Planificador_Content_Barra_SeleccionManual">
                        <img src="images/colores/icon_hand_grey.svg" />
                </div>
                <div class='toolbar__menu__item toolbar__menu__item--active'>
                    <button class='toolbar__menu__button' data-event='move' data-gtm="Planificador_Content_Barra_SeleccionManual">
                        <img src="images/colores/icon_hand_grey.svg" />
                        <span class='toolbar__menu__item__legend'>
                            Mover y Seleccionar
                        </span>
                        <span class='toolbar__shortcut'>1</span>
                    </button>
                </div>
                <div class='toolbar__menu__item with-options'>
                    <button class='toolbar__menu__button' data-event='drawAddingPolygon' data-gtm="Planificador_Content_Barra_AsignarPoligono">
                        <img src="images/colores/icon_triangle_grey.svg" />
                        <span class='toolbar__menu__item__legend'>
                            Polígono
                        </span>
                        <span class='toolbar__shortcut'>2</span>
                    </button>
                    <button class='toolbar__menu__sub-button' data-event='drawAddingPolygon' data-gtm="Planificador_Content_Barra_AsignarPoligono">
                        + Añadir
                        <span class='toolbar__shortcut'>2</span>
                    </button>
                    <button class='toolbar__menu__sub-button' data-event='drawRemovingPolygon' data-gtm="Planificador_Content_Barra_RemoverPoligono">
                        - Remover
                        <span class='toolbar__shortcut'>2</span>
                        <span class='toolbar__shortcut'>R</span>
                    </button>
                </div>
                <div class='toolbar__menu__item with-options'>
                    <button class='toolbar__menu__button' data-event='drawAddingRectangle' data-gtm="Planificador_Content_Barra_AsignarRectangulo">
                        <img src="images/colores/icon_square_grey.svg" />
                        <span class='toolbar__menu__item__legend'>
                            Rectángulo
                        </span>
                        <span class='toolbar__shortcut'>3</span>
                    </button>
                    <button class='toolbar__menu__sub-button' data-event='drawAddingRectangle' data-gtm="Planificador_Content_Barra_AsignarRectangulo">
                        + Añadir
                        <span class='toolbar__shortcut'>3</span>
                    </button>
                    <button class='toolbar__menu__sub-button' data-event='drawRemovingRectangle' data-gtm="Planificador_Content_Barra_RemoverRectangulo">
                        - Remover
                        <span class='toolbar__shortcut'>3</span>
                        <span class='toolbar__shortcut'>R</span>
                    </button>
                </div>
                <div class='toolbar__menu__item'>
                    <button class='toolbar__menu__button' data-event='manualAssign' data-gtm="Planificador_Content_Barra_AsignarRemoverIndividual">
                        <img src="images/colores/icon_cursor_grey.svg" />
                        <span class='toolbar__menu__item__legend'>
                            Asignar/Remover
                        </span>
                        <span class='toolbar__shortcut'>4</span>
                    </button>
                </div>
                <div class='toolbar__menu__item'>
                    <button class='toolbar__menu__button' data-event='autoAssignByVehicle' data-gtm="Planificador_Content_Barra_AsignarxCamion">
                        <img src="images/icon_truck_grey.svg" style="width: 24px; height: 24px;" />
                        <span class='toolbar__menu__item__legend'>
                            Asignar a Vehículos de Clientes
                        </span>
                        <span class='toolbar__shortcut'>5</span>
                    </button>
                </div>
            </div>
            <button class='px-2 toolbar__expand'>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13.172 12L8.22198 7.04999L9.63598 5.63599L16 12L9.63598 18.364L8.22198 16.95L13.172 12Z" fill="#263238" fill-opacity="0.6" />
                </svg>
            </button>
        </div>
    </div>

    <div id="wrapper">
        <?php
        $hideMenu = true;
        $extendTooltip = false;
        $header_type = 'wide';
        $noBootstrap = true;
        include_once('../header.php');
        ?>
        <div id="content-wrap">
            <div id="menu" class="!bg-gray-50 border-r border-gray-100 !font-roboto">
                <div class="menu-content">
                    <div class="v1link flex bg-primaryq-50 !h-10 items-center justify-between px-4 mb-2 rounded-md text-primaryq font-roboto shadow-qm">
                        <div class="flex items-center space-x-2">
                            <div class="pt-1 text-xl"><i class="ri-history-line"></i></div>
                            <a class="hover:no-underline hover:font-medium !text-primaryq" href="index_v1.php"><?= _("Ir a la Versión Anterior"); ?></a>
                        </div>
                        <span class="pt-1 text-xl text-gray-300 cursor-pointer">
                            <i class="ri-close-line"></i>
                        </span>
                    </div>
                    <div id="detalleOpciones" class="flex flex-col visible space-y-4">
                        <div class="px-4 pt-3 pb-4 bg-white rounded-lg shadow-qm">
                            <div class="flex items-center space-x-2">
                                <span class="text-primaryq-200 !pt-1 text-2xl"><i class="ri-calendar-event-line"></i></span>
                                <div class="font-semibold labelMenu font-poppins text-gray"><?= _("Inicio de Rutas") ?></div>
                            </div>
                            <div class="botonera flex flex-col space-y-1.5 mt-2">
                                <span class="font-poppins font-semibold mb-0 !text-xl text-gray-500">Fecha y Salida</span>
                                <div class="flex space-x-2">
                                    <div class="work-date !w-full !flex-grow">
                                        <div class="input-group button-datepicker w-full date-picker !pl-2">
                                            <input id="fechaRepartoHeader" class="form-control fechaReparto !text-left !text-xl !pl-2" autocomplete="off" type="text" placeholder=<?= _("Fecha de entrega") ?> />
                                            <span class="input-group-addon text-gray-200 !pt-2 !pr-2 flex"><i class="ri-calendar-event-line my-auto"></i></span>
                                        </div>
                                    </div>
                                    <div class="input-group input-select-standard !mb-0 !w-16">
                                        <select id="salidaFechaRepartoHeader" class="form-control !rounded-md !w-16 !p-1">
                                            <option value="1">1</option>
                                            <option value="2">2</option>
                                            <option value="3">3</option>
                                            <option value="4">4</option>
                                            <option value="5">5</option>
                                            <option value="6">6</option>
                                            <option value="7">7</option>
                                            <option value="8">8</option>
                                            <option value="9">9</option>
                                            <option value="10">10</option>
                                            <option value="11">11</option>
                                            <option value="12">12</option>
                                            <option value="13">13</option>
                                            <option value="14">14</option>
                                            <option value="15">15</option>
                                            <option value="16">16</option>
                                            <option value="17">17</option>
                                            <option value="18">18</option>
                                            <option value="19">19</option>
                                            <option value="20">20</option>
                                            <option value="21">21</option>
                                            <option value="22">22</option>
                                            <option value="23">23</option>
                                            <option value="24">24</option>
                                            <option value="25">25</option>
                                            <option value="26">26</option>
                                            <option value="27">27</option>
                                            <option value="28">28</option>
                                            <option value="29">29</option>
                                            <option value="30">30</option>
                                            <option value="31">31</option>
                                            <option value="32">32</option>
                                            <option value="33">33</option>
                                            <option value="34">34</option>
                                            <option value="35">35</option>
                                            <option value="36">36</option>
                                            <option value="37">37</option>
                                            <option value="38">38</option>
                                            <option value="39">39</option>
                                            <option value="40">40</option>
                                            <option value="41">41</option>
                                            <option value="42">42</option>
                                            <option value="43">43</option>
                                            <option value="44">44</option>
                                            <option value="45">45</option>
                                            <option value="46">46</option>
                                            <option value="47">47</option>
                                            <option value="48">48</option>
                                            <option value="49">49</option>
                                            <option value="50">50</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-4 pt-3 pb-4 bg-white rounded-lg shadow-qm">
                            <div class="flex items-center space-x-2">
                                <span class="text-primaryq-200 !pt-1 text-2xl"><i class="ri-inbox-unarchive-line"></i></span>
                                <div class="font-semibold labelMenu font-poppins text-gray"><?= _("Seleccionar Órdenes") ?></div>
                            </div>
                            <div class="flex flex-col mt-2 space-y-3 botonera">

                                <?php if ($flash || $pedidos_manuales_corporate) { ?>
                                    <div class="dropdown show pedidos_manuales">
                                        <button class="justify-between w-full button-primary-ghost-chameleon-sidebar dropdown-toggle" id="dropdownPedidosManuales" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <div class="flex items-center space-x-2">
                                                <span class="pt-1 text-2xl text-primaryq-200"><i class="ri-inbox-unarchive-line"></i></span>
                                                <span class="text-xl"><?= _("Añadir Nuevas") ?></span>
                                            </div>
                                            <span class="pt-1 text-2xl"><i class="ri-arrow-down-s-line"></i></span>
                                        </button>
                                        <div class="dropdown-menu font-roboto" aria-labelledby="dropdownPedidosManuales">
                                            <div class="flex flex-col">
                                                <a class="dropdown-item dropdown-item-chameleon-sidebar" id="importarPedidosManuales" data-gtm="Planificador_Menu_SeleccionarOrdenes_Manual">
                                                    <span class="pt-1 text-2xl"><i class="ri-add-circle-line"></i></span>
                                                    <div>
                                                        <?= _("Manual") ?> <span class="shorcut-label !px-2">C</span><span class="shorcut-label !px-2">P</span>
                                                    </div>
                                                </a>
                                                <?php if (!$enableOrdersTemplate) : ?>
                                                    <a class="dropdown-item dropdown-item-chameleon-sidebar" id="importarPedidosRegulares">
                                                        <span class="pt-1 text-2xl"><i class="ri-file-excel-2-line"></i></span>
                                                        <span><?= _("Desde archivo") ?></span>
                                                    </a>
                                                <?php endif; ?>
                                                <?php /*if($enableReprogramacion):*/ ?>
                                                <a class="dropdown-item dropdown-item-chameleon-sidebar" id="pedidosReprogramar" data-gtm="Planificador_Menu_SeleccionarOrdenes_Reprogramar">
                                                    <span class="pt-1 text-2xl"><i class="ri-file-excel-2-line"></i></span>
                                                    <span><?= _("Reprogramar") ?><sup class="ml-1 text-gray-200"><?= _("BETA") ?></sup></span>
                                                </a>
                                                <?php if ($enableOrdersTemplate) : ?>
                                                    <a class="dropdown-item dropdown-item-chameleon-sidebar" id="importarOrderTemplates">
                                                        <span class="pt-1 text-2xl"><i class="ri-file-excel-2-line"></i></span>
                                                        <span><?= _("Desde archivo") ?></span>
                                                    </a>
                                                <?php endif; ?>
                                                <?php /* endif;*/ ?>
                                                <?php if ($enableIntegrations) : ?>
                                                    <a class="dropdown-item dropdown-item-chameleon-sidebar" id="pedidosMerchants" data-gtm="Planificador_Menu_SeleccionarOrdenes_DesdeVendedores">
                                                        <svg class="svg-pedidos-merchants svg-mask-properties"></svg>
                                                        <?= _("Desde vendedores") ?>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if ($fuerzaVentas) : ?>
                                                    <a class="dropdown-item dropdown-item-chameleon-sidebar" id="getFrecuenciaVisita" data-gtm="Planificador_Menu_SeleccionarOrdenes_PorFrecuenciaDeVisita">
                                                        <span class="pt-1 text-2xl"><i class="ri-user-shared-line"></i></span>
                                                        <span><?= _("Por frecuencia de visita") ?></span>
                                                    </a>
                                                <?php endif; ?>

                                            </div>
                                        </div>
                                    </div>
                                <?php } else { ?>
                                    <?php if (!$ocultarImportar) { ?>
                                        <?php if (!$enableOrdersTemplate) : ?>
                                            <div id="importarPedidos" class="button-primary-ghost-chameleon-sidebar" role="button">
                                                <span class="pt-1 text-2xl text-primaryq-200"><i class="ri-inbox-unarchive-line"></i></span>
                                                <span class="text-xl"><?= _("Importar") ?></span>
                                            </div>
                                        <?php endif; ?>
                                        <?php if ($enableOrdersTemplate) : ?>
                                            <div id="importarOrderTemplates" class="button-primary-ghost-chameleon-sidebar" role="button" data-gtm="Planificador_Menu_SeleccionarOrdenes_DesdeArchivo">
                                                <span class="pt-1 text-2xl text-primaryq-200"><i class="ri-file-excel-2-line"></i></span>
                                                <span class="text-xl"><?= _("Importar") ?></span>
                                            </div>
                                        <?php endif; ?>
                                    <?php } ?>
                                <?php } ?>

                                <?php if ($importarPedidosCustomWs) { ?>
                                    <div id="importarPedidosCustomWs" class="button-primary-ghost-chameleon-sidebar" role="button">
                                        <span class="pt-1 text-2xl text-primaryq-200"><i class="ri-inbox-unarchive-line"></i></span>
                                        <span class="text-xl"><?= _("Importar desde WS") ?></span>
                                    </div>
                                <?php } ?>

                                <?php if ($compactacion_enwis) { ?>
                                    <div id="getPedidosEnwis" class="button-primary-ghost-chameleon-sidebar" role="button">
                                        <span class="pt-1 text-2xl text-primaryq-200"><i class="ri-inbox-unarchive-line"></i></span>
                                        <?= _("Traer pedidos Enwis") ?>
                                    </div>
                                <?php } ?>

                                <span class="font-poppins font-semibold !mt-6 mb-0 !text-xl text-gray-500">Ver pendientes</span>

                                <div id='pendientes-date-picker' class="input-group button-datepicker date-picker !w-full">
                                    <!-- Default value to show when datePicker is disabled -->
                                    <span class='default-value'></span>
                                    <input class="form-control" autocomplete="off" type="text" id="fecha" placeholder=<?= _("Fecha...") ?> />
                                    <span class="input-group-addon text-gray-200 !pt-2 !pr-2 flex"><i class="ri-calendar-event-line my-auto"></i></span>
                                </div>
                                <div id="datePickerDisabled" class="alert alert-info" style="display: none;">
                                    <span class="glyphicon glyphicon-time" style="margin-right: 5px"></span>
                                    <p><?= _('Visualizando una planificación con fecha anterior a la presente.') ?></p>
                                </div>
                            </div>

                            <?php if (!$chameleon_no_programados_auto && $chameleon_filter_get_pedidos) { ?>
                                <div class="input-group toggle-button !justify-start !items-center !space-x-2">
                                    <input id="programados" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" ">
                                    <label for="programados" class="!font-normal !m-0"><?= _("Pendientes") ?></label>
                                </div>
                            <?php } ?>

                            <div id="agrupador" class="botonera">
                                <div class="font-poppins font-semibold !mt-6 mb-0 !text-xl text-gray-500"><?= _("Agrupación") ?></div>
                                <div id="agrupadorMultiselectContainer" data-template='<div class="tooltip tooltip-agrupadores" style="z-index: 999 !important;"><div class="tooltip-arrow"></div><div class="tooltip-inner"><div></div></div>' title="<?= _("Seleccione una agrupación") ?>">
                                    <div class="btn-group" id="agrupadorDropdown">
                                        <button type="button" class="text-left dropdown-toggle btn btn-default btn-sm" data-toggle="dropdown">
                                            <span><?= _("Seleccionar") ?></span>
                                            <b class="caret"></b>
                                        </button>

                                        <div id="agrupadorMultiselect" class="dropdown-menu">
                                            <input type="text" placeholder="Buscar.." id="searchAgrupador" onkeyup="filterFunction()">
                                            <ul>
                                                <li class="all-container">
                                                    <label class="checkcontainer">
                                                        <span class="grouper-span"><?= _("Todos") ?></span>
                                                        <input type="checkbox" value="Todos" id="all-groupers" />
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </li>
                                            </ul>

                                            <div id="apply-groupers" class="button button-standard" role="button">
                                                <span><b><?= _("Aplicar") ?></b></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-4 pt-3 pb-4 bg-white rounded-lg shadow-qm">
                            <div class="flex items-center space-x-2">
                                <span class="text-primaryq-200 !pt-1 text-2xl"><i class="ri-route-line"></i></span>
                                <div class="font-semibold labelMenu font-poppins text-gray"><?= _("Planificar Rutas") ?></div>
                            </div>
                            <div class="flex flex-col mt-2 space-y-3 botonera">

                                <button id="routingOptions" class="planningButton button-secondary-chameleon-sidebar !mb-4 !rounded-full" data-toggle="modal" data-target="#routingOptionsModal" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo" disabled>
                                    <span class="pt-1 text-2xl text-gray-400"><i class="ri-settings-line"></i></span>
                                    <span class="text-xl"><?= _("Opciones de Ruteo") ?></span>
                                </button>

                                <button id="menuTabOptimize" class="planningButton button-primary-ghost-chameleon-sidebar" style="display: none;" data-menu-content="detalleOptimizar" data-gtm="Planificador_Menu_PlanificarRutas_RuteoDinamico" disabled>
                                    <span class="pt-1 text-2xl text-primaryq-200"><i class="ri-magic-line"></i></span>
                                    <span class="text-xl"><?= _("Ruteo Dinámico") ?></span>
                                </button>

                                <div class="flex space-x-2 !mt-6">
                                    <div id="guardarAsignacion" role="button" class="button-secondary-chameleon-sidebar group/button !px-2" data-gtm="Planificador_Menu_PlanificarRutas_Guardar">
                                        <span class="pt-1 text-2xl text-gray-400"><i class="ri-save-3-line"></i></span>
                                        <span class="text-xl hidden group-hover/button:!inline-flex group-hover/button:!visible"><?= _("Guardar") ?></span>
                                    </div>
                                    <div id="cargarAsignacion" role="button" class="button-secondary-chameleon-sidebar group/button !px-2" data-gtm="Planificador_Menu_PlanificarRutas_Cargar">
                                        <span class="pt-1 text-2xl text-gray-400"><i class="ri-upload-2-line"></i></span>
                                        <span class="text-xl hidden group-hover/button:!inline-flex group-hover/button:!visible"><?= _("Cargar") ?></span>
                                    </div>
                                    <div id="descargarPedidosChameleon" role="button" class="button-secondary-chameleon-sidebar group/button !px-2" data-gtm="Planificador_Menu_PlanificarRutas_Descargar">
                                        <span class="pt-1 text-2xl text-gray-400"><i class="ri-download-2-line"></i></span>
                                        <span class="text-xl hidden group-hover/button:!inline-flex group-hover/button:!visible"><?= _("Descargar") ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-5 py-4 bg-white rounded-lg shadow-qm" id='clienteOcacionalGroup' style="display:none;">
                            <div class="font-semibold labelMenu font-poppins text-gray"><?= _("Agregar Cliente") ?></div>
                            <div class="flex flex-col mt-6 space-y-3 botonera">
                                <div class="input-group input-with-icon">
                                    <input placeholder=<?= _("Cod. Cliente - Nombre - Codigo Pedido") ?> type="text" id="buscarClientesOcacional" />
                                    <span class="input-group-addon"><span class="glyphicon glyphicon-search"></span></span>
                                </div>
                            </div>
                        </div>
                        <div id="asignarCargasTooltip">
                            <button id="asignarCargas" class="publishButton button-primary-chameleon-sidebar !rounded-md w-full" data-gtm="Planificador_Menu_PublicarPlan" disabled>
                                <span class="pt-1 text-2xl text-primaryq-100"><i class="ri-send-plane-line"></i></span>
                                <span class="text-xl"><?= _("Publicar Plan") ?></span>
                            </button>
                        </div>
                    </div>
                    <div id="detalleOptimizar">
                        <div class="px-5 py-6 bg-white rounded-lg shadow-qm">
                            <div class="font-semibold labelMenu font-poppins text-gray"><?= _("Parámetros") ?></div>
                            <div class="flex flex-col mt-6 space-y-3 botonera">
                                <div class="input-group input-select-standard flex-column !w-full !mb-0">
                                    <label class="!text-lg"><?= _("Depósito") ?></label>
                                    <select id="depot_optimizar" class="form-control !rounded-md"></select>
                                </div>
                                <div class="input-group input-select-standard flex-column !w-full">
                                    <label class="!text-lg"><?= _("Zonas") ?></label>
                                    <select id="zonas_optimizar" class="form-control !rounded-md"></select>
                                </div>
                                <div class="input-group input-select-standard flex-column not-flash !w-full">
                                    <label class="!text-lg"><?= _("Performance (Tiempo)") ?></label>
                                    <select id="time_limit" class="form-control !rounded-md">
                                        <option value="10"><?= _("Test (10 seg)") ?></option>
                                        <option selected="selected" value="60"><?= _("Very Low (1 min)") ?></option>
                                        <option value="120"><?= _("Low (2 min)") ?></option>
                                        <option value="300"><?= _("Normal (5 min)") ?></option>
                                        <option value="600"><?= _("High (10 min)") ?></option>
                                        <option value="900"><?= _("Extreme (15 min)") ?></option>
                                    </select>
                                </div>
                                <div class="input-group input-select-standard flex-column routing_v2 !w-full">
                                    <label class="!text-lg"><?= _("Objetivo") ?></label>
                                    <select id="algorithmObjective" class="form-control !rounded-md">
                                        <option value="mindisp"><?= _("Minimizar Dispersión") ?></option>
                                        <option selected="selected" value="mindist"><?= _("Minimizar Distancia") ?></option>
                                        <option class='not-flash' value="mintime"><?= _("Minimizar Tiempo") ?></option>
                                    </select>
                                </div>
                                <div class="input-group input-select-standard flex-column routing_v2 not-flash !w-full">
                                    <label class="!text-lg"><?= _("Agrupaciones") ?></label>
                                    <select id="algorithmClusters" class="form-control !rounded-md">
                                        <option selected="selected" value="0">0</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                        <option value="200">200</option>
                                        <option value="250">250</option>
                                        <option value="300">300</option>
                                        <option value="350">350</option>
                                        <option value="400">400</option>
                                    </select>
                                </div>
                                <div class="input-group input-select-standard flex-column routing_v2 not-flash !w-full">
                                    <label class="!text-lg"><?= _("Tolerancia") ?></label>
                                    <select id="algorithmExc" class="form-control !rounded-md">
                                        <option value="0.4"><?= _("Muy Alta") ?></option>
                                        <option value="0.5"><?= _("Alta") ?></option>
                                        <option selected="0.6" value="0.6"><?= _("Media") ?></option>
                                        <option value="0.7"><?= _("Baja") ?></option>
                                        <option value="0.8"><?= _("Muy Baja") ?></option>
                                        <option value="1"><?= _("Sin Tolerancia (pueden quedar clientes sin asignar)") ?></option>
                                    </select>
                                </div>
                                <div class="input-group toggle-button !justify-start !items-center !space-x-2 !mt-8">
                                    <input id="clearPreviousRoutes" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" ">
                                    <label for="clearPreviousRoutes" class="!font-normal !m-0"><?= _("Borrar Rutas") ?></label>
                                </div>
                                <div class="input-group toggle-button !justify-start !items-center !space-x-2">
                                    <input id="onlyEmptyVehicles" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" " checked>
                                    <label for="onlyEmptyVehicles" class="!font-normal !m-0"><?= _("Sólo Vehículos Sin Carga") ?></label>
                                </div>
                                <div class="input-group toggle-button !justify-start !items-center !space-x-2 !mb-8">
                                    <input id="calculateRouteStart" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" ">
                                    <label for="calculateRouteStart" class="!font-normal !m-0"><?= _("Calcular Inicio de Ruta") ?></label>
                                    <div data-testid="icon-ri-question-fill" title="" data-test-id="icon-ri-question-fill" class="ri-question-fill text-gray-100 hover:text-gray-400 cursor-pointer" style="font-size: 16px;" data-original-title="Para poder realizar el calculo de horario de inicio, por lo menos el 70% de los pedidos deben tener ventana de entrega."></div>
                                </div>
                            </div>
                            <div id="optimize" class="button-primary-chameleon-sidebar !justify-center" role="button" data-gtm="Planificador_Menu_PlanificarRutas_RuteoDinamico_Comenzar">
                                <span class="pt-1 text-2xl text-primaryq-100"><i class="ri-magic-line""></i></span>
                                <span class=" text-xl"><?= _("Comenzar") ?></span>
                            </div>
                            <div class="button-secondary-chameleon-sidebar !mt-2 !text-lg rounded-full backMenuTabOptimize" role="button" data-gtm="Planificador_Menu_PlanificarRutas_RuteoDinamico_Cancelar">
                                <span class="pt-1 text-2xl"><i class="ri-close-line""></i></span>
                                <span class=" text-xl"><?= _("Cancelar") ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="menu-buttons">
                    <ul>
                        <li>
                            <a href="javascript:void(0)" title="<?= _('Opciones') ?>" class="menuTabOpciones submenu" data-menu-content="detalleOpciones">
                                <span class="glyphicon glyphicon-chevron-right"></span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div id="content">
                <div id="main">
                    <div style="position: relative;">
                        <div id="map_canvas"></div>
                        <div id="alertsContainer" style="font-size: 14px;">
                            <div id="partidaAlert" class="alert alert-warning fade" role="alert">
                                <strong><?= _('Oops! No tienes almacén'); ?> </strong><a style="color: inherit;" href="../capas.php?capa=DEPOSITOS" target="_blank">(<?= _('carga uno aquí'); ?>)</a>
                                <p style="font-size: 12px; margin: 0;"><?= _('Recuerda refrescar esta página luego de cargarlo.'); ?></p>
                            </div>
                            <div id="choferes-flash-alert-model" class="hidden">
                                <p class="hidden empty-search"><?= _('No hay flota con esos parámetros'); ?></p>
                                <p class="hidden no-drivers"><?= _('Oops! No tienes conductores'); ?></p>
                                <p class="hidden no-drivers"><?= _('Recuerda refrescar esta página luego de cargarlos.'); ?></p>
                                <div id="add-truck-btn" class="hidden no-drivers">
                                    <a href="../capas.php?capa=CHOFERES" target="_blank"><?= _('Cargar flota'); ?></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="detalleOffset"></div>
                </div>
                <div id="truckContainer">
                    <div class="absolute collapse-buttons">
                        <button id="drag-into-map" class="btn btn-default btn-xs resizer"></button>
                        <!--<button id="collapse-map" class="btn btn-default btn-xs">
                            <span class="glyphicon glyphicon-chevron-up"></span>
                        </button>
                        <button id="expand-map" class="btn btn-default btn-xs">
                            <span class="glyphicon glyphicon-chevron-down"></span>
                        </button>-->
                    </div>

                    <div id="blockContainerHeader">
                        <div id="tabs-container">
                            <div id="devices-tab" class="tab active" data-ref="blockContainer">
                                <span class="text-2xl pt-1 !mr-2"><i class="ri-truck-line"></i></span>
                                <h4 class="font-semibold tab-truck font-poppins"><?= _("Flota") ?></h4>
                                <div class="bg-gray-50 rounded-full !ml-4 px-2">
                                    <span id="vehiclesBadge" class="text-xl font-medium text-gray-200" data-toggle="tooltip" data-placement="top" data-html="true">
                                        <span class="totalesAsignadosVehiculos text-primaryq-500 !font-roboto">0</span> / <span class="totalesActivosVehiculos">0</span> (<span class="totalesVehiculos">0</span>)
                                    </span>
                                </div>
                            </div>
                            <div id="orders-table-tab" class="tab" data-ref="orders-table">
                                <span class="text-2xl pt-1 !mr-2"><i class="ri-archive-line"></i></span>
                                <h4 class="font-semibold tab-truck font-poppins"><?= _("Órdenes") ?></h4>
                                <div class="bg-gray-50 rounded-full !ml-4 px-2">
                                    <span id="ordersBadge" class="text-xl font-medium text-gray-200" data-toggle="tooltip" data-placement="top" data-html="true">
                                        <span class="totalesAsignadosPedidos text-primaryq-500 !font-roboto">0</span> / <span class="totalesPedidos">0</span>
                                    </span>
                                </div>
                            </div>
                            <div id="map-tab" class="tab" data-ref="zones-container">
                                <span class="text-2xl pt-1 !mr-2"><i class="ri-road-map-line"></i></span>
                                <h4 class="font-semibold tab-truck font-poppins"><?= _("Zonas") ?></h4>
                                <div class="bg-gray-50 rounded-full !ml-4 px-2">
                                    <span id="zonesBadge" class="text-xl font-medium text-gray-200" data-toggle="tooltip" data-placement="top" data-html="true">
                                        <span class="totalesZonas">0</span>
                                    </span>
                                </div>
                            </div>

                        </div>

                        <div id="toolsContainer" class="w-full px-6 pt-2 pb-3 blockContainer-tools truckListContainer-tools tools">
                            <div class="flex items-center space-x-6">

                                <div class="flex items-center space-x-2 font-roboto">
                                    <span class="text-[18px] mt-1 text-gray-500"><i class="ri-search-line"></i></span>
                                    <input id="searchCamion" type="text" class="form-control !pl-2" placeholder="<?= _("Buscar") ?>" data-gtm="Planificador_Content_BlockContainer_Flota_Buscar">
                                </div>

                                <div id="sortCamion" class="flex items-center space-x-2 font-roboto">
                                    <span class="text-[18px] mt-1 text-gray-500"><i class="ri-sort-desc"></i></span>
                                    <div id="sortTruckContainer"></div>
                                </div>

                                <div class="flex items-center space-x-2">
                                    <span class="text-[18px] mt-1 text-gray-500"><i class="ri-eye-line"></i></span>
                                    <div class="inline-flex rounded-lg">
                                        <label data-gtm="Planificador_Content_BlockContainer_Flota_Vistas_Grilla" for="devicesCardsView" class="flex radio w-[36px] h-[32px] items-center justify-center rounded-lg cursor-pointer bg-gray-50 text-gray-300 hover:bg-gray-100 border border-gray-100 [&.viewDevicesSelected]:bg-primaryq-50 [&.viewDevicesSelected]:text-primaryq [&.viewDevicesSelected]:border-primaryq devices-view-type">
                                            <input type="radio" name="devicesViewType" id="devicesCardsView" hidden />
                                            <span class="text-[16px] mt-1 font-normal"><i class="ri-layout-grid-line"></i></span>
                                        </label>
                                    </div>
                                    <div class="inline-flex rounded-lg">
                                        <label data-gtm="Planificador_Content_BlockContainer_Flota_Vistas_Lista" for="devicesListView" class="flex radio w-[36px] h-[32px] items-center justify-center rounded-lg cursor-pointer bg-gray-50 text-gray-300 hover:bg-gray-100 border border-gray-100 [&.viewDevicesSelected]:bg-primaryq-50 [&.viewDevicesSelected]:text-primaryq [&.viewDevicesSelected]:border-primaryq devices-view-type">
                                            <input type="radio" name="devicesViewType" id="devicesListView" hidden />
                                            <span class="text-[16px] mt-1 font-normal"><i class="ri-table-line"></i></span>
                                        </label>
                                    </div>
                                </div>
                                <button id="pinDetalleZona" class="btn btn-default btn-xs active" title="<?= _('Anclar') ?>"></button>
                            </div>

                            <div class="flex items-center space-x-2">
                                <label data-gtm="Planificador_Content_BlockContainer_Flota_ActivarTodaLaFlota" class="device-enabled-container relative inline-flex items-center cursor-pointer !mb-0">
                                    <input id="activarTodaFlota" type="checkbox" value="" class="sr-only peer" checked>
                                    <div class="!min-w-[32px] w-[32px] h-[16px] bg-gray-50 shadow-inner peer-checked:shadow-none peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:!left-[1px] after:top-1/2 after:-translate-y-1/2 after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-primaryq-500 peer-checked:after:ml-[2px]"></div>
                                    <span class="ml-2 leading-4">Activar toda la flota</span>
                                </label>

                                <button id="remover-carga" class="button-red-ghost rounded-full !py-0 publishButton" data-gtm="Planificador_Content_BlockContainer_Flota_RemoverRutas">
                                    <div class="flex items-center space-x-2">
                                        <span class="pt-1 text-2xl"><i class="ri-close-circle-line"></i></span>
                                        <span class="leading-4"><?= _("Remover Rutas") ?></span>
                                    </div>
                                </button>

                                <button id="optimizar-rutas" class="button-primary-ghost rounded-full !py-0 publishButton" data-gtm="Planificador_Content_BlockContainer_Flota_OptimizarRutas">
                                    <div class="flex items-center space-x-2">
                                        <span class="pt-1 text-2xl"><i class="ri-magic-line"></i></span>
                                        <span class="leading-4"><?= _("Optimizar Rutas") ?></span>
                                    </div>
                                </button>

                            </div>
                        </div>
                    </div>

                    <div id="blockContainer" class="flex flex-wrap items-start justify-start overflow-y-auto col-md-12 tab-content"></div>
                    <div id="truckListContainer" class="flex flex-row hidden col-md-12 tab-content"></div>
                    <div id="zones-container" class="hidden col-md-12 tab-content"></div>
                    <div id="orders-table" class="hidden w-full h-full px-3 tab-content">
                        <order-list class="flex w-full h-full"></order-list>
                    </div>

                    <!-- Displayed when flash user's fleet is empty -->
                    <div class="flex items-center justify-center hidden w-full h-full pb-8" id="fleet-empty">
                        <div class="fleet-empty-container flex flex-grow items-center justify-center p-4 border-2 border-dashed border-gray-100 bg-gray-50 hover:bg-primaryq-50 rounded-md min-h-[120px] h-full cursor-pointer" onClick="addDrivers()">
                            <div class="text-[32px] flex items-center justify-center bg-primaryq-50 rounded-full p-3 w-[50px] h-[50px] mb-4 text-primaryq-300">
                                <i class="ri-user-add-line"></i>
                            </div>
                            <div class="!font-poppins !font-medium !text-[14px] text-gray-600">
                                <?= _("Todavia no has agregado Conductores. Agregar") ?>
                                <span class="text-primaryq !font-poppins !font-medium !text-[14px]">
                                    <?= _("Nuevo Conductor") ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div id="device-container-model" class="device-card group hidden relative w-[218px] bg-white shadow-qm border [&.selected]:border-2 border-gray-50 mr-[10px] mb-[10px] rounded-lg box-border p-[4px] pl-[12px] pt-0 h-max">
                        <div class="device-card-overlay bg-gray opacity-80 absolute top-0 left-0 right-0 bottom-0 rounded-md flex items-center justify-center -z-10 [&.optimizing]:z-10">
                            <div class="mdc-circular-progress mdc-circular-progress--indeterminate" style="width:44px;height:44px;" role="progressbar" aria-label="Progress Bar" aria-valuemin="0" aria-valuemax="1">
                                <div class="mdc-circular-progress__determinate-container">
                                    <svg class="mdc-circular-progress__determinate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <circle class="mdc-circular-progress__determinate-track" cx="12" cy="12" r="8.75" stroke-width="2.5" />
                                        <circle class="mdc-circular-progress__determinate-circle" cx="12" cy="12" r="8.75" stroke-dasharray="54.978" stroke-dashoffset="54.978" stroke-width="2.5" />
                                    </svg>
                                </div>
                                <div class="mdc-circular-progress__indeterminate-container">
                                    <div class="mdc-circular-progress__spinner-layer">
                                        <div class="mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left">
                                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="12" cy="12" r="8.75" stroke-dasharray="54.978" stroke-dashoffset="27.489" stroke-width="1.5" />
                                            </svg>
                                        </div>
                                        <div class="mdc-circular-progress__gap-patch">
                                            <svg class="mdc-circular-progress__indeterminate-circle-graphic" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="12" cy="12" r="8.75" stroke-dasharray="54.978" stroke-dashoffset="27.489" stroke-width="1" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-2 font-medium font-poppins"><?= _("Optimizando") ?></p>
                        </div>
                        <div class="colorIndicator absolute w-[7px] h-[80px] bg-gray-100 left-0 top-1/2 -translate-y-1/2 rounded-r-md group-[.vehicle-disabled]:!bg-gray-100"></div>
                        <div class="flex items-center justify-between space-x-1 border-b card-header border-gray-50">
                            <span class="mt-[1px] text-[16px]"><i class="text-gray-300 ri-smartphone-line"></i></span>
                            <span class="device-name text-[13px] !font-poppins !font-semibold overflow-hidden whitespace-nowrap text-ellipsis flex-grow"></span>
                            <div id="device-status" class="flex items-center w-6 cursor-pointer"></div>

                            <label class="device-enabled-container relative inline-flex items-center cursor-pointer !mb-0">
                                <input type="checkbox" value="" class="sr-only device-enabled peer" checked>
                                <div class="w-[32px] h-[16px] bg-gray-50 shadow-inner peer-checked:shadow-none peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:!top-[1px] after:!left-[1px] after:bg-white  after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-primaryq-500 peer-checked:after:ml-[2px]"></div>
                            </label>
                        </div>
                        <div class="card-body py-[4px] border-b border-gray-50 min-h-[45px]"></div>
                        <div class="card-tools flex space-x-2 items-center justify-between mt-[4px] h-[24px]">
                            <div class="wTooltip returnToDepotIndicator text-[16px]">
                                <i class="ri-arrow-go-back-line"></i>
                            </div>
                            <div class="flex items-center justify-end flex-grow space-x-2">
                                <div>
                                    <button data-gtm="Planificador_Content_BlockContainer_Flota_Configuracion" class="wTooltip device-tool-config button-secondary-chameleon-sidebar !w-[24px] !h-[24px] !p-0 !rounded-md text-[18px]" title="Configuración">
                                        <i class="ri-settings-3-line"></i>
                                    </button>
                                </div>
                                <div>
                                    <button data-gtm="Planificador_Content_BlockContainer_Flota_TraspasarCarga" class="cardActionButton wTooltip device-tool-traspasar button-secondary-chameleon-sidebar !w-[24px] !h-[24px] !p-0 !rounded-md text-[18px]" title="Traspasar Carga">
                                        <i class="ri-arrow-left-right-line"></i>
                                    </button>
                                </div>
                                <div>
                                    <button data-gtm="Planificador_Content_BlockContainer_Flota_RemoverCarga" class="cardActionButton wTooltip device-tool-eliminar button-red-ghost justify-center !w-[24px] !h-[24px] !p-0 !rounded-md text-[18px]" title="Remover Carga">
                                        <i class="ri-close-circle-line"></i>
                                    </button>
                                </div>
                                <div>
                                    <button data-gtm="Planificador_Content_BlockContainer_Flota_Optimizar" class="cardActionButton wTooltip device-tool-recalcular button-primary-ghost-chameleon-sidebar !w-[24px] !h-[24px] !p-0 !rounded-md text-[18px]" title="Optimizar">
                                        <i class="ri-magic-line"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Card model with "add more" button that appends after last driver -->
                    <div class="device-card hidden relative w-[220px] h-[107px] bg-primaryq-50 hover:bg-primaryq-500 transition shadow-sm border border-primaryq-100 border-dashed mr-[10px] mb-[10px] rounded-lg cursor-pointer" id="add-more-drivers-card">
                        <div class="flex flex-col items-center justify-center w-full h-full card-body group text-primaryq-500 hover:text-white" onClick="addDrivers()">
                            <span class="text-[28px]"><i class="ri-user-add-line"></i></span>
                            <span class="-mt-2 text-xl font-semibold font-poppins"><?= _("Nuevo Conductor") ?></span>
                        </div>
                    </div>

                    <div class="hidden measure-model flex items-center justify-between space-x-2 h-[12px]" id="measure-container-model">
                        <span class="text-gray-400 measure-icon"><i class="h-[12px] w-[12px]"></i></span>
                        <div class="progress flex-grow h-[6px] bg-gray-50 shadow-none mb-0">
                            <div class="progress-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                <span class="sr-only"></span>
                            </div>
                        </div>
                        <span class="percentage text-right min-w-[22px] text-[9px] text-gray-400">0%</span>
                    </div>

                </div>
            </div>
            <div id="ticker" class="flex flex-col relative w-[10px] w-min-[10px] w-max-[328px] z-[100] border-l bg-bg-light !font-roboto overflow-y-auto h-[calc(100vh_-_50px)]">
                <div class="color left-0 absolute w-[7px] rounded-r-md h-[38px] top-2 z-[10] bg-[#dadada]"></div>
                <div id="datosDispositivo" class="pl-8 pr-4">
                    <div class="flex p-0 selected-device">
                        <div id="selected-device-card" class="flex py-4">
                            <div class="flex justify-between items-center">
                                <div class="flex space-x-2 items-center flex-nowrap">
                                    <span class="!pt-1 text-2xl text-gray-200"><i class="ri-truck-line"></i></span>
                                    <div id="dispositivo-nombre" class="font-poppins font-semibold text-2xl whitespace-nowrap"><?= _("Toda la flota"); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div><div class="bg-gray-100 w-full h-[1px]"></div></div>
                <div id="vehicle-tabs-container">
                    <div id="vehicleActions" class="flex px-6 py-2"></div>
                    <div><div class="bg-gray-100 w-full h-[1px]"></div></div>
                    <div class="px-6 py-4 flex space-x-2">
                        <div id="device-tab" class="tab active transition hover:bg-primaryq-50 hover:border-primaryq-100 cursor-pointer px-4 py-1 rounded-full border border-gray-100 bg-gray-50 text-gray-300 [&.active]:!bg-primaryq-50 [&.active]:!border-primaryq [&.active]:!text-primaryq" data-ref="vehicle-datails-tab">
                            <span><?= _("Vehículo") ?></span>
                        </div>
                        <div id="detail-tab" class="tab flex space-x-2 items-center transition hover:bg-primaryq-50 hover:border-primaryq-100 cursor-pointer px-4 py-1 rounded-full border border-gray-100 bg-gray-50 text-gray-300 [&.active]:!bg-primaryq-50 [&.active]:!border-primaryq [&.active]:!text-primaryq" data-ref="detalleCamionWrapper">
                            <span><?= _("Ruta") ?></span>
                            <span id="detail-route-counter" class="color rounded-full text-white px-2 !-mr-2.5"></span>
                        </div>
                    </div>
                </div>
                <div class="w-[328px] h-full px-6 overflow-y-auto -right-[328px]">
                    <!-- menu content start -->
                    <div id="vehicle-datails-tab" class="vehicle-tab-content">
                        <div id="selected-device-card" class="my-4">
                            <div class="menuGroup">
                                <div id="selected-device-body" class="flex flex-col">
                                    <div id='all-fleet-options' class="not-flash py-4 flex flex-col space-y-4">
                                        <label id='modificarTodosContainer' class="relative inline-flex items-center cursor-pointer !mb-6">
                                            <input id="modificarTodos" type="checkbox" value="" class="sr-only peer" checked>
                                            <div class="!min-w-[32px] w-[32px] h-[16px] bg-gray-50 shadow-inner peer-checked:shadow-none peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:!left-[1px] after:top-1/2 after:-translate-y-1/2 after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-primaryq-500 peer-checked:after:ml-[2px]"></div>
                                            <span class="ml-2 leading-4 !font-normal text-xl"><?= _("Modificar todos") ?></span>
                                        </label>
                                        <div id="startTimeContainer" class="flex items-center justify-between" style="display: none;">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-2xl text-primaryq-300"><i class="ri-play-circle-line"></i></span>
                                                <label for="startTime" class="font-poppins font-semibold pt-0.5"><?= _("Inicio") ?></label>
                                            </div>
                                            <div class="input-group w-[130px]">
	                                            <input type="text" class="input-sm !rounded-md form-control inputValorUnidad" id="startTime" placeholder="hh:mm" disabled="disabled">
                                            </div>
                                        </div>
                                        <div id="minTimeContainer" class="flex items-center justify-between" style="display: none;">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-2xl text-primaryq-300"><i class="ri-timer-line"></i></span>
                                                <label for="minTime" class="font-poppins font-semibold pt-0.5"><?= _("Tiempo Mínimo") ?></label>
                                            </div>
                                            <div class="input-group w-[130px]">
                                            	<input type="text" class="input-sm !rounded-md form-control inputValorUnidad" id="minTime" placeholder="hh:mm" disabled="disabled">
                                            </div>
                                        </div>
                                        <div id="maxTimeContainer" class="flex items-center justify-between" style="display: none;">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-2xl text-primaryq-300"><i class="ri-timer-fill"></i></span>
                                                <label for="maxTime" class="font-poppins font-semibold pt-0.5"><?= _("Tiempo Máximo") ?></label>
                                            </div>
                                            <div class="input-group w-[130px]">
                                            	<input type="text" class="input-sm !rounded-md form-control inputValorUnidad" id="maxTime" placeholder="hh:mm" disabled="disabled">
                                            </div>
                                        </div>
                                        <div id="maxDistanceContainer" class="flex items-center justify-between" style="display: none;">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-2xl text-primaryq-300"><i class="ri-pin-distance-line"></i></span>
                                                <label for="maxDistance" class="font-poppins font-semibold pt-0.5"><?= _("Distancia Máxima") ?></label>
                                            </div>
                                            <div class="input-group w-[130px]">
                                            	<input type="text" class="input-sm !rounded-md form-control inputValorUnidad" id="maxDistance" placeholder="Kms" disabled="disabled">
                                            </div>
                                        </div>
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-2">
                                                <span class="text-2xl text-primaryq-300"><i class="ri-arrow-go-back-line"></i></span>
                                                <label for="modificarTodosRegresoDeposito" class="font-poppins font-semibold pt-0.5"><?= _("Regreso a Depósito") ?></label>
                                            </div>
                                            <label id='modificarTodosRegresoDepositoContainer' class="relative w-[130px] inline-flex items-center cursor-pointer">
                                                <input id="modificarTodosRegresoDeposito" disabled type="checkbox" value="" class="sr-only peer">
                                                <div class="!min-w-[32px] w-[32px] h-[16px] bg-gray-50 shadow-inner peer-checked:shadow-none peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:!left-[1px] after:top-1/2 after:-translate-y-1/2 after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-primaryq-500 peer-checked:after:ml-[2px]"></div>
                                            </label>
                                        </div>

                                        <?php if ($routeCost) : ?>
                                            <div class="my-4 bg-white p-4 shadow-qm rounded-lg flex flex-col space-y-2 opacity-60" id="trip-type-rate-container-all">
                                                <div class="flex flex-col space-y-1">
                                                    <div class="flex items-center justify-start">
                                                    <span class="font-poppins text-xl font-semibold"><?= _("Tipo de Viaje") ?></span>
                                                    <div class='loader-wrapper ml-auto'>
                                                        <span id='trip-types-loader-all' class='loader-spinner loader-spinner--small'></span>
                                                        <span id='rates-loader-all' class='loader-spinner loader-spinner--small'></span>
                                                    </div>
                                                    </div>
                                                    <div id="dispositivosRouteCostTripTypeAll" class="form-group [&>.input-group]:!w-full [&>.input-group>select]:!rounded-md !mb-2">

                                                    </div>
                                                    <span class="font-poppins text-xl font-semibold"><?= _("Tarifa") ?></span>
                                                    <div id="dispositivosRouteCostRateAll" class="form-group [&>.input-group]:!w-full [&>.input-group>select]:!rounded-md">
                                                        
                                                    </div>
                                                </div>
                                            </div>

                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="my-4 bg-white p-4 shadow-qm rounded-lg flex flex-col space-y-2" style="display: none" id="chofer-asignado-container">
                            <div class="flex flex-col space-y-1">
                                <span class="font-poppins text-xl font-semibold"><?= _("Conductor asignado") ?></span>
                                <div id="comboChofer" class="form-group [&>.input-group]:!w-full [&>.input-group>select]:!rounded-md"></div>
                            </div>
                            <div class="flex flex-col space-y-1">
                                <span id='vuelco-title' style="display:none;"  class="font-poppins text-xl font-semibold"><?= _("Descarga") ?></span>
                                <div id="vuelco" style="display:none;" class="form-group [&>.input-group]:!w-full [&>.input-group>select]:!rounded-md"></div>
                            </div>
                        </div>
                        <?php if ($routeCost) : ?>

                        <div class="my-4 bg-white p-4 shadow-qm rounded-lg flex flex-col space-y-2" style="" id="trip-type-rate-container">
                            <div class="flex flex-col space-y-1">
                                <div class="flex items-center justify-start">
                                  <span class="font-poppins text-xl font-semibold"><?= _("Tipo de Viaje / Tarifa") ?></span>
                                  <div class='loader-wrapper ml-auto'>
                                    <span id='trip-types-loader' class='loader-spinner loader-spinner--small'></span>
                                    <span id='rates-loader' class='loader-spinner loader-spinner--small'></span>
                                  </div>
                                </div>
                                <div id="dispositivosRouteCostTripType" class="form-group [&>.input-group]:!w-full [&>.input-group>select]:!rounded-md !mb-2">

                                </div>
                                <div id="dispositivosRouteCostRate" class="form-group [&>.input-group]:!w-full [&>.input-group>select]:!rounded-md">
                                    
                                </div>
                            </div>
                        </div>
                        
                        <?php endif; ?>
                    
                        <div class="my-4 bg-white px-4 py-2 shadow-qm rounded-lg" style="display: none">
                            <div class="">
                                <div id="dispositivoData" class="flex flex-col">
                                    <div class="prediction-row mb-2">
                                        <p></p>
                                        <p class="flex space-x-1 items-center font-poppins">
                                            <span class="text-xl text-gray-300"><i class="ri-pin-distance-line"></i></span>
                                            <span class="font-semibold"><?= _("Distancia") ?></span>
                                        </p>
                                        <p class="flex space-x-1 items-center font-poppins">
                                            <span class="text-xl text-gray-300"><i class="ri-timer-line"></i></span>
                                            <span class="font-semibold"><?= _("Tiempo") ?></span>
                                        </p>
                                    </div>

                                    <div class="separator !-mx-4"></div>

                                    <div id="origen-cliente-container" class="dispositivo-data-container" title="<?= _("Origen a Primer cliente") ?>">
                                        <div class="prediction-row dynamic-content">
                                            <img src="images/origen_cliente.svg" alt="" />
                                            <p></p>
                                            <p></p>
                                        </div>

                                        <div class="separator !-mx-4"></div>
                                    </div>

                                    <div id="cliente-ultimo-container" class="dispositivo-data-container" title="<?= _("Primero Cliente a Último cliente") ?>">
                                        <div class="prediction-row dynamic-content">
                                            <img src="images/cliente_ultimo.svg" alt="" />
                                            <p></p>
                                            <p></p>
                                        </div>

                                        <div class="separator !-mx-4"></div>
                                    </div>

                                    <div id="ultimo-descarga-container" class="dispositivo-data-container" title="<?= _("Último cliente a Descarga") ?>">
                                        <div class="prediction-row dynamic-content">
                                            <img src="images/ultimo_descarga.svg" alt="" />
                                            <p></p>
                                            <p></p>
                                        </div>

                                        <div class="separator !-mx-4"></div>
                                    </div>

                                    <div id="descarga-final-container" class="dispositivo-data-container" title="<?= _("Descarga a Final") ?>">
                                        <div class="prediction-row dynamic-content">
                                            <img src="images/descarga_final.svg" alt="" />
                                            <p></p>
                                            <p></p>
                                        </div>

                                        <div class="separator !-mx-4"></div>
                                    </div>

                                    <div id="ultimo-final-container" class="dispositivo-data-container" title="<?= _("Último cliente a Final") ?>">
                                        <div class="prediction-row dynamic-content">
                                            <img src="images/ultimo_final.svg" alt="" />
                                            <p></p>
                                            <p></p>
                                        </div>

                                        <div class="separator !-mx-4"></div>
                                    </div>

                                    <div class="prediction-row mt-4">
                                        <p class="text-left font-poppins">
                                            <span class="font-semibold"><?= _("Total") ?></span><br />
                                            <?= _("Máximo") ?>
                                        </p>
                                        <div id="distancia-total-container" class="dispositivo-data-totals-container">
                                            <b></b>
                                            <p></p>
                                        </div>

                                        <div id="tiempo-total-container" class="dispositivo-data-totals-container">
                                            <b></b>
                                            <p></p>
                                        </div>
                                    </div>
                                </div>
                                <div id="dispositivosDataCost" class="flex flex-cols" style="display: none;">
                                    <div class="prediction-row mt-4">
                                        <p class="text-left font-poppins">
                                                <span class="font-semibold"><?= _("Costo Estimado") ?></span>
                                            </p>
                                        <div id="costo-total-container" class="dispositivo-data-totals-container font-poppins font-semibold text-[14px]"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="detalleCamionWrapper" class="hidden vehicle-tab-content">
                        <div id="detalleCamionWrapperEmpty">
                            <img src="images/detalle-ruta-empty-state.svg" alt="" />
                            <h4 class="display-title"><?= _('Sin Ruta Todavía') ?></h4>
                            <p class="display-text"><?= _('Este vehículo no tiene una ruta por realizar. Empieza a utilizarlo asignándole pedidos.') ?></p>
                        </div>
                        <div class="">
                            <div class="filtroDetalleCamionContainer">
                                <div style="width: 250px;" class="input-group input-select-standard">
                                    <select id="filtroDetalleCamion" class="form-control"></select>
                                </div>
                                <div id="filtroDetalleCamionOrder" data-order="0">
                                    <span class="glyphicon glyphicon-sort-by-attributes"></span>
                                </div>
                                <div id="invertirOrdenPlanificacion">
                                    <span class="glyphicon glyphicon-transfer" title="<?= _('Invertir Orden'); ?>" />
                                </div>
                            </div>
                            <div id="detalleCamion" class="text-gray -mr-6"></div>
                        </div>
                    </div>
                    <!-- Depecrated <div id="dispositivoImg"></div> Depecrated-->


                    <!--                <div class="hidden mb-8 panel panel-default" id="traspasar-carga-container">-->
                    <!--                    <div class="menuGroup right-side-card">-->
                    <!--                        <div class="right-side-card-header" >-->
                    <!--                            <div class="labelMenuRightColumn">--><? //=_("Traspasar carga a:");
                                                                                            ?>
                    <!--</div>-->
                    <!--                        </div>-->
                    <!--                        <div class="separator"></div>-->
                    <!--                        <div class="load-transfer-pickup">-->
                    <!--                            <div id="traspasoCarga" class="form-group"></div>-->
                    <!--                        </div>-->
                    <!--                        <div class="load-transfer-actions">-->
                    <!--                            <div id="optimizarPorDistancia"class="button button-standard-small button-important">-->
                    <!--                                --><? //=_("Recalcular Ruta")
                                                            ?>
                    <!--                            </div>-->
                    <!--                            <div class="button button-standard-small button-important-danger reset">-->
                    <!--                                --><? //=_("Remover Carga")
                                                            ?>
                    <!--                            </div>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                    <!--                </div>-->
                    <!-- menu content end -->
                </div>
                <div class="menu-buttons">
                    <ul>
                        <li>
                            <a href="javascript:void(0)" class="menuTabOpciones submenu" data-menu-content="detalleOpciones">
                                <span class="glyphicon glyphicon-chevron-left"></span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div id="importSelection" title="<?= _('Elige como importar tus pedidos') ?>">
        <div id="fileUploadOption" class="importOption">
            <img src="images/importar_archivo.svg" alt="" />
            <p><?= _('Importar Archivo') ?></p>
        </div>
        <div id="tiendaNubeUploadOption" class="importOption">
            <img src="images/importar_tiendanube.png" alt="" />
            <p><?= _('Tiendanube') ?></p>
        </div>
    </div>

    <div id="externalAuthRequest" title="<?= _('Elige como importar tus pedidos') ?>">
        <p><?= _('Para poder importar los pedidos necesitamos que autorices nuestra App.') ?></p>
        <p><?= _('Haz clic en el botón debajo para iniciar el proceso de autorización.') ?></p>
        <div class="button button-small" id="authButton"><?= _("Autorizar") ?></div>
    </div>

    <!-- Usuarios flash -->
    <?php if ($flash) : ?>
        <div id="uploadDialog" class="upload-dialog-flash" title="<?= _('Subir archivo de Pedidos') ?>">
            <form name="upload" method="post" enctype="multipart/form-data" class="upload-dialog-toggle">

                <p><?= _('1. SELECCIONAR MODO:') ?></p>

                <label id="uploader-simple" class="radio-inline"><input type="radio" name="uploader-mode"><?= _('Básico') ?></label>
                <label id="uploader-advanced" class="radio-inline"><input type="radio" name="uploader-mode"><?= _('Avanzado') ?></label>
                <div style="margin-top: 10px; margin-bottom: 30px;" class="upload-dialog-toggle">
                    <a style="display: table-cell; font-size: 11px; font-weight: bold; padding-left: 3px;" id="template-url" href="db/importadorPedidos_flash_simple.xlsx">
                        <span style="font-size: 13px; margin-top: 10px; font-weight: bold; color: #1C7DF4;" id="downloadFileText"><?= _('Descargar archivo de ejemplo '); ?></span>

                        <span class="download-icon"></span>
                    </a>
                </div>

                <p><?= _('2. SUBIR ARCHIVO:') ?></p>


                <div class="dropzone image-upload" id="dropzone">

                    <div id="fileInfoContainer">


                    </div>

                </div>

                <input type="file" name="file" id="file" class="inputfile" />

                <span class="error"><?= _("No seleccionaste ningun archivo") ?></span>
                <div class="row bottom-buttons no-gutter">
                    <div class="text-left col-sm-6">

                        <a style="font-size: 10px; padding-left: 3px; margin-top: 10px; cursor: pointer; display: block;" href="https://docs.quadminds.com/docs/importar-pedidos" target="_blank" id="launchIntercom">

                            <span class="help-icon"></span>
                            <p><?= _("¿Necesitas ayuda?") ?></p>
                        </a>
                    </div>
                    <div class="text-right col-sm-6">
                        <button type="button" id="importButton" class="btn btn-primary import-button disabled"><?= _("Importar") ?></button>
                    </div>
                </div>


            </form>
        <?php else : ?>
            <div id="uploadDialog" title="<?= _('Subir archivo de Pedidos') ?>">
                <form name="upload" method="post" enctype="multipart/form-data" class="upload-dialog-toggle">
                    <input type="file" name="file" id="file" class="inputfile" />
                    <label for="file">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17" viewBox="0 0 20 17">
                            <path d="M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z" />
                        </svg>
                        <span><?php echo _("Seleccionar Archivo") ?></span>
                        <span class="error"><?= _("No seleccionaste ningun archivo") ?></span>

                    </label>
                </form>

            <?php endif ?>

            <?php if ($flash) : ?>
                <div id="import-result-information" class="import-result-flash">
                    <table class="table table-pedidos table-pedidos-flash">
                        <tbody>
                        </tbody>
                    </table>
                    <table class="table table-pedidos-errores">
                        <thead>

                        </thead>
                        <tbody>
                        </tbody>
                    </table>

                    <div class="row bottom-buttons-results">
                        <div class="text-left col-sm-6">

                        </div>
                        <div class="text-right col-sm-6">
                            <button type="button" id="closeButton" class="btn btn-primary close-button-importador"><?= _("Cerrar") ?></button>
                        </div>
                    </div>
                </div>
            <?php else : ?>
                <div id="import-result-information">
                    <h5 class="import-information-title"><?php echo _('Resultado de la Importacion:') ?></h5>
                    <table class="table table-pedidos">
                        <tbody>
                        </tbody>
                    </table>
                </div>
            <?php endif ?>
            <?php if ($url_import['url_cargas_upload_espontaneos']) { ?>
                <form name="uploadEspontaneos" method="post" enctype="multipart/form-data" class="upload-dialog-toggle">
                    <!--        <input type="file" name="file" id="file" />-->
                    <input type="file" name="fileEspontaneos" id="fileEspontaneos" class="inputfile" />
                    <label for="fileEspontaneos" style="background-color: #009BDB;margin-top: 30px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="17" viewBox="0 0 20 17">
                            <path d="M10 0l-5.2 4.9h3.3v5.1h3.8v-5.1h3.3l-5.2-4.9zm9.3 11.5l-3.2-2.1h-2l3.4 2.6h-3.5c-.1 0-.2.1-.2.1l-.8 2.3h-6l-.8-2.2c-.1-.1-.1-.2-.2-.2h-3.6l3.4-2.6h-2l-3.2 2.1c-.4.3-.7 1-.6 1.5l.6 3.1c.******* 1.2.9h16.3c.6 0 1.1-.4 1.3-.9l.6-3.1c.1-.5-.2-1.2-.7-1.5z" />
                        </svg>
                        <span><?php echo _("Cargar Espontáneos") ?></span>
                    </label>
                    <span class="error"><?= _("No seleccionaste ningun archivo") ?></span>
                </form>
                <div id="import-result-information_espontaneos">
                    <h5 class="import-information-title"><?php echo _('Resultado de la Importacion:') ?></h5>
                    <table class="table">
                        <tbody>
                        </tbody>
                    </table>
                </div>
            <?php } ?>
            <div id="formMsg" style="display: none;"></div>
            </div>

            <div id="order-detail-dialog" title="<?= _("Pedido") ?>">
                <div id="order-info">
                    <div>
                        <span class="subtitle"><?= _("CÓDIGO:") ?></span>
                        <span class="content code"></span>
                    </div>

                    <div>
                        <span class="subtitle"><?= _("ASIGNADO A:") ?></span>
                        <span class="content assigned"></span>
                    </div>

                    <div>
                        <span class="subtitle"><?= _("COMENTARIOS:") ?></span>
                        <span class="content comments"></span>
                    </div>
                </div>

                <p class="separator"></p>

                <div id="order-measures">
                    <p class="subtitle"><?= _("DIMENSIONES:") ?></p>

                    <div class="hidden measure-info-model measure-info">
                        <p>
                            <img src="" alt="order-measure" />
                            <span class="measure-label"></span>
                        </p>
                        <p class="measure-value"></p>
                    </div>

                    <div id="measures-list">
                    </div>
                </div>
            </div>

            <!-- Trial max drivers quantity reached dialog -->
            <div id="max-drivers-qty-dialog" title="<?= _("Agregar conductores") ?>" style="display: none;">
                <div class="flash-dialog-content">
                    <h4><?= _("Puedes agregar hasta 10 conductores") ?></h4>
                    <span>
                        <?= _("Actualiza tu suscripción para agregar más conductores o elimina alguno para reemplazarlo por uno nuevo.") ?>
                    </span>
                    <div class="flash-dialog-buttons">
                        <a href="../capas.php?capa=CHOFERES" class="flash-outlined-btn">
                            <?= _('Ir a conductores'); ?>
                        </a>

                        <a href="../stripe/subscription" class="flash-outlined-btn primary">
                            <?= _('Actualizar suscripción'); ?>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Add drivers dialog for flash users -->
            <div id="addDriversDialog" class="dialog" title="Agrega Conductores">
                <div id="addDriversDialogDummyContent"></div>
            </div>

            <!-- Bulk delete orders dialogs start -->
            <div id="delete-orders-no-orders-dialog" title="<?= _("Eliminar pedidos") ?>">
                <p><?= _("Error. Debe seleccionar al menos un pedido.") ?></p>
            </div>

            <div id="delete-orders-verification-dialog" title="<?= _("Eliminar pedidos") ?>">
                <p><?= _("¿Está seguro que desea eliminar los pedidos seleccionados?") ?></p>
            </div>
            
            <div id="delete-orders-infobox-verification-dialog" title="<?= _("Eliminar pedidos") ?>">
                <p><?= _("¿Está seguro que desea eliminar las órdenes?") ?></p>
            </div>
            
            <div id="delete-orders-confirm-dialog" title="<?= _("Eliminar pedidos") ?>">
                <p><?= _("Los pedidos fueron eliminados exitosamente.") ?></p>
            </div>
            <!-- Bulk delete orders dialogs end -->

            <div id="something-went-wrong-dialog" title="<?= _("Error") ?>">
                <p><?= _("Algo salió mal. Intente nuevamente más tarde.") ?></p>
            </div>

            <!-- Delete orders dialogs start -->
            <div id="delete-order-verification-dialog" title="<?= _("Eliminar pedido") ?>">
                <p><?= _("¿Está seguro que desea eliminar el pedido <span class='order-code'></span>?") ?></p>
            </div>

            <div id="delete-order-confirm-dialog" title="<?= _("Eliminar pedido") ?>">
                <p><?= _("El pedido fue eliminado exitosamente.") ?></p>
            </div>
            <!-- Delete orders dialogs end -->

            <div id="overflow-confirm" style="display: none;">
                <p id="overflow">
                    <span class="ui-icon ui-icon-alert" style="float:left; margin:0 7px 20px 0;"></span>
                </p>
            </div>

            <div id="traspaso-dialog" style="display: none;">
                <div class="load-transfer-pickup">
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <p><?= _("De:") ?></p>
                        <b id="traspasoCargaFrom"></b>
                    </div>
                    <div id="traspasoCarga" class="form-group form-inline"></div>
                </div>
            </div>

            <div id="noVehicleDialogPolygon" style="display: none;">
                <p><?= _("Debes elegir un vehículo para completar esta acción") ?></p>
                <div id="selectVehicleOnDialog" class="form-group form-inline"></div>
            </div>

            <div id="ZoneOptimizationDialog" style="display: none;">
                <p><?= _("Debes elegir un vehículo para completar esta acción") ?></p>
                <div id="selectZonesOnDialog" class="form-group form-inline"></div>
            </div>

            <div id="truckAssignmentErrorDialog" style="display: none;">
                <p style="font-weight: bold; color: #333;">
                    <?= _('Esto puede deberse a dos motivos:'); ?>
                <ul>
                    <li>
                        <?= _('No hay una vinculación entre los clientes de tus pedidos y tus vehículos'); ?>
                    </li>
                    <li>
                        <?= _('Los pedidos con clientes vinculados ya se encuentran asignados a una ruta'); ?>
                    </li>
                </ul>
                <hr>
                <?= _('Si quieres saber como realizar una vinculación entre clientes y vehículos puedes dirigirte a '); ?>
                <a style="color: inherit; font-weight:bold; color: #000;" href="https://docs.quadminds.com/docs/asignar-ordenes-a-veh%C3%ADculos" target="_blank"><?= _("este artículo"); ?></a>
                </p>
            </div>

            <div id="MultipleZoneOptimizationDialog" style="display: none;">
                <p><?= _("Selecciona el objetivo de la optimización") ?></p>
                <div id="selectObjectiveOnDialog" class="form-group form-inline">
                    <select id="selectMultipleZoneOptimizationObjective" class="form-control">
                        <option value="Distancias"><?= _("Minimizar Distancia") ?></option>
                        <option value="Tiempos"><?= _("Minimizar Tiempos") ?></option>
                        <option value="DispersionesGeograficas"><?= _("Minimizar Dispersión") ?></option>
                    </select>
                </div>
            </div>

            <div id="route-reset-verification-dialog" title="<?= _("Eliminar pedido") ?>" style="display: none;">
                <p><?= _("¿Está seguro que desea remover la carga?") ?></p>
            </div>

            <div id="fechaRepartoDialog" title="Salvar/Exportar" style="display: none;">
                <div class="work-date">
                    <label><?= _("Fecha") ?></label>
                    <div class="flex justify-between input-group button-datepicker ">
                        <input id="fechaReparto" class="form-control fechaReparto" autocomplete="off" type="text" id="fecha" placeholder=<?= _("Fecha") ?> />
                        <span class="flex items-center justify-center input-group-addon px-7"><img src="./images/calendar.svg" alt="calendar image" class="absolute svg-icon " /></span>
                    </div>
                </div>
                <div class="input-group input-select-standard">
                    <label><?= _("Salida") ?></label>
                    <select id="salidaFechaReparto" class="form-control">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                        <option value="8">8</option>
                        <option value="9">9</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                        <option value="13">13</option>
                        <option value="14">14</option>
                        <option value="15">15</option>
                        <option value="16">16</option>
                        <option value="17">17</option>
                        <option value="18">18</option>
                        <option value="19">19</option>
                        <option value="20">20</option>
                        <option value="21">21</option>
                        <option value="22">22</option>
                        <option value="23">23</option>
                        <option value="24">24</option>
                        <option value="25">25</option>
                        <option value="26">26</option>
                        <option value="27">27</option>
                        <option value="28">28</option>
                        <option value="29">29</option>
                        <option value="30">30</option>
                        <option value="31">31</option>
                        <option value="32">32</option>
                        <option value="33">33</option>
                        <option value="34">34</option>
                        <option value="35">35</option>
                        <option value="36">36</option>
                        <option value="37">37</option>
                        <option value="38">38</option>
                        <option value="39">39</option>
                        <option value="40">40</option>
                        <option value="41">41</option>
                        <option value="42">42</option>
                        <option value="43">43</option>
                        <option value="44">44</option>
                        <option value="45">45</option>
                        <option value="46">46</option>
                        <option value="47">47</option>
                        <option value="48">48</option>
                        <option value="49">49</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>

            <div id="importTiendaNubeDialog" title="<?= _("Importar desde tu Tienda Nube") ?>" style="display: none;">
                <div class="importTiendaNubeBlock-dates" style="display: flex; justify-content: space-between; align-items: center; padding: 10px">
                    <div>
                        <label class="importTiendaNubeBlock-label" style="margin-right: 10px;"><?= _("Fecha Desde") ?></label>
                        <div class="input-group button-datepicker">
                            <input id="fechaDesdeTiendaNube" class="form-control" autocomplete="off" type="text" placeholder=<?= _("Fecha Desde") ?> />
                            <span class="input-group-addon"><img src="./images/calendar.svg" alt="calendar image" class="absolute svg-icon" /></span>
                        </div>
                    </div>
                    <div>
                        <label class="importTiendaNubeBlock-label" style="margin-right: 10px;"><?= _("Fecha Hasta") ?></label>
                        <div class="input-group button-datepicker">
                            <input id="fechaHastaTiendaNube" class="form-control" autocomplete="off" type="text" placeholder=<?= _("Fecha Desde") ?> />
                            <span class="input-group-addon"><img src="./images/calendar.svg" alt="calendar image" class="absolute svg-icon" /></span>
                        </div>
                    </div>
                </div>
                <div class="importTiendaNubeBlock">
                    <label class="importTiendaNubeBlock-label" style="margin-right: 10px;"><?= _("Estado del Pedido") ?></label>
                    <select style="width: 393px;" id="estadoPedidoTiendaNube" class="form-control">
                        <option value="1"><?= _("Todas (salvo archivadas y canceladas)") ?></option>
                        <option value="2"><?= _("Esperando que empaquetes la orden") ?></option>
                        <option value="3"><?= _("Esperando confirmación de envío") ?></option>
                        <option value="4"><?= _("Esperando confirmación de pago") ?></option>
                    </select>
                </div>
                <div class="importTiendaNubeBlock">
                    <label class="importTiendaNubeBlock-label" style="margin-right: 10px;"><?= _("Formas de entrega") ?></label>
                    <select style="display: none;" id="carriersTiendaNube" multiple="multiple" class="form-control">
                    </select>
                </div>
                <div id="import-result-information-tienda-nube">
                    <h5 class="import-information-title"><?php echo _('Resultado de la Importacion:') ?></h5>
                    <table class="table table-pedidos">
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>

            <div id="exportConfirm" style="display: none;" title="<?= _("Publicar Plan") ?>">
                <p style="font-size: 13px;"><b><?= _("Al publicar el plan ocurrira lo siguiente:") ?></b></p>
                <ul class="ul-publish">
                    <li><?= _("Los conductores tendran <b>acceso a la ruta desde la app</b><br>(en la fecha del plan)") ?></li>
                    <br>
                    <li><?= _("Si posee <b>activada la notificación de pedido en preparación:</b><br>los clientes con email registrado serán notificados.") ?></li>
                    <br>
                    <li><?= _("Las rutas se verán a través de la opción de <b>menú \"Entregas\"</b>") ?></li>
                </ul>
                <div class="input-group toggle-button" style="display: none;">
                    <label for="exportTN"><?= _("Informar a Tienda Nube") ?></label>
                    <input id="exportTN" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" ">
                </div>
            </div>

            <div id="waiting" style="z-index: 100002; position: absolute;"></div>

            <div id="modal" style="background: #aaaaaa; top: 0; left: 0; position: absolute; opacity: 0.5; filter:Alpha(Opacity=50); z-index: 100000"></div>

            <div id="configuracionDialog" class="modalConfiguracionChameleon" style="display: none;"></div>

            <div id="configuracionAllDialog" class="modalConfiguracionChameleon" style="display: none;"></div>

            <div id="noPedidos" style="display: none;">
                <div id="openImport" class="button button-standard button-important">
                    <img src="./images/import.svg" alt="save image" class="svg-icon" />
                    <?= _("Importar nuevos pedidos para esta fecha.") ?>
                </div>
                <p style="margin: 20px; font-size: 10px; text-align: center;"><?= _("O intente cambiando la fecha de pedido o los agrupadores.") ?></p>
            </div>

            <div id="saveAsignacionDialog" style="display: none;">
                <label class="main-label"><?= _("Nombre:") ?></label>
                <input type="text" id="nombreAsignacion" placeholder="Plan 01" />
                <div class="input-group toggle-button">
                    <label for="recurrenteAsignacion"><?= _("Recurrente") ?></label>
                    <input id="recurrenteAsignacion" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" ">
                </div>
            </div>

            <div id="exportarNoPlanificadosDialog" style="display: none;">
                <div class="input-group toggle-button">
                    <label for="exportNoPlanificadosDetalle"><?= _("Con detalle de productos:") ?></label>
                    <input id="exportNoPlanificadosDetalle" type="checkbox" aria-label="..." data-style="toggle" data-toggle="toggle" data-on=" " data-off=" ">
                </div>
            </div>

            <div id="cargarAsignacionDialog" style="display: none; width: 500px;">
                <div id="asignacionesTable"></div>
            </div>

            <div id="pedidosFrecuenciaVisita" style="display: none;">
                <img class="exito-frecuencia-visita" src="./images/icono-exito.svg" alt="Pedidos creados" />
                <h5>¡Listo!</h5>
                <p class="text-pedidos-frecuencia-visitas">
                    <?= _("No olvides publicar el plan para que los conductores vean las Rutas en la app.") ?>
                </p>
            </div>

</body>

<div id='geocodificarClientes' style="background: white" style="display:none;"></div>
<div id='optimizationStatus' style="display:none; width:600px;">
    <table>
        <tr>
            <td valign="top">
                <div id="optimizationVars"></div>
                <div id="optimizationStages">
                    <div id="optimizationStage"></div>
                    <!--<div id="optimizationStatusProgressbar"><div class="progress-label">Loading...</div></div>-->
                </div>
            </td>
            <td valign="top">
                <div id="optimizationStatusCost" style="margin-left: 10px; width:100%;">
                    <div class='optimizeDatosHeader' style="width:100%;">
                        <h3><?= _("Resultados") ?></h3>
                    </div>
                    <div>
                        <div style="font-size:14px; font-weight: bold; margin:10px;"><?= _("Tiempo transcurrido") ?></div>
                        <div style="font-size:13px; margin:10px;"><span id="runner" style=" margin:10px;"></span></div>
                    </div>
                    <!--                    <div>-->
                    <!--                        <div style="font-size:13px; font-weight: bold; margin: 0px;">--><? //= _("Primera optimización");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div style="font-size:11px; font-weight: bold; margin:10px;">--><? //= _("Distancia");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="primer_optimizacion" style="font-size:10px; margin:10px;"></div>-->
                    <!--                        <div style="font-size:12px; font-weight: bold; margin:10px;">--><? //= _("Flota");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="primer_optimizacion_vehiculos" style="font-size:11px; margin:10px;"></div>-->
                    <!--                        <br/>-->
                    <!--                    </div>-->
                    <!--                    <div>-->
                    <!--                        <div style="font-size:15px; font-weight: bold; margin:10px;">--><? //= _("Última optimización");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div style="font-size:12px; font-weight: bold; margin:10px;">--><? //= _("Distancia última optimización");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="ultima_optimizacion" style="font-size:11px; margin:10px;"></div>-->
                    <!--                        <div style="font-size:12px; font-weight: bold; margin:10px;">--><? //= _("Flota última optimización");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="ultima_optimizacion_vehiculos" style="font-size:11px; margin:10px;"></div>-->
                    <!--                        <br/>-->
                    <!--                    </div>-->
                    <!--                    <div>-->
                    <!--                        <div style="font-size:14px; font-weight: bold; color:#33bb77; margin:10px;">--><? //= _("Reducción");
                                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="reduccion" style="font-size:13px; color:#33bb77; margin:10px;"></div>-->
                    <!--                        <br/>-->
                    <!--                    </div>-->
                    <!--                    <div>-->
                    <!--                        <div style="font-size:15px; font-weight: bold; margin:10px;">--><? //= _("Distancia");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="ultima_optimizacion" style="font-size:11px; margin:10px;">- Km</div>-->
                    <!--                        <br/>-->
                    <!--                        <div style="font-size:15px; font-weight: bold; margin:10px;">--><? //= _("Flota");
                                                                                                                ?>
                    <!--</div>-->
                    <!--                        <div id="ultima_optimizacion_vehiculos" style="font-size:11px; margin:10px;">-</div>-->
                    <!--                        <br/>-->
                    <!--                    </div>-->
                </div>
            </td>
        </tr>
    </table>
    <div id="optimizationStatusProgressbar">
        <div class="progress-label"><?= _("Cargando...") ?></div>
    </div>

    <div class="botonera">
        <div class="button button-small" id="optimizeCancel"><?= _("Cancelar") ?></div>
        <div class="button button-small" id="optimizeFinish" style="display: none;"><?= _("Aceptar") ?></div>
    </div>
</div>


<div id='optimizationResult' style="display:none;">
    <div>
        <!--
        <table width="100%">
            <tr>
                <td><div style="font-size:28px; padding-top:5px; padding-bottom:5px;" align="center">Antes</div></td>
                <td><div style="font-size:28px; padding-top:5px; padding-bottom:5px;" align="center">Despues</div></td>
            </tr>
            <tr>
                <td>
                    <br/>
                    <div id="result_primer_optimizacion" style="font-size:17px;" align="center">1.232 Km</div>
                    <div style="font-size:13px; padding-top:4px;" align="center">Distancia</div>
                </td>
                <td>
                    <br/>
                    <div id="result_ultima_optimizacion" style="font-size:17px;" align="center">763 Km</div>
                    <div style="font-size:13px; padding-top:4px;" align="center">Distancia</div>
                </td>
            </tr>
            <tr>
                <td>
                    <br/>
                    <div id="result_primer_flota" style="font-size:17px;" align="center"></div>
                    <div style="font-size:13px; padding-top:4px;" align="center">Utilización de flota</div>
                </td>
                <td>
                    <br/>
                    <div id="result_ultima_flota" style="font-size:17px;" align="center"></div>
                    <div style="font-size:13px; padding-top:4px;" align="center">Utilización de flota</div>
                </td>
            </tr>
        </table>
        -->

        <div>
            <!--
            <div align="center"><div id="result_reduccion" style="font-size:23px; color:#229977; margin-top: 20px;">46%</div></div>
            <div style="font-size:17px; color:#229977; margin-top:10px;" align="center">Reducción de distancia</div>
            <td>
                <br/>
                <div id="result_ultima_optimizacion" style="font-size:17px;" align="center"></div>
                <div style="font-size:13px; padding-top:4px;" align="center">Distancia</div>
            </td>
            -->
            <td>
                <br />
                <div id="result_ultima_flota" style="font-size:17px;" align="center"></div>
                <div style="font-size:13px; padding-top:4px;" align="center"><?= _("Flota utilizada") ?></div>
            </td>
            <br />
        </div>

    </div>

    <div class="botonera">
        <div class="button button-small" id="optimizeResultFinish"><?= _("Aceptar") ?></div>
    </div>
</div>

<div id='optimizationUnexpected' style="display:none;">
    <div id='optimizationUnexpectedMessage'>
    </div>

    <div class="botonera">
        <div class="button button-small" id="optimizeUnexpectedAccept"><?= _("Aceptar") ?></div>
    </div>
</div>

<div id='optimizationError' style="display:none;">
    <div id='optimizationErrorMessage'>
    </div>
</div>

<div id="alertaModificarChofer" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Atencion: Conductor ya Asignado') ?></h4>
            </div>
            <div class="modal-body">
                <p><?= _('El conductor ya fue asignado a las siguientes unidades: ') ?> <br>
                    <strong id="unidadesPreAsignadas"></strong>
                </p>
            </div>
            <div class="modal-footer">
                <button id="btnCerrarAlertaModificarChofer" type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="alertFinalizacionImportacionWS" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body p-0">
                <div class="max-w-9xl mx-auto">
                    <div class="bg-white rounded-md shadow-lg overflow-hidden">
                        <!-- Header -->
                        <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
                                <div>
                                    <h1 class="text-xl font-bold text-white">Resultado de Importación</h1>
                                    <p class="text-blue-100/90 text-sm mt-1">Procesado el <span id="fechaImportacion"></span> a las <span id="horaImportacion"></span></p>
                                </div>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/10 text-white backdrop-blur-sm">
                                    <i class="fas fa-file-import mr-2"></i> <span id="nombreArchivoImportado">Importación WS</span>
                                </span>
                            </div>
                        </div>
                        
                        <!-- Tabs -->
                        <div class="border-b border-gray-200 bg-white">
                            <nav class="flex overflow-x-auto px-2">
                                <button onclick="openTab(event, 'resumen')" class="tab-link px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 border-transparent text-gray-500 hover:bg-gray-50 transition-colors">
                                    <i class="ri-pie-chart-2-fill mr-2"></i> Resumen
                                </button>
                                <button onclick="openTab(event, 'errores')" class="tab-link px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 border-transparent text-gray-500 hover:bg-gray-50 transition-colors">
                                    <i class="ri-error-warning-line mr-2"></i> Errores
                                    <span class="ml-2 bg-red-100 text-red-800 text-xs px-2 py-0.5 rounded-full" id="contadorErrores">0</span>
                                </button>
                                <button onclick="openTab(event, 'ordenes')" class="tab-link px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 border-transparent text-gray-500 hover:bg-gray-50 transition-colors">
                                    <i class="ri-list-ordered mr-2"></i> Órdenes
                                    <span class="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full" id="contadorOrdenes">0</span>
                                </button>
                                <button onclick="openTab(event, 'logs')" class="tab-link px-4 py-3 text-sm font-medium whitespace-nowrap border-b-2 border-transparent text-gray-500 hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-file-alt mr-2"></i> Logs
                                </button>
                            </nav>
                        </div>
                        
                        <div class="p-6 space-y-6">
                            <!-- Resumen -->
                            <div id="resumen" class="tab-content">
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <!-- Tarjeta Verde -->
                                    <div class="p-4 bg-green-50 rounded-lg border border-green-100">
                                        <div class="flex items-center gap-3">
                                            <div class="p-2 bg-green-100 rounded-full">
                                                <i class="ri-check-line text-green-600"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm text-gray-600">Órdenes procesadas</p>
                                                <p class="text-2xl font-bold text-gray-900" id="ordenesProcessadas">0</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Tarjeta Roja -->
                                    <div class="p-4 bg-red-50 rounded-lg border border-red-100">
                                        <div class="flex items-center gap-3">
                                            <div class="p-2 bg-red-100 rounded-full">
                                                <i class="ri-close-circle-line text-red-600"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm text-gray-500">Órdenes con errores</p>
                                                <p class="text-2xl font-bold text-gray-900" id="ordenesConErrores">0</p>
                                            </div>
                                        </div>
                                    </div>
                                

                                 <div class="p-4 bg-blue-50 rounded-lg border border-blue-100">
                                        <div class="flex items-center gap-3">
                                            <div class="p-2 bg-blue-100 rounded-full">
                                                <i class="ri-arrow-left-right-fill  text-blue-600"></i>
                                            </div>
                                            <div>
                                                <p class="text-sm text-gray-500">Total de Órdenes obtenidas por el servicio</p>
                                                <p class="text-2xl font-bold text-gray-900" id="ordenesRecibidas">0</p>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                
                                
                                <!-- Mensaje de éxito -->
                                <div class="mt-6 p-4 bg-green-50 rounded-lg border border-green-100" id="mensajeExito">
                                    <div class="flex items-center gap-3">
                                        <i class="ri-check-line text-green-600"></i>
                                        <div>
                                            <h3 class="font-medium text-green-800">Proceso completado exitosamente</h3>
                                            <p class="text-sm text-green-700 mt-1">Todas las órdenes fueron procesadas correctamente</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Mensaje de advertencia (inicialmente oculto) -->
                                <div class="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-100 hidden" id="mensajeAdvertencia">
                                    <div class="flex items-center gap-3">
                                        <i class="ri-error-warning-line text-yellow-600"></i>
                                        <div>
                                            <h3 class="font-medium text-yellow-800">La importación se completó con advertencias</h3>
                                            <p class="text-sm text-yellow-700 mt-1">Algunas órdenes no pudieron procesarse correctamente</p>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                            
                            
                            <!-- Errores -->
                            <div id="errores" class="tab-content hidden">
                                <div class="mb-6">
                                    <h2 class="text-lg font-medium text-gray-900 mb-2">Errores encontrados</h2>
                                    <p class="text-sm text-gray-500">Listado de problemas detectados durante la importación</p>
                                </div>
                                
                                <!-- Empty State -->
                                <div id="sinErrores" class="text-center py-8 bg-gray-50 rounded-lg">
                                    <div class="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                                        <i class="ri-check-line text-green-600"></i>
                                    </div>
                                    <h3 class="text-lg font-medium text-gray-900">No se encontraron errores</h3>
                                    <p class="mt-1 text-sm text-gray-500">Todas las órdenes se procesaron correctamente</p>
                                </div>
                                
                                <!-- Tabla de errores (inicialmente oculta) -->
                                <div id="tablaErroresContainer" class="hidden">
                                    <!-- El contenido se generará dinámicamente -->
                                </div>
                            </div>
                            
                            <!-- Órdenes -->
                            <div id="ordenes" class="tab-content hidden">
                               <div class="mb-6">
                                 <h2 class="text-lg font-medium text-gray-900 mb-2">Órdenes cargadas</h2>
                                 <p class="text-sm text-gray-500">Listado de las ordenes que fueron cargadas</p>
                             </div>
                                <div class="overflow-x-auto rounded-lg border border-gray-200">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID Orden</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Cliente</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Fecha</th>
                                                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Estado</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200" id="tablaOrdenes">
                                            <!-- Contenido dinámico -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- Logs -->
                            <div id="logs" class="tab-content hidden">
                                <div class="space-y-4" id="logs-container">
                                    <!-- Los logs de API se renderizarán aquí dinámicamente -->
                                </div>
                            
                            </div>
                   
                </div>
                 <!-- Footer -->
                 <div class="px-6 py-4 bg-gray-50 border-t border-gray-200">
                            <div class="flex flex-col md:flex-row justify-between items-center gap-3">
                                <p class="text-sm text-gray-600">
                                    Tiempo total de procesamiento: 
                                    <span class="font-medium" id="tiempoProcesamiento">0 segundos</span>
                                </p>
                                <div class="flex gap-3">
                                    <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" id="downloadImportResults">
                                         <i class="ri-download-line mr-2"></i>Descargar Excel
                                    </button>
                                    <button type="button" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700" data-dismiss="modal" id="cerrarModal">
                                        <i class="ri-check-line mr-2"></i>Aceptar
                                    </button>
                                </div>
                            </div>
                 </div>
            </div>
        </div>
    </div>
</div>
</div>
</div>

<div id="alertFinalizacionImportacion" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Importacion Finalizada') ?></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <span><?= _('La importacion de pedidos ha finalizado') ?></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <span id="mensajeFinalizarImportacion"></span>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cerrar</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="alertFinalizacionExportacion" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Exportacion Finalizada') ?></h4>
            </div>
            <div class="modal-body">
                <p><?= _('La exportacion a finalizado') ?><br>
                </p>
                <ul id="listaRutasExportadas" class="list-group" style="margin-bottom: 20px;margin-top: 10px;margin-left: 10px;">

                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cerrar</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="modalImportarPedidosConParametros" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Importar Pedidos') ?></h4>
            </div>
            <div class="modal-body">

                <div class="row" style="margin-left: 0px; margin-right: 0px;">
                    <div class="col-lg-4 col-md-4 col-sm-4">
                        <div><span><?= _("Fecha Desde") ?></span></div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <input id="fechaImportDesde" value='<?= _($hoy_ddmmyy); ?>' type="text" placeholder='<?= _($hoy_ddmmyy); ?>' class="text-center form-control">
                    </div>
                </div>
                <div class="row" style="margin-left: 0px; margin-right: 0px;">
                    <div class="col-lg-4 col-md-4 col-sm-4">
                        <div><span><?= _("Fecha Hasta") ?></span></div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <input id="fechaImportHasta" value='<?= _($hoy_ddmmyy); ?>' type="text" placeholder='<?= _($hoy_ddmmyy); ?>' class="text-center form-control">
                    </div>
                </div>

                <div class="row" style="margin-left: 0px; margin-right: 0px;">
                    <div class="col-lg-4 col-md-4 col-sm-4">
                        <div><span><?= _("BELN") ?></span></div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <input id="beln_importar" type="text" class="text-center form-control">
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="btnImportarPedidosConParametros"><?= _('Importar'); ?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cerrar</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="resultImportarPedidosCustomWs" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Importacion Finalizada') ?></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <span><?= _('La importacion de pedidos ha sido finalizada') ?></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <?= _('Cantidad de pedidos recibida: ') ?> <span id="cant_recibidos"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <?= _('Cantidad de pedidos insertados: ') ?> <span id="cant_insertados"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <?= _('Cantidad de pedidos actualizados: ') ?> <span id="cant_actualizados"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <?= _('Cantidad de pedidos con error: ') ?> <span id="cant_error"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?= _('Cerrar') ?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="resultFinalizarImportacionWS" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Importación Finalizada') ?></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="row resultrow">
                            <span class="col-lg-6"><?= _('Cantidad de pedidos recibidos: ') ?></span> 
                            <span class="col-lg-6" id="cant_recibidosImportacionWS"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="row resultrow">
                            <span class="col-lg-6"><?= _('Cantidad de pedidos insertados: ') ?> </span>
                            <span class="col-lg-6" id="cant_insertadosImportacionWS"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="row resultrow">
                            <span class="col-lg-6"><?= _('Cantidad de pedidos actualizados: ') ?> </span>
                            <span class="col-lg-6" id="cant_actualizadosImportacionWS"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <div class="row resultrow">
                            <span class="col-lg-6"><?= _('Cantidad de pedidos con error: ') ?> </span>
                            <span class="col-lg-6" id="cant_errorImportacionWS"></span>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">
                        <span id="resume"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal"><?= _('Cerrar') ?></button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<!-- QS-743 modal de geocodificacion para usuarios flash -->
<div id="geocode-clients" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div id="map"></div>
                <form class="map-content" action="javascript:void(0);" autocomplete="off">
                    <input type="hidden" placeholder="" value="" id="geocode-clients-id" />
                    <div class="map-content-header">
                        <h3><?= _("Resolver direcciones.") ?></h3>
                        <span data-dismiss="modal" aria-label="Close" class="close glyphicon glyphicon-remove"></span>
                    </div>
                    <div class="inline field mt-15">
                        <div class="field-title" id="geocode-clients-name"></div>
                        <div class="field-subtitle" id="geocode-clients-code"></div>
                    </div>
                    <div class="inline field mt-9 address">
                        <span class="glyphicon glyphicon-map-marker"></span>
                        <div class="field-title" id="geocode-clients-address"></div>
                        <span id="geocode-clients-address-tooltip" class="glyphicon glyphicon-question-sign" title="<?= _("Dirección que proviene del pedido importado"); ?>"></span>
                    </div>
                    <div class="field mt-15 mb-15">
                        <div class="field-title"><?= _("Dirección a ubicar") ?></div>
                        <div class="address-input-fields">
                            <input type="text" placeholder="" value="" id="geocode-clients-address-to-geocode" autocomplete="false" />
                            <span class="glyphicon glyphicon-search"></span>
                        </div>
                    </div>
                    <div class="botonera">

                        <input class="btn btn-default" type="submit" name="submit" value="<?= _("Saltar") ?>" onclick="omitClientGeocode()" />
                        <input class="btn btn-default" id="geocode-clients-save" type="submit" name="submit" value="<?= _("Guardar") ?>" onclick="saveClientGeocode();" />

                    </div>
                    <div class="footer-geocode-clients">
                        <div class="rest-geocoding">
                            <span id="clients-geocoded">0</span>
                            <span>
                                Hay <strong> <span id="clients-to-geocode">0</span> cliente/s</strong> sin geocodificar...
                            </span>
                            <span class="glyphicon glyphicon-chevron-down"></span>
                        </div>
                        <div class="geocode-list-clients">

                        </div>
                    </div>
                </form>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

<div id="client-geocode-popover-model" class="client-geocode-popover" style="display: none">
    <!-- /.popover model -->
    <div class="title !font-poppins text-xl"></div>
    <div class="content">Para poder planificarlos deben estar geocodificados.</div>
    <div class="buttons">
        <button class="close">Cerrar</button>
        <button class="open">Geocodificar</button>
    </div>
</div> <!-- /.popover model -->



<div class="modal fade modalSubscriptionMessage" id="modalSubscriptionMessage" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="title-subscription-modal">Período de prueba por finalizar</h5>
                <button type="button" class="close close-modal-message" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div>
                    <div id="text-warning-subscription" class="text-modal-warning-subscription"> <?= $dias_trial == 1 ? "Queda <span> $dias_trial día" : "Quedan <span> $dias_trial días" ?> de prueba del Trial. </span> </div>
                    <div> Suscribe para seguir disfrutando de Flash.</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary otro-momento" data-dismiss="modal">EN OTRO MOMENTO</button>
                <button type="button" class="btn btn-primary suscribirse" id="go-to-subscription">QUIERO SUSCRIBIRME</button>
            </div>
        </div>
    </div>
</div>


<!-- QS-817 modal de pedidos -->
<div id="generar-pedidos" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-body">
                <div id="mapclientes"></div>


                <div class="left-column-modal">

                    <div class="left-column-header">
                        <div>
                            <div class="back-arrow-button" data-dismiss="modal"></div>
                            <h3><?= _("Crear pedidos") ?></h3>
                        </div>
                        <div class="show-cod-pedido-dinamic">
                            <span>#</span>
                            <span class="span-cod-pedido" id="show-cod-pedido"></span>
                        </div>

                    </div>
                    <div class="left-column-subheader">
                        <div class="ventanaAyuda">
                            <div class="mensajeAyuda">
                                <div class="texto-mensajeAyuda">
                                    <div class="texto-descAyuda">Avanzar al siguiente campo</div>
                                    <div class="texto-leyendaAyuda"><span>Tab</span></div>
                                </div>
                                <div class="texto-mensajeAyuda">
                                    <div class="texto-descAyuda">Enviar el formulario </div>
                                    <div class="texto-leyendaAyuda"><span>Ctrl</span> <span>Enter</span></div>
                                </div>


                                <div class="top-right-help">
                                    <div class="pop" data-toggle="popover" data-placement="right" data-content="Conoce todo lo que puedes hacer desde el Pedido Manual.</br> <a href=&quot;https://docs.quadminds.com/docs/crear-pedidos-manual&quot; target=&quot;_blank&quot;>Ver mas información</a>" data-original-title="">
                                        <svg class="icon-pedidos-manual icon_help"></svg>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>


                    <form id="pedidos-manuales-form">
                        <div class="fields-generar-pedidos">

                            <div class="pedido-info-general">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual"></svg>
                                </div>
                                <div class="container-pedido-info-general">
                                    <div class="pedido-tipo">
                                        <input class="" type="radio" id="pedido-entrega" name="tipo-pedido" value="1" checked>
                                        <label for="pedido-entrega">Entrega</label>

                                        <input class="" type="radio" id="pedido-retiro" name="tipo-pedido" value="3">
                                        <label for="pedido-retiro">Retiro</label>
                                    </div>

                                    <div class="pedido-codigo">
                                        <div class="text-codigo-pedido"> Órden </div>
                                        <div class="input-codigo-pedido">
                                            <input name="pedidosManualCodigo" id="pedidosManualCodigo" type="text" data-toggle="tooltip" data-html="true" data-placement="top" title="Código Pedido: </br> *) El sistema genera un número. </br> *) O puedes consignar número propio.">
                                        </div>

                                    </div>
                                </div>

                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon-nombre"></svg>
                                </div>
                                <div class="autocompleteClienteDiv field-container">
                                    <input name="pedidosManualNombre" id="pedidosManualNombre" type="text" placeholder="Nombre del cliente" value="" />
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container" style="margin: auto;">

                                </div>
                                <div>
                                    <div class="input-codigo-cliente" style="display: none;">
                                        <input name="pedidosManualNuevoCodigoCliente" id="pedidosManualNuevoCodigoCliente" type="text" placeholder="Código cliente" value="" />
                                        <label for="activate-agrupar" id="activate-agrupar-label" class="input-check-agrupar" >
                                            <input type="checkbox" id='activate-agrupar' class='activate-agrupar' />
                                            <div class="text-check-agrupar"> Desagrupar</div>
                                            <div class="text-clickable" data-toggle="tooltip" data-html="true" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Generar otro punto en el mapa para esta orden.">
                                                <svg class="icon-pedidos-manual icon_info-filled"></svg>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="field field--radio-direccion">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual "></svg>
                                </div>
                                <div class="flex-space-around radio-field-container radio-button-direccion">
                                    <label class="label-radio-direccion" for="location-direccion">
                                        <input class="radio-direccion" type="radio" id="location-direccion" name="client-location" onclick="clientLocation(this);">
                                        <span><?= _("Domicilio"); ?></span>
                                    </label>

                                    <label class="label-radio-direccion" for="location-coordenadas">
                                        <input class="radio-direccion" type="radio" id="location-coordenadas" name="client-location" onclick="clientLocation(this);">
                                        <span><?= _("Coordenadas"); ?></span>
                                    </label>
                                </div>
                            </div>
                            <div class="field field--direccion">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_location"></svg>
                                </div>
                                <div class="field-container">
                                    <input name="direccionCliente" type="text" id="direccionCliente" class="form-control input-sm" placeholder="Dirección de entrega" value="" />
                                    <input name="coordenadas" type="text" id="inputCoordenadas" class="form-control input-sm" value="" placeholder="-99.9999,-99.9999" />
                                </div>

                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_date_range"></svg>
                                </div>
                                <div class="input-group divfechapedido radio-field-container">
                                    <input id="fechaPedidoManual" name="fechaPedidoManual" class="form-control" autocomplete="off" type="text" placeholder="Fecha de entrega" data-toggle="tooltip" data-placement="bottom" onkeydown="event.preventDefault()" title="Fecha de Entrega del pedido." />
                                    <span class="input-group-addon no-pointers"><img src="./images/calendar.svg" alt="calendar image" class="absolute svg-icon" /></span>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual"></svg>
                                </div>
                                <div class="container-timewindow field-container">
                                    <div class="activate-timewindow-container">
                                        <input type="checkbox" id='activate-timewindows' class='activate-timewindows' />
                                        <label for="activate-timewindows" id="activate-timewindows-label" class="label-activate-windows" data-toggle="tooltip" data-html="true" data-placement="bottom" title="Horario de Entrega/Retiro del cliente: </br> Hasta 2 Franjas Horarias.">
                                            <svg class="icon-timewindows"></svg>
                                            <div>Añadir Franjas Horarias <span class="optional-text">(opcional)</span></div>
                                        </label>
                                    </div>

                                    <div class="timewindows-sliders-container">
                                        <div>
                                            <div class="flex-column">
                                                <label for="activar-primera-franjahoraria">
                                                    <input type="checkbox" id="activar-primera-franjahoraria" data-target="containerFirstSliderTimeZone">
                                                    <span>Ventana horaria 1</span>
                                                </label>
                                                <input type="text" class="horario" id="horario" disabled value="09:00 Hs a 18:00 Hs">
                                            </div>
                                            <div class="SliderDisabled" id="containerFirstSliderTimeZone">
                                                <div class="divSlider">
                                                    <div id="franja-horaria-pedido-manual"></div>
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <div class="flex-column">
                                                <label for="activar-segunda-franjahoraria">
                                                    <input type="checkbox" id="activar-segunda-franjahoraria" data-target="containerSecondSliderTimeZone">
                                                    <span class="segunda-franja-text-span">Ventana horaria 2</span>
                                                </label>
                                                <input type="text" class="horario" id="second-horario" disabled value="09:00 Hs a 18:00 Hs">
                                            </div>
                                            <div class="SliderDisabled" id="containerSecondSliderTimeZone">
                                                <div class="divSlider">
                                                    <div id="segunda-franja-horaria-pedido-manual"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_clock"></svg>
                                </div>
                                <div class="field-container">
                                    <div class="container-demora">
                                        <div class="text-demora">Duración estimada de la visita es </div>
                                        <input name="pedidosManualDemora" id="pedidosManualDemora" type="text" placeholder="demora" value="5" data-toggle="tooltip" data-placement="top" title="Tiempo de demora de la visita en el cliente." />
                                        <div class="text-demora">min </div>
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_notes"></svg>
                                </div>
                                <div id="show-pedidosManualNotas" class="text-clickable">
                                    Añadir Notas
                                    <span class="optional-text">(opcional)</span>
                                </div>
                                <div id="container-pedidosManualNotas">
                                    <div class="field-container">
                                        <textarea name="pedidosManualNotas" id="pedidosManualNotas" placeholder="Detalle de Entrega / Retiro *"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_phone"></svg>
                                </div>
                                <div id="show-pedidosManualTelefono" class="text-clickable" data-toggle="tooltip" data-placement="top" title="Permite al conductor contactarse desde la App con el cliente.">
                                    Teléfono de Contacto
                                    <span class="optional-text">(opcional)</span>
                                </div>
                                <div id="container-pedidosManualTelefono">
                                    <div class="field-container">
                                        <input name="pedidosManualTelefono" id="pedidosManualTelefono" type="text" autocomplete="off" placeholder="Teléfono" value="" />
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_email"></svg>
                                </div>
                                <div id="show-pedidosManualEmail" class="text-clickable" data-toggle="tooltip" data-placement="top" data-html="true" title="Permite notificar al cliente en tiempo real en el Seguimiento del Pedido. </br> *Antes activar Notificaciones en Settings.">
                                    E-mail de Contacto
                                    <span class="optional-text">(opcional)</span>
                                </div>
                                <div id="container-pedidosManualEmail">
                                    <div class="field-container">
                                        <input name="pedidosManualEmail" id="pedidosManualEmail" type="email" placeholder="E-mail Contacto" value="" />
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_mail"></svg>
                                </div>
                                <div id="show-pedidosManualMerchantEmail" class="text-clickable">
                                    E-mail Remitente
                                    <span class="optional-text">(opcional)</span>
                                    <div class="pop" data-toggle="popover" data-placement="right" data-content="Permite notificar a quién envía el pedido. Ejemplo: Tienda o Merchant. </br> *Antes activar <span style='font-weight:700;'>Notificaciones</span> en Settings.  </br> <a href=&quot;https://docs.quadminds.com/docs/notificaciones-al-remitente&quot; target=&quot;_blank&quot;>Ver mas información</a>" data-original-title="">
                                        <svg class="icon-pedidos-manual icon_info-filled"></svg>
                                    </div>
                                </div>
                                <div id="container-pedidosManualMerchantEmail">
                                    <div class="field-container">
                                        <div class="merchant-field-container">
                                            <input name="pedidosManualMerchantEmail" id="pedidosManualMerchantEmail" type="email" placeholder="E-mail Remitente" value="" />
                                        </div>
                                        <div class="merchantWarning" id="containerMerchantWarning">
                                            <div class="merchantWarning-text">
                                                Para utilizar el mail de notificación al Remitente, antes debes activarlo en <span> Settings</span>.
                                            </div>
                                        </div>
                                        <div class="merchantDisabled" id="containerMerchantEmail"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_capacities"></svg>
                                </div>
                                <div id="show-capacities" class="text-clickable" data-toggle="tooltip" data-html="true" data-placement="top" title="Dimensiones del pedido: Peso, Volumen y Dinero. El pedido ocupará un porcentaje % en la capacidad total del vehículo.">
                                    Añadir Capacidades
                                    <span class="optional-text">(opcional)</span>
                                </div>
                                <div class="field-container cortes-container">
                                    <div class="cortes-pedidos">
                                        <div class="capacities-container">
                                            <input name="volumenPedido" id="volumenPedido" type="text" placeholder="Volumen" value="" />
                                            <span class="capacities-legend">m3</span>
                                        </div>
                                    </div>
                                    <div class="cortes-pedidos">
                                        <div class="capacities-container">
                                            <input name="pesoPedido" id="pesoPedido" type="text" placeholder="Peso" value="" />
                                            <span class="capacities-legend">kg</span>
                                        </div>
                                    </div>
                                    <div class="cortes-pedidos">
                                        <div class="capacities-container">
                                            <input name="dineroPedido" id="dineroPedido" type="text" placeholder="Dinero" value="" />
                                            <span class="capacities-legend">$</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_tag"></svg>
                                </div>
                                <div id="show-pedidosManualAgrupador" class="text-clickable" data-toggle="tooltip" data-html="true" data-placement="top" title="Asigná una categoría a los pedidos y permite ver agrupados en el mapa. Ejemplo: Diferenciar por tipo de Tienda, Cliente o Producto.">
                                    Añadir Etiqueta
                                    <span class="optional-text">(opcional)</span>
                                </div>
                                <div id="container-pedidosManualAgrupador">
                                    <div class="autocompleteAgrupadoresDiv field-container">
                                        <input name="pedidosManualAgrupador" id="pedidosManualAgrupador" type="text" placeholder="Añadir Etiqueta (opcional) - presiona ENTER para agregar" value="" />
                                    </div>
                                </div>
                            </div>
                            <div class="field mt-15 mb-15">
                                <div class="icon-container">
                                    <svg class="icon-pedidos-manual icon_config"></svg>
                                </div>
                                <div id="show-pedidosManualHabilidad" class="text-clickable" data-toggle="tooltip" data-html="true" data-placement="top">
                                    Añadir Habilidad
                                    <span class="optional-text">(opcional)</span>
                                    <div class="pop" data-toggle="popover" data-placement="right" data-content="Permite la posibilidad de rutear tus pedidos de acuerdo a las habilidades o características especiales de tus vehículos. </br> <a href=&quot;https://docs.quadminds.com/docs/optimizaci%C3%B3n-con-habilidades&quot; target=&quot;_blank&quot;>Ver mas información</a>" data-original-title="">
                                        <svg class="icon-pedidos-manual icon_info-filled"></svg>
                                    </div>
                                </div>
                                <div id="container-pedidosManualHabilidad">
                                    <div class="autocompleteAgrupadoresDiv field-container">
                                        <input name="pedidosManualHabilidad" id="pedidosManualHabilidad" type="text" placeholder="Añadir Habilidad (opcional) - presiona ENTER para agregar" value="" />
                                    </div>
                                </div>
                            </div>
                            <div>
                                <input name="coordenadas" id="inputCoordenadas" type="hidden" value="" />
                                <input id="pedidosManualLatitud" name="latitud" type="hidden" value="0" />
                                <input id="pedidosManualLongitud" name="longitud" type="hidden" value="0" />
                                <input id="pedidosManualCodigo_cliente" name="codigo_cliente" type="hidden" value="0" />
                                <input id="idPedido" type="hidden">
                                <div class="mensajepedidomanual"></div>
                            </div>


                        </div>
                        <div class="modal-footer">
                            <div class="botonera">
                                <button type="button" class="close-button" data-dismiss="modal">CERRAR</button>
                                <div class="container-keep-creating">
                                    <input id="keep-creating" type="checkbox">
                                    <label for="keep-creating"><?= _("Seguir creando") ?></label>
                                </div>
                                <button type="button" type="submit" class="submit submit-button" id="submitPedidoManual">CREAR</button>
                            </div>
                        </div>
                    </form>

                    <!--<div class ="mensajegenerarpedido">
              <div>Pedidos creados:</div>
              <span>3</span>

              <div class="dropdown-toggle dropdown-toggle-pedidos" data-toggle="dropdown">
                  <span class="caret" style="margin-left: 7px"></span>
              </div>

              <ul class="dropdown-menu dropdown-pedidoscreados" role="menu">
                  <li>
                      <div class="pedidoscreados-posicion">1</div>
                      <div class="pedidoscreados-nombre">
                          <div>Juan Rodriguez</div>
                          <div>Mendoza 1200, Buenos Aires, Argentina</div>
                      </div>
                      <div class="pedidoscreados-borrar">
                          <span class="glyphicon glyphicon-trash"></span>
                      </div>
                  </li>
                  <li>

                      <div class="pedidoscreados-posicion">2</div>
                      <div class="pedidoscreados-nombre">
                          <div>Carlos Perez</div>
                          <div>Juramento 2340, Buenos Aires, Argentina</div>
                      </div>
                      <div class="pedidoscreados-borrar">
                          <span class="glyphicon glyphicon-trash"></span>
                      </div>

                  </li>
                  <li>
                      <div class="pedidoscreados-posicion">3</div>
                      <div class="pedidoscreados-nombre">
                          <div>Maria Laura Martinez</div>
                          <div>Olazabal 1200, Buenos Aires, Argentina</div>
                      </div>
                      <div class="pedidoscreados-borrar">
                          <span class="glyphicon glyphicon-trash"></span>
                      </div>
                  </li>
                  <li>
                      <div class="pedidoscreados-posicion">4</div>
                      <div class="pedidoscreados-nombre">
                          <div>José González</div>
                          <div>Mendoza 1200, Buenos Aires, Argentina</div>
                      </div>
                      <div class="pedidoscreados-borrar">
                          <span class="glyphicon glyphicon-trash"></span>
                      </div>
                  </li>
              </ul>
          </div>-->

                </div>

                <!-- <div class="right-column-modal">
            <div class="mapclientes">

            </div>
        </div>-->

            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->


<div id="DialogdescargarPedidosChameleon" style="display: none;">
    <div class="container">
        <div class="row">
            <div class="form-group">
                <div>
                    <div class="textoCabeceraDescargas">Seleccione el conjunto de pedidos y el formato de salida:</div>
                    <div class="titulosDescargar">Pedidos Planificados</div>
                    <div class="custom-control custom-radio">
                        <input type="radio" value="1" id="Planificado1" checked="checked" name="opcionDescarga" class="custom-control-input">
                        <label class="custom-control-label titulosReportesDescargables" for="Planificado1">Sin detalle de producto</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" value="2" id="Planificado2" name="opcionDescarga" class="custom-control-input">
                        <label class="custom-control-label titulosReportesDescargables" for="Planificado2">Con detalle de producto</label><sup>Nuevo</sup>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" value="3" id="Planificado3" name="opcionDescarga" class="custom-control-input">
                        <label class="custom-control-label titulosReportesDescargables" for="Planificado3">Agrupados por vehículo</label><sup>Nuevo</sup>
                    </div>
                    <div class="titulosDescargar">Pedidos No Planificados</div>
                    <div class="custom-control custom-radio">
                        <input type="radio" value="4" id="NoPlanificado1" name="opcionDescarga" class="custom-control-input">
                        <label class="custom-control-label titulosReportesDescargables" for="NoPlanificado1">Sin detalle de producto</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" value="5" id="NoPlanificado2" name="opcionDescarga" class="custom-control-input">
                        <label class="custom-control-label titulosReportesDescargables" for="NoPlanificado2">Con detalle de producto</label>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="modal" id="alertMustSelectDeviceToUnnassign" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <img src="images/alert-circle-outline.png" style="width: 18px; height: 18px;" />
                <img src="images/icon_truck_grey.svg" style="width: 18px; height: 18px;" />
                <h5 class="modal-title">
                    Seleccionar Vehículo
                </h5>
                <button type="button" class="close close-modal-message" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Para remover esta orden, debes seleccionar el vehículo al que fue asignada.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary-qm-ghost" data-dismiss="modal">Aceptar</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="pedidos-merchants-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h5 class="modal-title">Importar pedidos de vendedores</h5>
            </div>
            <div class="modal-body">
                <div hidden id="spinner"></div>
                <div class="hidden modal-body-container">
                    <div class="subtitulo-merchant">Selecciona los vendedores que quiera importar los pedidos</div>
                    <!-- <div class="table-container">
                        <div class="table-wrapper" id="table-wrapper">
                            <table class="table table-bordered" id="table-merchants">
                                <thead>
                                    <tr>
                                        <th scope="col">
                                            <input type="checkbox" class="custom-control-input checkPedidoMerchant" id="checkAllPedidosMerchants">
                                            <label class="custom-control-label label-table-header" for="checkAllPedidosMerchants">Vendedores</label>
                                        </th>
                                        <th scope="col" class="display-center">
                                            <label class="custom-control-label label-table-header display-center" for="">Integración</label>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div> -->
                    <div id="sellers-by-provider">
                    </div>
                </div>
                <div class="hidden modal-body-empty">
                    <img src="images/vendedores_empty.png" />
                    <div class="text-block-empty-merchant">
                        <div class="empty-merchant-subtitle">
                            Todavía no hay Vendedores
                        </div>
                        <div class="empty-merchant-text">
                            Invita vendedores para poder importar sus órdenes <br>
                            desde aquí:
                        </div>
                    </div>
                    <div class="footer-empty-merchant">
                        <button type="button" class="btn btn-primary empty-merchant" id="portal-ordenes" onclick="window.location.href='/configuracion.php?active=merchants';">
                            Invitar vendedores
                        </button>
                    </div>
                </div>
            </div>
            <div class="hidden modal-footer button-containers-merchants">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary importar-pedidos-merchants" id="importar-pedidos-merchants">
                    Importar
                </button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="pedidos-merchants-modal-result" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <svg class="svg-resultados-merchant"></svg>
                    Resultado de la importación
                </h5>
            </div>
            <div class="modal-body">
                <div class="hidden modal-result-items">
                    <div class="subtitulo-merchant">Los siguientes pedidos fueron importados desde vendedores:</div>

                    <div class="table-container">
                        <div class="table-wrapper">
                            <table class="table table-bordered" id="table-merchants-results">
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="accordion-merchants-results">

                    </div>
                </div>

                <div id="modal-accordeon-result"></div>

                <div class="hidden modal-result-empty ">
                    <img src="images/icono_empty.png" />
                    <div class="text-block-empty-merchant">
                        <div class="empty-merchant-subtitle">
                            No hay pedidos de Vendedores
                        </div>
                        <div class="empty-merchant-text">
                            Por el momento no se encontraron pedidos de <br>
                            tus Vendedores para entregar. Intentalo más tarde.
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="report-btn">
                    <img src="images/LeftIcon.svg" alt="report-icon" />
                    <p>Reporte</p>
                </button>
                <button style="color: #fff !important;background-color:#1473F7;height:38px" type="button" class="btn btn-secondary btn-close" type="button" class="btn btn-secondary btn-close" data-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<order-templates id="orderTemplates" idUsuario="<? echo $usuario_id ?>" isOpen='0'></order-templates>
<div class="modal" tabindex="-1" id="modal-remover-rutas" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remover Rutas</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Esta acción removerá la carga de todos los vehículos.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary button-cancel" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary button-accept" id="action-remover-rutas">Remover</button>
            </div>
        </div>
    </div>
</div>

<div class="modal" tabindex="-1" id="modal-alerta" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <svg class="svg-alert svg-mask-properties"></svg>
                <h5 class="modal-title" id="titleAlertDialog"></h5>
            </div>
            <div class="modal-body" id="textAlertDialog">>
                <p>Esta acción removerá la carga de todos los vehículos.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary button-dismiss" data-dismiss="modal">Aceptar</button>
            </div>
        </div>
    </div>
</div>


<div class="modal" tabindex="-1" id="routingOptionsModal" role="dialog">
    <div class="modal-dialog !w-[400px]" role="document">
        <div class="modal-content !w-[400px]">
            <div class="flex items-center py-4 modal-header">
                <h4 class="flex-grow text-2xl font-semibold text-left modal-title font-poppins"><?= _("Opciones de Ruteo") ?></h3>
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
            </div>
            <div class='options-toolbar options-toolbar--small' id='optionsToolbar'>
                <div class='options-toolbar__content'>

                    <div class='options-toolbar__menu-container'>
                        <div class="options-toolbar__menu">
                            <div class='options-toolbar__menu-section'>
                                <div class='options-toolbar__menu-title'>
                                    <span class="!text-xl mr-1 text-primaryq-300 mt-0.5"><i class="ri-treasure-map-line"></i></span>
                                    <span><?= _("Opciones de ruta") ?></span>
                                </div>
                                <!--<div class="input-group toggle-button options-toolbar__menu-row">
                                    <input id="checkToll"
                                        type="checkbox"
                                        aria-label= "Autopistas"
                                        data-style= "toggle"
                                        data-toggle= "toggle"
                                        data-on=""
                                        data-off=""
                                        >
                                    <label>Evitar Peajes</label>
                                </div>   -->
                                <div class="options-toolbar__menu-clients-container options-toolbar__menu-row">
                                    <div class="input-group toggle-button options-toolbar__menu-clients-text">
                                        <input id="groupClientes" type="checkbox" aria-label="Agrupar Clientes" data-style="toggle" data-toggle="toggle" data-on="" data-off="" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_AgruparClientes">
                                        <label><?= _("Agrupar Clientes") ?></label>
                                    </div>
                                    <div class="options-toolbar__menu-clients-value hidden w-[]" id="clientsOptions">
                                        <div class="options-toolbar__menu-clients-value-subtext "><?= _(" radio ") ?></div>
                                        <input class="clients-value-input" id="metrosClientes" type="number" />
                                    </div>

                                    <div class="text-clickable" data-toggle="tooltip" data-placement="right" data-html="true" title="Agrupa clientes dentro de un radio para optimizar aún más la ruta (en metros).">
                                        <span class="!text-xl text-primaryq-200 mt-0.5"><i class="ri-information-fill"></i></span>
                                    </div>

                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="font-roboto text-[14px]">Tiempo de Servicio</span>
                                    <div class="flex items-center">
                                        <input id="minServiceTime" type="number" value="0" class="border border-gray-100 text-[14px] text-center !rounded-md pl-1 h-[20px] pr-12 w-32">
                                        <span class="text-gray-100 text-[10px] !-ml-10">mins<span>
                                    </div>
                                    <div class="text-clickable !ml-4" data-toggle="tooltip" data-placement="right" data-html="true" title="Tiempo de servicio por defecto para clientes que no tengan una demora establecida.">
                                        <span class="!text-xl text-primaryq-200 mt-0.5"><i class="ri-information-fill"></i></span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="font-roboto text-[14px]">Objetivo de optimización</span>
                                    <div class="flex items-center">
                                        <select id="singleRouteOptimizationObjective" class="form-control !rounded-md !p-0 h-[20px]" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_ObjetivoDeOptimizacion">
                                            <option value="Distancias" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_ObjetivoDeOptimizacion_Distancia"><?= _("Distancia") ?></option>
                                            <option value="Tiempos" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_ObjetivoDeOptimizacion_Tiempo"><?= _("Tiempos") ?></option>
                                        </select>
                                    </div>
                                    <div class="text-clickable !ml-4" data-toggle="tooltip" data-placement="right" data-html="true" title="<?= _("Seleccione si prefiere reducir tiempos o distancias cuando se optimiza una ruta individual o una zona.") ?>">
                                        <span class="!text-xl text-primaryq-200 mt-0.5"><i class="ri-information-fill"></i></span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="font-roboto text-[14px]">Penalización por Espera</span>
                                    <div class="flex items-center">
                                        <select id="optimizationWaitingPenalty" class="form-control !rounded-md !p-0 h-[20px]" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_ObjetivoDeOptimizacion_Distancia">
                                            <option value="50" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_PenalizaciónPorEspera_Alta"><?= _("Alta") ?></option>
                                            <option value="25" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_PenalizaciónPorEspera_Media"><?= _("Media") ?></option>
                                            <option value="10" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_PenalizaciónPorEspera_Baja"><?= _("Baja") ?></option>
                                            <option value="0" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_PenalizaciónPorEspera_Baja"><?= _("Sin Penalizar") ?></option>
                                        </select>
                                    </div>
                                    <div class="text-clickable !ml-4" data-toggle="tooltip" data-placement="right" data-html="true" title="<?= _("Esta variable ajusta la penalización por esperar al horario de apertura de la parada.") ?>">
                                        <span class="!text-xl text-primaryq-200 mt-0.5"><i class="ri-information-fill"></i></span>
                                    </div>
                                </div>
                            </div>
                            <div class='options-toolbar__menu-section'>
                                <div class='options-toolbar__menu-title'>
                                    <span class="!text-xl mr-1 text-primaryq-300 mt-0.5"><i class="ri-car-line"></i></span>
                                    <span><?= _("Tipo de vehículos") ?></span>
                                </div>
                                <div class="options-toolbar__menu-cartype">
                                    <div class="options-toolbar__menu-cartype-radio">
                                        <label>
                                            <input type="radio" name="routingmode" id="routing-truck" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_TipoDeVehiculos_Pesados" checked>
                                            <svg class="options-toolbar__svg-truck options-toolbar__svg"></svg>
                                            Pesados
                                        </label>
                                    </div>
                                    <div class="options-toolbar__menu-cartype-radio">
                                        <label>
                                            <input type="radio" name="routingmode" id="routing-car" data-gtm="Planificador_Menu_PlanificarRutas_OpcionesDeRuteo_TipoDeVehiculos_Livianos">
                                            <svg class="options-toolbar__svg-car options-toolbar__svg"></svg>
                                            Livianos
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="frecuencia-visita-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    Añadir órdenes para el <span id="frecuencia-visita-modal-title">día</span>
                </h5>
            </div>
            <div class="modal-body">
                <div id="frecuencia-visita-modal-body-success">
                    <div class="texto-resultado-frecuencia-visita texto-superior">
                        Se añadirá una orden por cada cliente que tenga asignado el <span class="visitas-dia">[día de visita]</span> como día de visita.
                    </div>

                    <div class="titulo-resultado-frecuencia-visita">CLIENTES CON DÍA DE VISITA ASIGNADO</div>

                    <div class="calculo-visitas-container">
                        <div class="calculo-visitas-row">
                            <div class="icon-calculo-visitas"><i class="ri-calendar-event-line" style="font-size: 16px;"></i></div>
                            <div class="dia-calculo-visitas">Para el <span class="visitas-dia">[día de visita]</span></div>
                            <div class="cantidad-calculo-visitas"><span id="cantidad-calculo-clientes">0</span>/<span id="cantidad-total-clientes">0</span></div>
                        </div>

                        <div class="excluidos-calculo-visitas-row">
                            <div class="icon-calculo-visitas"><i class="ri-error-warning-line" style="font-size: 16px; color:red;"></i></div>
                            <div class="excluidos-texto">Sin vehículo asignado</div>
                            <div class="excluidos-cantidad"><span id="excluidos-calculo-clientes">0</span></div>
                        </div>
                    </div>

                    <div class="titulo-resultado-frecuencia-visita">VEHÍCULOS CON CLIENTES ASIGNADOS</div>

                    <div class="calculo-visitas-container">
                        <div class="calculo-visitas-row">
                            <div class="icon-calculo-visitas"><i class="ri-route-line" style="font-size: 16px;"></i></div>
                            <div class="dia-calculo-visitas">Rutas para el día <span id="rutas-dia">[día de visita]</span></div>
                            <div class="cantidad-calculo-visitas"><span id="cantidad-calculo-rutas">12</span>/<span id="cantidad-total-dispositivos">40</span></div>
                        </div>
                    </div>
                </div>
                <div id="frecuencia-visita-modal-body-failed">
                    <div class="texto-resultado-frecuencia-visita texto-superior">
                        No encontramos Clientes que tengan asignado un día de visita para la Fecha de inicio de Ruta que seleccionaste.
                    </div>
                </div>

                <input type="hidden" id="pedidos-creados-dia" value="0">

            </div>
            <div class="modal-footer">
                <div class="texto-resultado-frecuencia-visita texto-footer">
                    Para asignar vehículos y días de visita, o hacer cambios, debes hacerlo desde “Clientes”
                </div>
                <div class="botones-footer">
                    <button type="button" class="boton boton-cancelar" data-dismiss="modal">Cancelar</button>
                    <button id="abrir-capa-cliente" type="button" class="boton boton-clientes" data-dismiss="modal">Ir a Clientes</button>
                    <button id="generar-rutas-cliente-dia" type="button" class="boton boton-generar">Añadir</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="pedidos-previamente-creados-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    Ya tienes órdenes para el <span id="pedidos-previamente-creados-fecha">día</span>
                </h5>
            </div>
            <div class="modal-body">
                <div class="texto-body">
                    Se volverán a añadir órdenes por cada cliente que tenga asignado el <span id="pedidos-previamente-creados-dia">[día de visita]</span> como día de visita.
                </div>
            </div>
            <div class="modal-footer">
                <div class="botones-footer">
                    <button type="button" class="boton boton-cancelar" data-dismiss="modal">No añadir</button>
                    <button id="pedidos-previamente-creados-boton-crear" type="button" class="boton boton-generar">Añadir de todas formas</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="pedidos-ungroup-modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    Desagrupar Órdenes 
                    <div class="pop" data-toggle="popover" data-placement="right" data-content="Desagrupar</a>" data-original-title="">
                        <svg class="icon-ungroup-info icon_help_line"></svg>
                    </div>
                </h5>
            </div>
            <div class="modal-body">
                <div class="texto-body">
                    <span id="pedidos-codigos-ungroup-texto"></span><span id="pedidos-codigos-ungroup">[codigo de pedido]</span>?
                </div>
            </div>
            <div class="modal-footer">
                <div class="botones-footer">
                    <button type="button" class="boton boton-cancelar" data-dismiss="modal">No, cancelar</button>
                    <button id="pedidos-ungroup-boton-crear" type="button" class="boton boton-generar">Sí, desagrupar</button>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($enablePublishingModal || $enableDynamicRoutingModal) : ?>
    <div class="modal" tabindex="-1" id="dynamic-routing-modal" role="dialog" aria-hidden="true" style="z-index: **********;" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog--monorepo-iframe" role="document">
            <div class="modal-content modal-content--monorepo-iframe">
                <iframe id="dynamic-routing-iframe" class="monorepo-iframe" src="<?php echo $dynamicRoutingModalsUrl; ?>"></iframe> 
            </div>
        </div>
    </div>
<?php endif; ?>
<div id="modalImportarPedidosChess" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title"><?= _('Importar Pedidos') ?></h4>
            </div>
            <div class="modal-body">

                <div class="row" style="margin-left: 0px; margin-right: 0px;">
                    <div class="col-lg-4 col-md-4 col-sm-4">
                        <div><span><?= _("Fecha Desde") ?></span></div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <input id="fechaImportDesdeChess" value='<?= _($hoy_ddmmyy); ?>' type="text" placeholder='<?= _($hoy_ddmmyy); ?>' class="text-center form-control">
                    </div>
                </div>
                <div class="row" style="margin-left: 0px; margin-right: 0px;">
                    <div class="col-lg-4 col-md-4 col-sm-4">
                        <div><span><?= _("Fecha Hasta") ?></span></div>
                    </div>
                    <div class="col-lg-6 col-md-6 col-sm-6">
                        <input id="fechaImportHastaChess" value='<?= _($hoy_ddmmyy); ?>' type="text" placeholder='<?= _($hoy_ddmmyy); ?>' class="text-center form-control">
                    </div>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" id="btnImportarPedidosChess"><?= _('Importar'); ?></button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cerrar</button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->

</html>

<script>
    $("#generar-pedidos").on('shown.bs.modal', function() {
        initAutocomplete();
    });
</script>
