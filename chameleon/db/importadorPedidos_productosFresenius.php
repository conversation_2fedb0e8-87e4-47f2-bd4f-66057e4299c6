<?php
include_once 'util_chameleon.php';
include_once('../../util_db.php');
include_once('../../util_session.php');
include_once('../../classes/PHPExcel.php');
include_once('../../services/geocoding/geocoding_util.php');
include_once('../../services/geocoding/geocoding_settings.php');

manejar_sesion();

//ini_set('display_errors', 1);
//error_reporting(E_ALL);

$usuario_id = $_SESSION['usuario_id'];
rastrear('importadorPedidos_productosFresenius.php','', $usuario_id);

$ahora = date("Y-m-d H:i:s");
$hoy = date("Y-m-d");

define("MAIN_USUARIO_FRESENIUS_PRODUCTOS", 5488);
define("ID_CORTE_POR_PESO", 2);

//Mappping usuarios fresenius
define(
    "MAPPING_USUARIOS_FRESENIUS",
    serialize(
        array(
            '3412A001SKDC' => 5488,
            '3416A007SKDC' => 5488,
            '3417A008ROPT' => 5488,
            '3417A008COPT' => 5488,
            '3412A001BOLI' => 5488,
            '3416A007BOLI' => 5488,
            '3412A001PTEF' => 5488,
            '3412A001RTPT' => 5488,
            '3420A013CCCC' => 7017,
            '3017A008CODR' => 5492,
            '3017A008COBL' => 5492,
            '3017A008ROBL' => 5490,
            '3017A008RODR' => 5490,
            '3017A008RGNO' => 17903,
            '3017A008SABL' => 17903

        )
    )
);

define('URBANO_COLOR','#FF0000');
define('URBANO_MARCA', 'Bultos');
define('AMOUNT_BULK', 40);

/*
 * MEMOIZED FUNCTIONS
 */
$memoClienteHasCoordinates = memoize('clienteHasCoordinates');
/*
 *
 */


$usuariosMap = unserialize(MAPPING_USUARIOS_FRESENIUS);

if ((($_FILES["file"]["type"] == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") || ($_FILES["file"]["type"] == "application/vnd.ms-excel")) && ($_FILES["file"]["size"] < 4000000)){
    if ($_FILES["file"]["error"] > 0){
        $error = $_FILES["file"]["error"];
    }
    else {
        $archivo = "../../upload_ruteador/".$usuario_id."_".uniqid(mt_rand(),true)."_".$_FILES["file"]["name"];
        $success = move_uploaded_file($_FILES["file"]["tmp_name"], $archivo);
        $log_interfaces_name = basename($_SERVER['SCRIPT_NAME']);
        $log_interfaces_data = substr($archivo, 6);
        $log_interfaces_data = mysql_real_escape_string($log_interfaces_data);
        logDatosInterfaces($log_interfaces_data,$log_interfaces_name,$usuario_id);
    }
} else{
    loggear("Error al subir el archivo productosFresenius ".print_r($_FILES["file"]), "productosFresenius");
    return;
}


$FileType = PHPExcel_IOFactory::identify($archivo);
$objReader = PHPExcel_IOFactory::createReader($FileType);
switch ($FileType) {
    case 'Excel2007':
    case 'Excel2003XML':
    case 'Excel5':
    case 'OOCalc':
    case 'SYLK':
        break;
}

$cacheMethod = PHPExcel_CachedObjectStorageFactory:: cache_to_phpTemp;
$cacheSettings = array( ' memoryCacheSize ' => '64MB');
PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);

$objReader->setReadDataOnly(false);
$objPHPExcel = $objReader->load($archivo);

//  Get worksheet dimensions
$objWorksheet = $objPHPExcel->getActiveSheet();
$highestRow = $objWorksheet->getHighestRow();
$countPedidos = 0;
$resultArray = [];
for ($row = 2; $row <= $highestRow; $row++) {

    $almacen = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(0, $row)->getValue());
    $pExpedicion = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(1, $row)->getValue());
    $centro = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(2, $row)->getValue());
    $constructedUsuarioIdentifier = trim($centro).trim($pExpedicion).trim($almacen);
    $codigo_cliente = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(3, $row)->getValue());
    $razon_social = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(4, $row)->getValue());
    $domicilioEntrega = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(5, $row)->getValue());
    $ciudad = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(6, $row)->getValue());
    $provincia = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(7, $row)->getValue());
    $domicilio = trim($domicilioEntrega).', '.trim($ciudad).', '.trim($provincia).', '.'Argentina';
    $telefono = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(8, $row)->getValue());
    $bultos = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(9, $row)->getValue());
    $remito = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(10, $row)->getValue());
    $dateTimeObject = PHPExcel_Shared_Date::ExcelToPHPObject($objWorksheet->getCellByColumnAndRow(11, $row)->getValue());
    $fecha_pedido =  $dateTimeObject->format('Y-m-d');
    $codigo_pedido = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(12, $row)->getValue());
    $codigo_producto = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(13, $row)->getValue());
    $descripcion_producto = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(14, $row)->getValue());
    $cantidad_producto = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(15, $row)->getValue());
    $peso = mysql_real_escape_string($objWorksheet->getCellByColumnAndRow(16, $row)->getValue());
    $agrupacion = $pExpedicion;

    if (!$codigo_cliente) {
        continue;
    }
    //Map usuario
    $usuario_id = $usuariosMap[$constructedUsuarioIdentifier];
    $cortesWithId = ['bultos' => 5];
    $cortes = [
        'bultos' => $bultos,
        'peso'   => $peso
    ];

    $cliente = constructCliente($codigo_cliente,$razon_social,$domicilio,$telefono);
    $pedido = constructPedido($codigo_pedido,$fecha_pedido,$agrupacion);
    $parametros = constructParametros($remito);
    $producto = constructProductos($codigo_producto,$descripcion_producto,$cantidad_producto);

    $idCliente = handleCliente($cliente, array_keys($cliente), $usuario_id);
    $idPedido = getIdPedido($pedido['codigo_pedido'], $idCliente, $usuario_id);
    $idProducto = getIdProductoByCodigo($producto['codigo'], $usuario_id);
    if(!$idProducto){
        $idProducto = insertProducto($producto['codigo'],$producto['descripcion'],$usuario_id);
    }
    $producto['id_producto'] = $idProducto;

    $geoPosicionado = ($memoClienteHasCoordinates($idCliente)) ? 'true' : 'false';

    if($geoPosicionado == 'false'){
        $sinGeo++;
    }

    $resultArray[] = array(
        'clienteGeocodificado' =>$geoPosicionado,
        'codigo_pedido' => $codigo_pedido,
        'codigo_cliente' => $codigo_cliente
    );

    if ($idPedido) {
        $existingPedido = getExistingPedido($idPedido, array_keys($pedido));
        if ($modifiedPedido = pedidoNeedsUpdate($pedido, $existingPedido)) {
            updatePedido($idPedido, $modifiedPedido);
            $modificado++;
        }
        insertCorteBultos($idPedido,$cortes['bultos']);
        insertCortePeso($idPedido,$cortes['peso']);
        insertOrUpdateDetalles($idPedido,$producto,$cantidad_producto);
        setAndSaveParametros($parametros, $idPedido,$cortes,$idCliente,$usuario_id);
    } else {
        $idPedido = setAndSavePedido($pedido, $idCliente, $usuario_id);
        insertCorteBultos($idPedido,$cortes['bultos']);
        insertCortePeso($idPedido,$cortes['peso']);
        insertOrUpdateDetalles($idPedido,$producto,$cantidad_producto);
        setAndSaveParametros($parametros, $idPedido,$cortes,$idCliente,$usuario_id);
        $countPedidos++;
    }
}


$data = [];
$data[0] = $countPedidos;
$data[1] = [
    "modificados" => ["cantidad" => $modificado],
    "sinGeo" => ['cantidad' => $sinGeo],
    "logFile" => ['link' => writeCsvLogFile($resultArray,$usuario_id)]
];
echo json_encode($data);


//Funciones:
/**
 * @param $logData
 * @param $logName
 * @param $idUsuario
 */
function logDatosInterfaces($logData, $logName, $idUsuario){
    $log_interfaces_ts = date("Y-m-d H:i:s");
    mysql_query(
        "INSERT INTO log_interfaces (id_usuario, interfaz, fecha_ejecucion, valor) 
                VALUES($idUsuario, '$logName', '$log_interfaces_ts', '$logData')"
    );
}

/**
 * @param $codigo_cliente
 * @param $razon_social
 * @param $domicilio
 * @param $telefono
 * @return array
 */
function constructCliente($codigo_cliente,$razon_social,$domicilio,$telefono){
    return [
        'codigo' => $codigo_cliente,
        'razon_social' => $razon_social,
        'domicilio' => $domicilio,
        'telefono_contacto' => $telefono ?: 'No Informado',
    ];
}

/**
 * @param $cliente
 * @param $clienteFieldsWithDbMapping
 * @param $idUsuario
 * @return int|string|void
 */
function handleCliente($cliente, $clienteFieldsWithDbMapping, $idUsuario){
    $clienteExists = clienteExists($cliente['codigo'], $idUsuario);
    if($clienteExists){
        list($idCliente, $habilitado) = array_values($clienteExists);
    }
    if ($idCliente) {
        ($habilitado == 1) ?: habilitarCliente($idCliente);
        $existingCliente = getExistingCliente($idCliente, $clienteFieldsWithDbMapping);
        if ($modifiedCliente = clienteNeedsUpdate($cliente, $existingCliente)) {
            updateCliente($modifiedCliente, $idCliente);

            return $idCliente;
        }

        return $idCliente;
    } else {
        $cliente = setupClienteForInsert($cliente, $idUsuario);
        $idCliente = insertCliente($cliente);

        return $idCliente;
    }
}

/**
 * @param $codigoCliente
 * @param $idUsuario
 * @return array|bool
 */
function clienteExists($codigoCliente, $idUsuario){
    $codigoCliente = mysql_real_escape_string($codigoCliente);
    $queryResult = mysql_query(
        "SELECT c.id, c.habilitado
                                FROM clientes c
                                WHERE c.codigo = '$codigoCliente'
                                AND c.habilitado = 1
                                AND c.id_usuario = $idUsuario LIMIT 1"
    );
    if (!mysql_num_rows($queryResult)) {
        return false;
    }

    return mysql_fetch_assoc($queryResult);
}

/**
 * @param $idCliente
 */
function habilitarCliente($idCliente){
    $ahora = date('Y-m-d H:i:s');
    mysql_query(
        "UPDATE clientes SET habilitado = 1, update_timestamp = '$ahora',estado = 'HABILITADO' WHERE id = $idCliente"
    );
}

/**
 * @param $idCliente
 * @param $fields
 * @return array
 */
function getExistingCliente($idCliente, $fields){
    $queryResult = mysql_query(
        "SELECT ".implode(',', $fields)." FROM clientes
                                WHERE id = $idCliente"
    );

    return mysql_fetch_assoc($queryResult);
}

/**
 * @param $dataCliente
 * @param $clienteActual
 * @return array|bool|Closure
 */
function clienteNeedsUpdate($dataCliente, $clienteActual){
    $noUpdateZeroCoordinate = function ($value, $key) {
        if ($key == 'latitud') {
            return ($value) ? true : false;
        }
        if ($key == 'longitud') {
            return ($value) ? true : false;
        }

        return true;
    };
    if (!arrayHaveSameKeys($dataCliente, $clienteActual) || !$camposModificados = array_filter(
            getArrayModifiedKeys($dataCliente, $clienteActual),
            $noUpdateZeroCoordinate,
            ARRAY_FILTER_USE_BOTH
        )
    ) {
        return false;
    }
    $ahora = date('Y-m-d H:i:s');
    $camposModificados['update_timestamp'] = $ahora;

    return $camposModificados;
}

/**
 * @param $array1
 * @param $array2
 * @return bool
 */
function arrayHaveSameKeys($array1, $array2){
    return !array_diff_key($array1, $array2) && !array_diff_key($array2, $array1);
}

/**
 * @param $new
 * @param $original
 * @return array|bool
 */
function getArrayModifiedKeys($new, $original){
    $compareStrings = function ($string1, $string2) {
        return (strcmp((string)$string1, (string)$string2) === 0) ? true : false;
    };
    $camposModificados = [];
    foreach ($original as $key => $value) {
        if (!$compareStrings($value, $new[$key])) {
            $camposModificados[$key] = $new[$key];
        }
    }
    if (empty($camposModificados)) {
        return false;
    }

    return $camposModificados;
}

/**
 * @param $camposModificados
 * @param $idCliente
 */
function updateCliente($camposModificados, $idCliente){
    $query = "UPDATE clientes SET";
    $comma = " ";
    foreach ($camposModificados as $key => $val) {
        if (!empty($val)) {
            $query .= $comma.$key." = '".mysql_real_escape_string(trim($val))."'";
            $comma = ", ";
        }
    }
    $query = $query." WHERE id = '".$idCliente."'";
    mysql_query($query);
}

/**
 * @param $cliente
 * @param $idUsuario
 * @return array
 */
function setupClienteForInsert($cliente, $idUsuario){
    $cliente['latitud'] = 0;
    $cliente['longitud'] = 0;
    if (!($cliente['latitud'] && $cliente['longitud'])) {
        $geocodeResult = geocode_here_single_string($cliente['domicilio']);
        if (gecode_google_is_success($geocodeResult['AD_RESULT'][0])) {
            $cliente['latitud'] = $geocodeResult['AD_RESULT'][0]['latitud'];
            $cliente['longitud'] = $geocodeResult['AD_RESULT'][0]['longitud'];
        }
    }
    $fixedFields = array(
        'domicilio_original' => $cliente['domicilio'],
        'update_timestamp' => (new DateTime())->format('Y-m-d H:i:s'),
        'id_usuario' => $idUsuario,
    );

    return array_merge($cliente, $fixedFields);
}

/**
 * @param $cliente
 * @return int
 */
function insertCliente($cliente){
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($cliente));
    $value = $cleanStrings(array_values($cliente));
    $query = "INSERT INTO clientes ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    $success = mysql_query($query);

    return ($success) ? mysql_insert_id() : 0;
}

/**
 * @param $codigo_pedido
 * @param $fecha_pedido
 * @param $agrupacion
 * @return array
 */
function constructPedido($codigo_pedido,$fecha_pedido,$agrupacion){
    $pedido = [
        'codigo_pedido' => $codigo_pedido,
        'fecha_pedido' => $fecha_pedido,
        'agrupacion' => $agrupacion
    ];

    return $pedido;
}

/**
 * @param $codigoPedido
 * @param $idCliente
 * @param $idUsuario
 * @return int|string
 */
function getIdPedido($codigoPedido, $idCliente, $idUsuario)
{
    $codigoPedido = mysql_real_escape_string($codigoPedido);
    $queryResult = mysql_query(
        "SELECT id
                                FROM pedidos p
                                WHERE p.id_cliente = $idCliente
                                AND p.codigo_pedido = '$codigoPedido'
                                AND p.id_usuario = $idUsuario"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return 0;
    }

    return mysql_result($queryResult, 0, 0);
}

/**
 * @param $idPedido
 * @param $fields
 * @return array
 */
function getExistingPedido($idPedido, $fields)
{
    $queryResult = mysql_query(
        "SELECT ".implode(',', $fields)."
                                FROM pedidos
                                WHERE id = $idPedido"
    );
    if (mysql_num_rows($queryResult) == 0) {
        return array();
    }

    return mysql_fetch_assoc($queryResult);
}

/**
 * @param $newPedidoData
 * @param $existingPedidoData
 * @return array|bool
 */
function pedidoNeedsUpdate($newPedidoData, $existingPedidoData)
{
    if (!arrayHaveSameKeys($newPedidoData, $existingPedidoData) ||
        !$camposModificados = getArrayModifiedKeys($newPedidoData, $existingPedidoData)
    ) {
        return false;
    }

    return $camposModificados;
}

/**
 * @param $idPedido
 * @param $camposModificados
 */
function updatePedido($idPedido, $camposModificados)
{
    $query = "UPDATE pedidos SET";
    $comma = " ";
    foreach ($camposModificados as $key => $val) {
        if (!empty($val)) {
            $query .= $comma.$key." = '".mysql_real_escape_string(trim($val))."'";
            $comma = ", ";
        }
    }
    $query = $query." WHERE id = ".$idPedido;
    mysql_query($query);
}

/**
 * @param $remito
 * @return array
 */
function constructParametros($remito){
    return [
        'remito' => $remito,
    ];
}

/**
 * @param $codigo_producto
 * @param $descripcion_producto
 * @param $cantidad_producto
 * @return array
 */
function constructProductos($codigo_producto,$descripcion_producto,$cantidad_producto){
    return [
        'codigo' => $codigo_producto,
        'descripcion' => $descripcion_producto,
        'cantidad' => $cantidad_producto,
    ];
}

/**
 * @param $codigoProducto
 * @param $idUsuario
 * @return int|string
 */
function getIdProductoByCodigo($codigoProducto, $idUsuario)
{
    $query = "SELECT id FROM maestro_productos WHERE codigo = '$codigoProducto' AND id_usuario = '$idUsuario'";
    $r_query = mysql_query($query);
    if(mysql_num_rows($r_query)){
        $id = mysql_result($r_query,0,0);
    }else{
        return false;
    }
    return $id;
}

/**
 * @param $codigoProducto
 * @param $descripcion
 * @param $idUsuario
 * @return int
 */
function insertProducto($codigoProducto, $descripcion, $idUsuario)
{
    $queryInsertProducto = "INSERT INTO maestro_productos (codigo, descripcion, id_usuario, id_estado_producto) VALUES ('%s', '%s','%s', '%s')";

    return ($result = mysql_query(
        sprintf($queryInsertProducto, $codigoProducto, $descripcion, $idUsuario, 1)
    )) ? mysql_insert_id() : 0;
}

/**
 * @param $item
 * @return string
 */
function getHashFromItem($item)
{
    return sha1(serialize($item));
}

/**
 * @param $idPedido
 * @param $entities
 * @return array
 */
function getDbHashedByEntity($idPedido, $entities)
{
    $hashesByEntity = [];
    $query = "SELECT parametro, valor
              FROM pedidos_parametros
              WHERE id_pedido = %s
              AND parametro IN (%s)";
    $queryResult = mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                "'".implode("','", $entities)."'",
            ]
        )
    );
    if (mysql_num_rows($queryResult) == 0) {
        return [];
    }
    while ($row = mysql_fetch_assoc($queryResult)) {
        $hashesByEntity[$row['parametro']] = $row['valor'];
    }

    return $hashesByEntity;
}

/**
 * @param $idPedido
 * @param $entity
 * @return mixed
 */
function wipeEntity($idPedido, $entity)
{
    if ($idPedido != 0) {
        mysql_query("DELETE FROM $entity WHERE id_pedido = $idPedido");
    }

    return $entity;
}

/**
 * @param $cortes
 * @param $idPedido
 * @param $cortesWithId
 */
function setAndSaveCortes($cortes, $idPedido, $cortesWithId)
{
    insertarPedidoCortes(setupPedidoCortesForInsert($cortes, $idPedido, $cortesWithId));
}

/**
 * @param $pedidoData
 * @param $idPedido
 * @param $cortes
 * @return array
 */
function setupPedidoCortesForInsert($pedidoData, $idPedido, $cortes)
{
    $pedidoCortes = [];
    foreach ($cortes as $key => $value) {
        $corte = [];
        $corte['id_pedido'] = $idPedido;
        $corte['id_corte'] = $value;
        $corte['valor'] = $pedidoData[$key];
        $pedidoCortes[] = $corte;
    }

    return $pedidoCortes;
}

/**
 * @param $pedidoCortesData
 */
function insertarPedidoCortes($pedidoCortesData)
{
    foreach ($pedidoCortesData as $pedidoCorte) {
        insertPedidoCorte($pedidoCorte);
    }
}

/**
 * @param $pedidoCorte
 */
function insertPedidoCorte($pedidoCorte)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedidoCorte));
    $value = $cleanStrings(array_values($pedidoCorte));
    $query = "INSERT INTO pedidos_cortes ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
}

/**
 * @param $entity
 * @param $hash
 * @param $idPedido
 */
function updateEntityHash($entity, $hash, $idPedido)
{
    $query = "INSERT INTO pedidos_parametros (id_pedido, parametro, valor)
             VALUES ('%s', '%s', '%s') ON DUPLICATE KEY UPDATE valor = VALUES(valor)";
    mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                $entity,
                $hash,
            ]
        )
    );
}

/**
 * @param $productos
 * @param $idPedido
 */
function setAndSaveDetalles($productos, $idPedido)
{
    insertarPedidoDetalles(setupPedidoDetallesForInsert($productos, $idPedido));
}

/**
 * @param $productos
 * @param $idPedido
 * @return array
 */
function  setupPedidoDetallesForInsert($productos, $idPedido)
{
    $pedidoDetalles = [];
    foreach ($productos as $producto) {
        $detalle = [];
        $detalle['id_pedido'] = $idPedido;
        $detalle['id_producto'] = $producto['id_producto'];
        $detalle['cantidad'] = $producto['cantidad'];
        $pedidoDetalles[] = $detalle;
    }

    return $pedidoDetalles;
}

/**
 * @param $pedidoDetallesData
 */
function insertarPedidoDetalles($pedidoDetallesData)
{
    foreach ($pedidoDetallesData as $pedidoDetalle) {
        insertPedidoDetalle($pedidoDetalle);
    }
}

/**
 * @param $pedidoDetalle
 */
function insertPedidoDetalle($pedidoDetalle)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedidoDetalle));
    $value = $cleanStrings(array_values($pedidoDetalle));
    $query = "INSERT INTO pedidos_detalles ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
}

/**
 * @param $parametros
 * @param $idPedido
 * @param $cortes
 * @param $idCliente
 * @param $idUsuario
 */
function setAndSaveParametros($parametros, $idPedido,$cortes,$idCliente,$idUsuario)
{
    $dia = date('Y-m-d');
    $bultos = 0;
    $r_bultos = mysql_query("SELECT pc.valor
                                  FROM pedidos p 
                                  INNER JOIN pedidos_cortes pc ON pc.id_pedido = p.id
                                  WHERE p.id_usuario = $idUsuario
                                  AND p.fecha_insercion LIKE '$dia%'
                                  AND pc.id_corte = '5' 
                                  AND p.id_cliente = $idCliente");
    while ($row = mysql_fetch_assoc($r_bultos)){
        $bultos = $bultos + intval($row['valor'],10);
    }
    $bultos = $bultos+$cortes['bultos'];
    $mustAssignColor = ($bultos >= AMOUNT_BULK) ? true : false;
    (!$mustAssignColor) ?: saveParametroColor($idPedido);
    insertarPedidoParametros(setupPedidoParametrosForInsert($parametros, $idPedido, array_keys($parametros)));
}

/**
 * @param $idPedido
 */
function saveParametroColor($idPedido)
{
    $query = "INSERT INTO pedidos_parametros (id_pedido, parametro, valor) VALUES ('%s', '%s', '%s')";
    mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                'URBANO_COLOR',
                URBANO_COLOR,
            ]
        )
    );
    mysql_query(
        vsprintf(
            $query,
            [
                $idPedido,
                'URBANO_MARCA',
                URBANO_MARCA,
            ]
        )
    );
}

/**
 * @param $pedidoData
 * @param $idPedido
 * @param $parametros
 * @return array
 */
function setupPedidoParametrosForInsert($pedidoData, $idPedido, $parametros)
{
    $pedidoParametros = [];
    foreach ($parametros as $parametro) {
        $newParametro = [];
        $newParametro['id_pedido'] = $idPedido;
        $newParametro['parametro'] = $parametro;
        $newParametro['valor'] = $pedidoData[$parametro];
        $pedidoParametros[] = $newParametro;
    }

    return $pedidoParametros;
}

/**
 * @param $pedidoParametrosData
 */
function insertarPedidoParametros($pedidoParametrosData)
{
    foreach ($pedidoParametrosData as $pedidoParametro) {
        insertPedidoParametro($pedidoParametro);
    }
}

/**
 * @param $pedidoParametros
 */
function insertPedidoParametro($pedidoParametros)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedidoParametros));
    $value = $cleanStrings(array_values($pedidoParametros));
    $query = "INSERT INTO pedidos_parametros ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    mysql_query($query);
}

/**
 * @param $pedido
 * @param $idCliente
 * @param $idUsuario
 * @return int
 */
function setAndSavePedido($pedido, $idCliente, $idUsuario)
{
    $pedido = setupPedidoForInsert($pedido, $idCliente, $idUsuario);
    $idPedido = insertPedido($pedido);

    return $idPedido;
}

/**
 * @param $pedidoData
 * @param $idCliente
 * @param $idUsuario
 * @return mixed
 */
function setupPedidoForInsert($pedidoData, $idCliente, $idUsuario)
{
    $ahora = date('Y-m-d H:i:s');
    $pedido = $pedidoData;
    $pedido['id_cliente'] = $idCliente;
    $pedido['fecha_insercion'] = $ahora;
    $pedido['id_usuario'] = $idUsuario;

    return $pedido;
}

/**
 * @param $pedido
 * @return int
 */
function insertPedido($pedido)
{
    $cleanStrings = function ($arrayWithStrings) {
        return array_map(
            function ($string) {
                return mysql_real_escape_string(trim($string));
            },
            $arrayWithStrings
        );
    };
    $key = $cleanStrings(array_keys($pedido));
    $value = $cleanStrings(array_values($pedido));
    $query = "INSERT INTO pedidos ( ".implode(',', $key).") VALUES('".implode("','", $value)."')";
    loggear($query, "fresenius_pedidos");
    mysql_query($query);
    $idPedido = mysql_insert_id();

    return $idPedido;
}

/**
 * @param $idPedido
 * @param $bultos
 * <AUTHOR>
 */
function insertCorteBultos($idPedido,$bultos){
    $r_corteBultos = mysql_query("SELECT id,valor FROM pedidos_cortes WHERE id_pedido = '$idPedido' AND id_corte='5'");

    if (mysql_num_rows($r_corteBultos)) {
        $corteBultos = mysql_fetch_assoc($r_corteBultos);
        $id_corte_bultos = $corteBultos['id'];
        $bultosPedido = $bultos;
        mysql_query("UPDATE pedidos_cortes SET valor = '$bultosPedido' WHERE id = '$id_corte_bultos'");
    }else{
        mysql_query("INSERT INTO pedidos_cortes (id_pedido, id_corte, valor) VALUES ('$idPedido','5','$bultos')");
    }
}

/**
 * @param $idPedido
 * @param $peso
 */
function insertCortePeso($idPedido, $peso){
    $r_cortePeso = mysql_query("SELECT id,valor FROM pedidos_cortes WHERE id_pedido = '$idPedido' AND id_corte= '".ID_CORTE_POR_PESO."'");

    if (mysql_num_rows($r_cortePeso)) {
        $cortepeso = mysql_fetch_assoc($r_cortePeso);
        $id_corte_peso = $cortepeso['id'];
        $pesoPedido= $peso;
        mysql_query("UPDATE pedidos_cortes SET valor = '$pesoPedido' WHERE id = '$id_corte_peso'");
    }else{
        mysql_query("INSERT INTO pedidos_cortes (id_pedido, id_corte, valor) VALUES ('$idPedido', '".ID_CORTE_POR_PESO ."','$peso')");
    }
}

/**
 * @param $idPedido
 * @param $productos
 * @param $cantidad_producto
 */
function insertOrUpdateDetalles($idPedido,$producto,$cantidad_producto){
    $id_producto = $producto['id_producto'];
    $r_pedido_detalle = mysql_query("SELECT id
                                            FROM pedidos_detalles
                                            WHERE id_pedido = $idPedido
                                            AND id_producto = $id_producto
                                            ");

    if (!mysql_num_rows($r_pedido_detalle)){
        $sql = "INSERT INTO pedidos_detalles (id_pedido, id_producto, cantidad)
                      VALUES ($idPedido, '$id_producto', '$cantidad_producto')";
        mysql_query($sql);
    }else{
        if ($idPedido != 0) {
            $sql = "UPDATE pedidos_detalles SET cantidad='$cantidad_producto' WHERE id_pedido='$idPedido'";
            mysql_query($sql);
        }
    }
}
function writeCsvLogFile($resultData,$id_usuario)
{

    usort($resultData, function ($a, $b) {
        return strcmp(key($a), key($b));
    });
    $resultData = array_reduce($resultData, function ($formattedArray, $row) {
        if ($row['clienteGeocodificado'] === 'false') {
            $formattedArray[] = array(
                $row['codigo_pedido'],
                $row['codigo_cliente'],
                'Cliente no Geocodificado'
            );
        }
        return $formattedArray;
    });
    $filename = "/upload_ruteador/" . 'log_noGeoNoExistente_' . $id_usuario . '_' .
        (new DateTime())->format('Y-m-d_H-i-s') . ".csv";
    $path = "/var/www/saas" . $filename;
    $fp = fopen($path, 'w');
    fputcsv($fp, array(
        'codigo_pedido',
        'codigo_cliente',
        'estado'
    ), ';');
    foreach ($resultData as $row) {
        fputcsv($fp, $row, ';');
    }
    fclose($fp);
    return $filename;
}

function clienteHasCoordinates($idCliente)
{
    $queryResult = mysql_query("SELECT id
                                FROM clientes
                                WHERE latitud IS NOT NULL AND latitud !=0
                                AND longitud IS NOT NULL AND longitud !=0
                                AND id = $idCliente
                                LIMIT 1");
    if (mysql_num_rows($queryResult) == 0) {
        return false;
    }
    return true;
}
function memoize($function)
{
    return function () use ($function) {
        static $cache = [];
        $args = func_get_args();
        $key = md5(serialize($args));
        if (!isset($cache[$key])) {
            $cache[$key] = call_user_func_array($function, $args);
        }

        return $cache[$key];
    };
}
