<?php

use DomainEvents\DomainEventPublisher;
use DomainEvents\Pois\PublishToExternalPubSub;

include_once 'util_chameleon.php';
include_once 'util_importador_flash.php';
include_once('../../util_db.php');
include_once('../../util_session.php');
include_once('../../util_google_storage.php');
include_once('../../classes/PHPExcel.php');
include_once('../../classes/DomainObjects/Cliente.php');
include_once('../../classes/DomainObjects/UsuariosParametro.php');
include_once('../../classes/DomainObjects/Merchant.php');
include_once('../../classes/DomainObjects/PedidosParametros.php');
include_once('../../classes/DomainObjects/OrdersTemplates.php');
include_once('../../services/geocoding/geocoding_util.php');
include_once('../../services/geocoding/geocoding_settings.php');
include_once('../../track_and_trace/util_track_and_trace.php');
include_once('../../classes/DomainObjects/Producto.php');
include_once('../../classes/DomainEvents/DomainEventPublisher.php');
include_once('../../classes/DomainEvents/Pois/handlers/PublishToExternalPubSub.php');
include_once('./OrderTemplates/LogRegistry.php');

manejar_sesion();

//ini_set('display_errors', 1);
//error_reporting(E_ALL);

$usuario_id = $_SESSION['usuario_id'];
rastrear('importadorPedidos_flash', '', $usuario_id);

define("ID_USUARIO", $usuario_id);
$ahora = date("Y-m-d H:i:s");
$template_id = _post('template_id');
$fecha_pedido = date('Y-m-d', strtotime(_post('fecha_pedido')));

$tnt = get_usuario_has_parametro($usuario_id, "TRACK_AND_TRACE");
$enableReprogramacion = get_parametro_usuario($usuario_id, "ENABLE_REPROGRAMACION");
$enableReprogramacionAutomatica = get_parametro_usuario($usuario_id, "ENABLE_REPROGRAMACION_AUTOMATICA");
$param_barcode_pedido = get_usuario_has_parametro($usuario_id, "IMPORTADOR_FLASH_BARCODE_PEDIDO");
$merchants = Merchant::getUserMerchants($usuario_id);
$existingMerchantsEmails = Merchant::getKeyValueEmails($merchants);
$orderTemplate = OrdersTemplates::getTemplateById($usuario_id, $template_id? $template_id : 1);
$template = json_decode(json_encode($orderTemplate), JSON_UNESCAPED_SLASHES);
$mapping = json_decode($template['data']['mapping']);

/* We subscribe to POI entity creations or mutations
and pass down the subscriber event handler.
In this case, the event handler will publish to an external PubSub service */
$poiListenerId = DomainEventPublisher::instance()->subscribe(new PublishToExternalPubSub());

// TODO
// Revisar si debo agregar este parámetro o no
// mysql_query("INSERT INTO usuarios_parametros (id_usuario, codigo_parametro, valor) 
//                 VALUES ($usuario_id, 'FLASH_UPLOADER_MODE', 'ADVANCED')
//                 ON DUPLICATE KEY UPDATE valor = 'ADVANCED'");
// removeUserCache($usuario_id);

$q_pais_db      = mysql_query("SELECT pais_geocodificacion, timezone FROM usuarios WHERE id = '$usuario_id'");
$pais_db        = mysql_result($q_pais_db, 0, 0);
$pais_timezone  = mysql_result($q_pais_db, 0, 1);
$pais = get_country_string($pais_db? $pais_db : 'AR');

$ultimo_codigo_cliente = (!empty(usuario_parametro($usuario_id, 'ULTIMO_CODIGO_CLIENTE')) ? usuario_parametro($usuario_id, 'ULTIMO_CODIGO_CLIENTE') : 0);
$ultimo_codigo_pedido = (!empty(usuario_parametro($usuario_id, 'ULTIMO_CODIGO_PEDIDO')) ? usuario_parametro($usuario_id, 'ULTIMO_CODIGO_PEDIDO') : 0);

$archivo = "../../upload_ruteador/" . $usuario_id . "_" . 'OT'.'-'.$template['data']['nombre'].'-'.$template['data']['defecto'].'-'.$template['data']['id'] . '-'  . uniqid(mt_rand(), true) . "_" . $_FILES["file"]["name"];
$success = move_uploaded_file($_FILES["file"]["tmp_name"], $archivo);

$log_interfaces_name = basename($_SERVER['SCRIPT_NAME']);
$log_interfaces_ts = date("Y-m-d H:i:s");
$log_interfaces_data = substr($archivo, 6);
$log_interfaces_data = mysql_real_escape_string($log_interfaces_data);
mysql_query("INSERT INTO log_interfaces (id_usuario, interfaz, fecha_ejecucion, valor) VALUES
                ($usuario_id, '$log_interfaces_name', '$log_interfaces_ts', '$log_interfaces_data')");

$data[0] = 0;
$data[1]['sinCliente']['cantidad'] = 0;
$data[1]['clientes_creados']['cantidad'] = 0;
$data[1]['sinGeo']['cantidad'] = 0;

if ($_FILES["file"]["size"] > 4000000) {
    loggear("Usuario $usuario_id, archivo demasiado grande: " . $_FILES["file"]["size"], 'importador_flash');
    $data[2] = [_('El archivo supera el tamaño máximo de 4MB')];
    echo (json_encode($data));
    die();
}

if ($_FILES["file"]["error"] > 0) {
    loggear("Usuario $usuario_id, error al subir archivo: " . $_FILES["file"]["error"], 'importador_flash');
    $data[2] = [_('Error indeterminado al procesar el archivo. Por favor contacte a Soporte para más información.')];
    echo (json_encode($data));
    die();
}

$FileType = PHPExcel_IOFactory::identify($archivo);
$FileTypes = ["Excel5", "Excel2007", "CSV"];

if (!in_array($FileType, $FileTypes)) {
    loggear("Usuario $usuario_id, Tipo de archivo invalido: $FileType", 'importador_flash');
    $data[2] = [_('Tipo de archivo no reconocido')];
    echo (json_encode($data));
    die();
}

$objReader = PHPExcel_IOFactory::createReader($FileType);

if ($FileType === 'CSV') {
    $delimiter = detectDelimiter($archivo);
    $objReader->setDelimiter($delimiter);
}

$cacheMethod = PHPExcel_CachedObjectStorageFactory::cache_to_phpTemp;
$cacheSettings = array(' memoryCacheSize ' => '64MB');
PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);

$objReader->setReadDataOnly(false);
$objPHPExcel = $objReader->load($archivo);

//  Get worksheet dimensions
$objWorksheet = $objPHPExcel->getActiveSheet();
$highestRow = $objWorksheet->getHighestDataRow();
$highestColumn = ($mapping->ultimaColDatos ? $mapping->ultimaColDatos : 21); //21 es hardcodeado del importador avanzado

$rutas = array();
$emptyRows = 0;
$primer_fila = ($mapping->primerFilaDatos ? $mapping->primerFilaDatos : 0);

$logRegistry = new LogRegistry();

$codigo_pedido_incremental = $ultimo_codigo_pedido;
$codigo_cliente_incremental = $ultimo_codigo_cliente;
for ($row = $primer_fila; $row <= $highestRow; $row++) {

    $error_flag = false;

    if (isRowEmpty($objWorksheet, $row, $highestColumn)) {
        $emptyRows++;
        if ($emptyRows > 2)
            break;
        else
            continue;

    } else $emptyRows = 0;

    if ($mapping->codPedidoAutogenerar) {
        $cod_pedido = $codigo_pedido_incremental + 1;
        $codigo_pedido_incremental++;
    } else {
        $cod_pedido = ($mapping->codPedido != '' ? $objWorksheet->getCellByColumnAndRow($mapping->codPedido, $row)->getCalculatedValue() : '');
    }

    if ($mapping->clienteCodigoAutogenerar) {
        $cliente_codigo = $codigo_cliente_incremental + 1;
        $codigo_cliente_incremental++;
    } else {
        $cliente_codigo = ($mapping->clienteCodigo !== null || $mapping->clienteCodigo !== '' ? $objWorksheet->getCellByColumnAndRow($mapping->clienteCodigo, $row)->getCalculatedValue() : '');
    }

    if($mapping->clienteCodigoTrim){
        $cliente_codigo = ltrim($cliente_codigo, 0);
    }
    
    $cliente_razon = ($mapping->clienteRazon != '' ? $objWorksheet->getCellByColumnAndRow($mapping->clienteRazon, $row)->getValue() : '');
    $telefono = ($mapping->telefono != '' ? $objWorksheet->getCellByColumnAndRow($mapping->telefono, $row)->getValue() : '');
    $email = ($mapping->email != '' ? $objWorksheet->getCellByColumnAndRow($mapping->email, $row)->getValue() : '');
    $direccion = "";
    if ($mapping->direccion != '') {
        foreach (explode(',', $mapping->direccion) as $row_direccion) {
            if ($row_direccion) {
                $item_direccion = $objWorksheet->getCellByColumnAndRow(trim($row_direccion), $row)->getValue();

                if($item_direccion) {
                  $direccion .= $item_direccion . ',';
                }
            }
        }
    }
    $direccion = $direccion? "$direccion $pais" : "";
    $latitud = ($mapping->latitud != '' ? $objWorksheet->getCellByColumnAndRow($mapping->latitud, $row)->getValue() : '');
    $longitud = ($mapping->longitud != '' ? $objWorksheet->getCellByColumnAndRow($mapping->longitud, $row)->getValue() : '');

    $latitud = str_replace(',', '.', $latitud ?? '');
    $longitud = str_replace(',', '.', $longitud ?? '');

    //Validaciones de fecha_pedido
    if (!$mapping->fechaPedidoAutogenerar) {
        $fecha_formato = isset($mapping->fechaFormato)? $mapping->fechaFormato : 'auto';

        if ($fecha_formato == 'auto') {
          $fecha_pedido = ($mapping->fechaPedido != '' ? $objWorksheet->getCellByColumnAndRow($mapping->fechaPedido, $row)->getValue() : '');
        } else {
          $fecha_pedido = ($mapping->fechaPedido != '' ? $objWorksheet->getCellByColumnAndRow($mapping->fechaPedido, $row)->getFormattedValue(PHPExcel_Style_NumberFormat::FORMAT_GENERAL) : '');
        }

        try {
          if (gettype($fecha_pedido) == 'double') { //Excel date/time value es un double
            $fecha_pedido = date('Y-m-d', PHPExcel_Shared_Date::ExcelToPHP($fecha_pedido));
            $fecha_pedido = date('Y-m-d', strtotime($fecha_pedido . ' +1 day'));
          } else {
            $fecha_pedido = createDateFromFormat($fecha_pedido, $fecha_formato);
          }
        } catch(Exception $ex) {
          $fecha_pedido = null;
        }
    }

    if (!$fecha_pedido || $fecha_pedido == '') {
        $error_flag = true;
        $logRegistry->setError($row, 'Ordenes sin Fecha', $rowData);
    }

    if (!$mapping->pedidoOpFijar) {
        $pedido_op = ($mapping->pedidoOpMapeo != '' ? $objWorksheet->getCellByColumnAndRow($mapping->pedidoOpMapeo, $row)->getValue() : '');
    } else {
        $pedido_op = ($mapping->pedidoOp ? $mapping->pedidoOp : "E");
    }
    if ($pedido_op == "R") {
        $operacion = 3;
    } else {
        $operacion = 1;
    }

    //Datos del producto
    $cod_producto = ($mapping->codProducto != '' ? $objWorksheet->getCellByColumnAndRow($mapping->codProducto, $row)->getValue() : '');
    $desc_producto = ($mapping->descProducto != '' ? $objWorksheet->getCellByColumnAndRow($mapping->descProducto, $row)->getValue() : '');
    $cant_producto = ($mapping->cantProducto != '' ? $objWorksheet->getCellByColumnAndRow($mapping->cantProducto, $row)->getCalculatedValue() : '');
    $promocion = ($mapping->promocion != '' ? $objWorksheet->getCellByColumnAndRow($mapping->promocion, $row)->getValue() : '');
    $retornable = ($mapping->retornable != '' ? $objWorksheet->getCellByColumnAndRow($mapping->retornable, $row)->getValue() : '0');
    $retornable = ($retornable == '1' || strtolower(trim($retornable)) == 'si') ? '1' : '0';
    //Cortes
    $peso = ($mapping->peso != '' ? $objWorksheet->getCellByColumnAndRow($mapping->peso, $row)->getCalculatedValue() : '');
    $volumen = ($mapping->volumen != '' ? $objWorksheet->getCellByColumnAndRow($mapping->volumen, $row)->getCalculatedValue() : '');
    $dinero = ($mapping->dinero != '' ? $objWorksheet->getCellByColumnAndRow($mapping->dinero, $row)->getCalculatedValue() : '');
    //Tiempos
    if ($mapping->demoraDuracionFijar) {
      $demora = $mapping->demoraDuracionDefault?? '';
    } else {
      $demora = ($mapping->demoraDuracion != '' ? $objWorksheet->getCellByColumnAndRow($mapping->demoraDuracion, $row)->getCalculatedValue() : '');
    }
    $horario_am = ($mapping->horarioAm != '' ? $objWorksheet->getCellByColumnAndRow($mapping->horarioAm, $row)->getCalculatedValue() : '');
    $horario_pm = ($mapping->horarioPm != '' ? $objWorksheet->getCellByColumnAndRow($mapping->horarioPm , $row)->getCalculatedValue() : '');

    if (!$horario_am)
        $horario_am = '00:00 - 24:00';

    if (!$horario_pm)
        $horario_pm = '24:00 - 24:00';

    $get_time_window = "SELECT time_window FROM clientes WHERE codigo = '$cliente_codigo' AND id_usuario = $usuario_id AND habilitado = 1 ORDER BY id DESC LIMIT 1";

    if (mysql_num_rows(mysql_query($get_time_window))) {
        $result = mysql_fetch_assoc(mysql_query($get_time_window));
        $current_time_window = $result['time_window'];
    }

    $new_time_window = obtener_ventana_cliente_AMPM(['am' => $horario_am, 'pm' => $horario_pm])['time_window'];

    if (!$new_time_window) {
        $new_time_window = 'FFFFFFFFFFFF';
    }

    if (!isset($current_time_window) || ($current_time_window !== $new_time_window && $new_time_window != 'FFFFFFFFFFFF')) {
        $timewindow = $new_time_window;
    } else {
        $timewindow = $current_time_window;
    }

    $comentario = ($mapping->comentario != '' ? $objWorksheet->getCellByColumnAndRow($mapping->comentario, $row)->getValue() : '');
    $agrupacion = ($mapping->agrupacion != '' ? $objWorksheet->getCellByColumnAndRow($mapping->agrupacion, $row)->getValue() : '');

    $merchantEmailList = ($mapping->merchantEmailList != '' ? $objWorksheet->getCellByColumnAndRow($mapping->merchantEmailList, $row)->getValue() : '');
    $merchantEmailList = explode(',', $merchantEmailList);
    $merchantEmailList = array_map('trim', $merchantEmailList);

    $habilidades = ($mapping->habilidades != '' ? $objWorksheet->getCellByColumnAndRow($mapping->habilidades, $row)->getValue() : '');
    $habilidades = explode(',', $habilidades);
    $habilidades = $habilidades[0];

    $vehiculo = ($mapping->vehiculo != '' ? $objWorksheet->getCellByColumnAndRow($mapping->vehiculo, $row)->getValue() : '');
    
    $ungroup = ($mapping->ungroup != '' ? $objWorksheet->getCellByColumnAndRow($mapping->ungroup, $row)->getValue() : '0');
    $ungroup = ($ungroup == '1' || strtolower(trim($ungroup)) == 'si') ? '1' : '0';
    $ordenEntrega = ($mapping->ordenEntrega != '' &&  is_numeric($mapping->ordenEntrega) ? $objWorksheet->getCellByColumnAndRow($mapping->ordenEntrega, $row)->getValue() : null);
    $ordenEntregaFormato = (($mapping->ordenEntregaFormato != '' || $mapping->ordenEntregaFormato) ? $mapping->ordenEntregaFormato : 'integer');
    $ordenEntregaPorFechaHora = (($mapping->ordenEntregaPorFechaHora != '' || $mapping->ordenEntregaPorFechaHora) ? $mapping->ordenEntregaPorFechaHora : false);

    if($ordenEntregaPorFechaHora) {

        error_log('ordenEntrega antes del date: '.$ordenEntrega);
        if (gettype($ordenEntrega) == 'double') { //Excel date/time value es un double
            error_log(' es doble ');
            $ordenEntrega = date('Y-m-d H:i:s', PHPExcel_Shared_Date::ExcelToPHP($ordenEntrega));
        }else{

            try{
                $ordenEntrega = formatearFecha($ordenEntrega, $ordenEntregaFormato);
            } catch(Exception $e) {
                $ordenEntrega = 0;
                loggear('Error al formatear la fecha: '.$e->getMessage(), "orders_templates");
            }
        }
       
    } 
    error_log('ordenEntrega despues del date: '.$ordenEntrega);

    //loggear('orden: '.$ordenEntrega, 'orders_templates');
    //loggear('ordenEntregaFormato: '.$ordenEntregaFormato, 'orders_templates');
   
    $rowData = [
      'cliente_codigo' => $cliente_codigo,
      'cod_pedido' => $cod_pedido,
      'fecha_pedido' => $fecha_pedido,
      'razon_social' => $cliente_razon,
      'direccion' => $direccion,
      'latitud' => $latitud,
      'longitud' => $longitud,
      'telefono' => $telefono,
      'email' => $email,
      'merchantEmailList' => $merchantEmailList,
      'habilidades' => $habilidades,
      'operacion' => $operacion,
      'agrupacion' => $agrupacion,
      'demora' => $demora,
      'timewindow' => $timewindow,
      'comentario' => $comentario,
      'orden' => $ordenEntrega,
      'peso' => floatval(str_replace(',', '.', $peso)),
      'volumen' =>floatval(str_replace(',', '.', $volumen)),
      'dinero' => floatval(str_replace(',', '.', $dinero)),
      'vehiculo' => $vehiculo
    ];


    if(!empty($ordenEntrega) && is_numeric($ordenEntrega)) {
        $rowData['orden'] = intval($ordenEntrega);
    } 


    //validaciones

    if (!$cod_pedido || $cod_pedido == '') {
        $error_flag = true;
        $logRegistry->setError($row, 'Ordenes sin codigo', $rowData);
    }

    if (strlen($cod_pedido) > 40) {
        $error_flag = true;
        $logRegistry->setError($row, 'Código de órden excede longitud permitida', $rowData);
    }

    if (!$cliente_codigo || $cliente_codigo == '') {
        $error_flag = true;
        $logRegistry->setError($row, 'Cliente sin codigo', $rowData);
    }

    if ($latitud != '' && (!is_numeric($latitud) || $latitud > 90 || $latitud < -90)) {
        $error_flag = true;
        $logRegistry->setError($row, 'Formato Erroneo Latitud', $rowData);
    }

    if ($longitud != '' && (!is_numeric($longitud) || $longitud > 180 || $longitud < -180)) {
        $error_flag = true;
        $logRegistry->setError($row, 'Formato Erroneo Longitud', $rowData);
    }

    if ($error_flag == true) {
        continue;
    }

    if (!isset($rutas[$cod_pedido])) {
        $rutas[$cod_pedido]['peso'] = 0;
        $rutas[$cod_pedido]['volumen'] = 0;
        $rutas[$cod_pedido]['dinero'] = 0;
        $rutas[$cod_pedido]['productos'] = array();
    }

    $rutas[$cod_pedido]['cliente_codigo'] = $cliente_codigo;
    $rutas[$cod_pedido]['cod_pedido'] = $cod_pedido;
    $rutas[$cod_pedido]['fecha_pedido'] = $fecha_pedido;
    $rutas[$cod_pedido]['razon_social'] = $cliente_razon;
    $rutas[$cod_pedido]['direccion'] = $direccion;
    $rutas[$cod_pedido]['latitud'] = $latitud;
    $rutas[$cod_pedido]['longitud'] = $longitud;
    $rutas[$cod_pedido]['telefono'] = $telefono;
    $rutas[$cod_pedido]['email'] = $email;
    $rutas[$cod_pedido]['merchantEmailList'] = $merchantEmailList;
    $rutas[$cod_pedido]['habilidades'] = $habilidades;
    $rutas[$cod_pedido]['operacion'] = $operacion;
    $rutas[$cod_pedido]['agrupacion'] = $agrupacion;
    $rutas[$cod_pedido]['peso'] += floatval(str_replace(',', '.', $peso));
    $rutas[$cod_pedido]['volumen'] += floatval(str_replace(',', '.', $volumen));
    $rutas[$cod_pedido]['dinero'] += floatval(str_replace(',', '.', $dinero));
    $rutas[$cod_pedido]['demora'] = $demora;
    $rutas[$cod_pedido]['orden'] = $ordenEntrega;
    $rutas[$cod_pedido]['ordenEntregaPorFechaHora'] = $ordenEntregaPorFechaHora;
    $rutas[$cod_pedido]['ordenEntregaFormato'] = $ordenEntregaFormato;
    $rutas[$cod_pedido]['timewindow'] = isset($rutas[$cod_pedido]['timewindow']) ? checkTimeWindow($rutas[$cod_pedido]['timewindow'], $timewindow) : $timewindow;
    $rutas[$cod_pedido]['comentario'] = $comentario;
    $rutas[$cod_pedido]['productos'][] = [
        'cod_producto' => $cod_producto,
        'desc_producto' => $desc_producto,
        'cantidad_producto' => $cant_producto,
        'precio' => floatval(str_replace(',', '.', $dinero)),
        'peso' => floatval(str_replace(',', '.', $peso)),
        'volumen' => floatval(str_replace(',', '.', $volumen)),
        'retornable' => $retornable,
        'promocion' => $promocion
    ];
    $rutas[$cod_pedido]['vehiculo'] = $vehiculo;
    $rutas[$cod_pedido]['ungroup']  = $ungroup;
    $rutas[$cod_pedido]['fila_archivo'] = $row;
}

$count = 0;
$count_modificado = 0;
$line = $primer_fila;
$count_no_client = 0;
$count_new_client = 0;
$count_singeo = 0;
$count_publicado = 0;
$data = [];
$id_pedidos = [];

$geoParam = UsuariosParametro::getValorByUsuarioAndParametro(
    $usuario_id,
    UsuariosParametro::IMPORTADOR_FLASH_ENABLE_GEOCODE
);
$geoParam = $geoParam == 1;

$clientsToGeo = [];
$aux_tw = [];

foreach ($rutas as $pedido => $detalle) {
    ++$line;
    $id_cliente_r = "SELECT id, razon_social, telefono_contacto, time_window, habilitado, domicilio_original, latitud, longitud FROM clientes WHERE codigo = '$detalle[cliente_codigo]' AND id_usuario = $usuario_id ORDER BY id DESC LIMIT 1";

    $c_timewindow = '';
    if (mysql_num_rows(mysql_query($id_cliente_r))) {
        $row = mysql_fetch_assoc(mysql_query($id_cliente_r));
        $id_cliente = $row['id'];
        $domicilio = $row['domicilio_original'];
        $habilitado = $row['habilitado'];
        $cliente_razon_social = $row['razon_social'];
        $cliente_telefono_contacto = $row['telefono_contacto'];
        $_latitud = $row['latitud'];
        $_longitud = $row['longitud'];
        $c_timewindow = $row['time_window'];

        $data_update = [];
        $data_update['habilitado']  = Cliente::HABILITADO;
        $data_update['estado']      = Cliente::OPERATIVO;

        if (filter_var($detalle['email'], FILTER_VALIDATE_EMAIL))
            $data_update['email']   = $detalle['email'];

        $cli = new Cliente();
        $cli->getById($id_cliente);
        $cli->setDataByArray($data_update);

        if ($detalle['razon_social'] && $detalle['razon_social'] != '' && $detalle['razon_social'] != $cliente_razon_social) {
            $cli->setDataByArray([
                'razon_social' => $detalle['razon_social']
            ]);
        }

        if ($detalle['telefono'] && $detalle['telefono'] != '' && $detalle['telefono'] != $cliente_telefono_contacto) {
            $cli->setDataByArray([
                'telefono_contacto' => $detalle['telefono']
            ]);
        }

        if (
            // Valido se cambio la dirección
            $detalle['direccion'] &&
            $domicilio != $detalle['direccion']
        ) {
            if (floatval($detalle['latitud']) && floatval($detalle['longitud'])) {
                $coords['latitude'] = floatval($detalle['latitud']);
                $coords['longitude'] = floatval($detalle['longitud']);
            } else if ($geoParam) {
                // Si tiene el parametro guardo la dirección para geocodificar al final.
                $clientsToGeo[$id_cliente] = $detalle['direccion'];
                $coords['latitude'] = 0;
                $coords['longitude'] = 0;
            } else {
                $coords['latitude'] = 0;
                $coords['longitude'] = 0;
            }
            $cli->setDataByArray([
                'latitud' => $coords['latitude'],
                'longitud' => $coords['longitude'],
                'domicilio' => $detalle['direccion'],
                'domicilio_original' => $detalle['direccion']
            ]);
        } else if (
            floatval($detalle['latitud']) != 0 &&
            floatval($detalle['longitud']) != 0 &&
            $_latitud != floatval($detalle['latitud']) &&
            $_longitud != floatval($detalle['longitud'])
        ) {

            $cli->setDataByArray([
                'latitud' => floatval($detalle['latitud']),
                'longitud' => floatval($detalle['longitud'])
            ]);
        }
        $cli->save();
    } else {

        if (!$detalle['razon_social'] || $detalle['razon_social'] == '') {
            $logRegistry->setError($detalle['fila_archivo'], 'Cliente sin nombre', $detalle);
            continue;
        }

        if (!$detalle['direccion'] || $detalle['direccion'] == '') {
            $logRegistry->setError($detalle['fila_archivo'], 'Cliente sin direccion', $detalle);
            continue;
        }

        if ($detalle['cliente_codigo'] == 'codigo' && $detalle['razon_social'] == 'razon_social' && $detalle['latitud'] == 'latitud')
            continue;

        if ($detalle['cliente_codigo'] != '' && $detalle['razon_social'] != '' && $detalle['direccion'] != '') {
            $cliente = [];
            $cliente['id_usuario'] = $usuario_id;
            $cliente['codigo'] = corregirCodigoDeCliente($detalle['cliente_codigo'], $ultimo_codigo_cliente, $mapping);
            $cliente['razon_social'] = mysql_real_escape_string($detalle['razon_social']);
            $cliente['domicilio'] = mysql_real_escape_string($detalle['direccion']);
            $cliente['domicilio_original'] = mysql_real_escape_string($detalle['direccion']);
            $cliente['update_timestamp'] = $ahora;
            $cliente['time_window'] = mysql_real_escape_string($detalle['timewindow']);
            $cliente['telefono_contacto'] = mysql_real_escape_string($detalle['telefono']);
            $cliente['email'] = mysql_real_escape_string($detalle['email']);

            if (!$detalle['latitud'] || $detalle['latitud'] == '' || $detalle['latitud'] == 0) {
                // Sino tiene el parametro para geocodificar, le setea latitud y longitud en 0.
                if (!$geoParam) {
                    $cliente['latitud'] = 0;
                    $cliente['longitud'] = 0;
                    $count_singeo++;
                }
            } else {
                if ($detalle['latitud'] && $detalle['latitud'] != '' && !empty($detalle['latitud'])) {
                    $cliente['latitud'] = mysql_real_escape_string($detalle['latitud']);
                }

                if ($detalle['longitud'] && $detalle['longitud'] != '' && !empty($detalle['longitud'])) {
                    $cliente['longitud'] = mysql_real_escape_string($detalle['longitud']);
                }
            }

            $newPoi = new Cliente();
            $newPoi->setDataByArray($cliente);
            $newPoi->create();

            $id_cliente = $newPoi->getId();

            // Si tiene el parametro guardo la dirección para geocodifcar al final
            if($geoParam) {
                if(empty($cliente['longitud']) && empty($cliente['latitud'])){
                    $clientsToGeo[$id_cliente] = $detalle['direccion'];
                }
            }

            $count_new_client++;
            loggear("Cliente nuevo $detalle[cliente_codigo] para usuario $usuario_id", basename($_SERVER['SCRIPT_NAME']));
        } else {
            $count_no_client++;
            continue;
        }
    }

    // Busco el vehículo
    $id_vehiculo = 'NULL';
    $codigo_vehiculo = trim($detalle['vehiculo']);
    if ($codigo_vehiculo != '') {
        //Busco por maquina
        $query_maquina = "SELECT id 
                            FROM maquinas 
                            WHERE (
                                id_usuario = $usuario_id OR id IN 
                                (SELECT id_maquina FROM maquinas_usuarios WHERE id_usuario = $usuario_id)
                            )
                                AND (nombre = '$codigo_vehiculo' OR codigo_erp = '$codigo_vehiculo')
                                LIMIT 1";
        $r_query_maquina = mysql_query($query_maquina);
        if(mysql_num_rows($r_query_maquina) > 0){
            $id_vehiculo = mysql_result($r_query_maquina, 0);
        }
    }

    $detalle['timewindow'] = checkPedidoClienteTimezone($c_timewindow, $detalle['timewindow']);

    $pedido = Pedido::getAllDataByUserAndCodigo($usuario_id, $detalle['cod_pedido']);

    if (isset($pedido)) {
        //Si el pedido existe verifico que sea del mismo cliente
        $id_pedido          = $pedido->getId();
        $id                 = $pedido->getId();
        $f_asignacion       = $pedido->getData('fecha_asignacion');
        $id_cli_ped         = $pedido->getData('id_cliente');

        if ($id_cli_ped == $id_cliente) {
            //si el pedido es del mismo cliente se verifica que esté asignado a una ruta

          if ($f_asignacion == NULL ) {
                $query = "UPDATE pedidos SET fecha_pedido = '$detalle[fecha_pedido]', 
                                        id_dispositivo_asignado = $id_vehiculo, 
                                        fecha_asignacion = NULL, 
                                        salida_asignacion = 0,
                                        total_con_iva = '$detalle[dinero]',
                                        time_window = '$detalle[timewindow]',
                                        id_cliente_reparto = NULL
                                    WHERE id = $id_pedido";
                mysql_query($query);
                mysql_query("DELETE FROM pedidos_cortes WHERE id_pedido = '$id_pedido'");
                mysql_query("DELETE FROM pedidos_detalles WHERE id_pedido = '$id_pedido'");
                mysql_query("DELETE FROM pedidos_remitentes WHERE id_pedido = '$id_pedido'");
                $count_modificado++;
                $logRegistry->setModificado($detalle['fila_archivo'], 'pedido', $detalle);
            } elseif($enableReprogramacionAutomatica && verificarOrdenRechazadaVencida($id_pedido)) {
                // Reprogramo la orden si tiene activada la reprogramacion automatica.
                $id = reprogramarOrden($pedido,$detalle, $usuario_id, $id_vehiculo);
                $count++;
            
            }else {
                $count_publicado++;
                $logRegistry->setError($detalle['fila_archivo'], 'Órden existente (Publicado en fecha anterior)', array_merge($detalle, ['fecha_publicacion' => $f_asignacion]));
                continue;
            }
        } else {
            //si el codigo de pedido fue usado antes con otro cliente se considera duplicado
            $count_publicado++;
            $logRegistry->setError($detalle['fila_archivo'], 'Órden Existente (Asignado a otro cliente)', array_merge($detalle, ['fecha_publicacion' => $f_asignacion]));
            continue;
        }
        $id = $id_pedido;
    } else {
        //Alta de nuevo pedido
        $pedidos = [];
        $pedidos['id_usuario']      = $usuario_id;
        $pedidos['id_cliente']      = $id_cliente;
        $pedidos['fecha_insercion'] = $ahora;
        $pedidos['operacion']       = mysql_real_escape_string($detalle['operacion']);
        $pedidos['codigo_pedido'] = corregirCodigoDePedido($detalle['cod_pedido'], $ultimo_codigo_pedido, $mapping);

        if($id_vehiculo != 0) {
                $pedidos['id_dispositivo_asignado'] = $id_vehiculo;
        }

        $pedidos['salida_asignacion'] = 0;
       
        if ($detalle['fecha_pedido'] && $detalle['fecha_pedido'] != '' && !empty($detalle['fecha_pedido'])) {
            $pedidos['fecha_pedido'] =  mysql_real_escape_string($detalle['fecha_pedido']);
        } else {
            $pedidos['fecha_pedido'] = $ahora;
        }

        if (!$detalle['ordenEntregaPorFechaHora']) {
            $pedidos['orden'] = $detalle['orden'];
        }

        if ($detalle['agrupacion'] && $detalle['agrupacion'] != '' && !empty($detalle['agrupacion'])) {
            $pedidos['agrupacion'] = $detalle['agrupacion'];
        }

        if ($detalle['dinero'] && $detalle['dinero'] != '' && !empty($detalle['dinero'])) {
            $pedidos['total_con_iva'] =  mysql_real_escape_string($detalle['dinero']);
        }

        if ($detalle['timewindow'] && $detalle['timewindow'] != '' && !empty($detalle['timewindow'])) {
            $pedidos['time_window'] =  mysql_real_escape_string($detalle['timewindow']);
        }

        $pedido = new Pedido($pedidos);
        $pedido->create();
        $id = $pedido->getId();

        // guardo todos los pedidos que le llega el orden en formato fecha para reordenarlos una vez insertados
        if ($detalle['ordenEntregaPorFechaHora']) {
            $pedidosConFechas[$pedidos['fecha_pedido']][$id] = [
                'fecha' => $detalle['orden']
            ];
        }
       
        $count++;
    }

    if ($mapping->ungroup) {
        //Chequeo de ungroup automático
        if($detalle['ungroup'] == '0'){
            $tw_result = '';
            if( (isset($aux_tw[$id_cliente]) && $aux_tw[$id_cliente] != 'FFFFFFFFFFFF') && $detalle['timewindow'] != 'FFFFFFFFFFFF'){
                $tw_result = compareTW($aux_tw[$id_cliente], $detalle['timewindow']);
                if ( $tw_result == 'FFFFFFFFFFFF'){
                    $detalle['ungroup'] = '1';
                };  
            }
            $aux_tw[$id_cliente] = $detalle['timewindow'];
        }
    }

    $peso       = mysql_real_escape_string($detalle['peso']);
    $volumen    = mysql_real_escape_string($detalle['volumen']);
    $dinero     = mysql_real_escape_string($detalle['dinero']);

    if (($peso && $peso == '') || empty($peso) || !is_numeric($peso)) {
        $peso = 0;
    }
    if (($volumen && $volumen == '') || empty($volumen) || !is_numeric($volumen)) {
        $volumen = 0;
    }
    if (($dinero && $dinero == '') || empty($dinero) || !is_numeric($dinero)) {
        $dinero = 0;
    }

    // QS-6913 - No guardar pedidos cortes cuando el valor sea 0
    // QS-7109 Se revierte el cambio del 6913 porque no funciona la optimización, se deja el código ya que a futuro lo van a utilizar.
    //if ((bool) $peso) {
      mysql_query("INSERT INTO pedidos_cortes (id_pedido, id_corte, valor) values ($id, 2, $peso)");
    //}
    //if ((bool) $volumen) {
      mysql_query("INSERT INTO pedidos_cortes (id_pedido, id_corte, valor) values ($id, 3, $volumen)");
    //}
    //if ((bool) $dinero) {
      mysql_query("INSERT INTO pedidos_cortes (id_pedido, id_corte, valor) values ($id, 7, $dinero)");
    //}

    // Assigns merchants to order
    $merchantsCounter = 0; // Max merchant quantity to insert
    if (!empty(array_filter($detalle['merchantEmailList']))) {
        foreach ($detalle['merchantEmailList'] as $merchantEmail) {
            if ($merchantsCounter >= 3) break; // Max merchants to treat reached

            $merchantId = Merchant::checkIfEmailExists($merchantEmail, $existingMerchantsEmails);

            // Exists and is a new order
            $merchantExists = $merchantId !== -1;
            if ($merchantExists) {
                Merchant::assignToOrder($id, $merchantId);
            } else {
                $isEmailValid = filter_var($merchantEmail, FILTER_VALIDATE_EMAIL);

                if ($isEmailValid) {
                    // Creates Merchant
                    $merhcantData = array(
                        'id_usuario' => $usuario_id,
                        'nombre' => '', // Change this line in future once merchant has new props
                        'email' => $merchantEmail
                    );
                    $createdMerchant = Merchant::createFullMerchant($merhcantData);

                    if ($createdMerchant) {
                        Merchant::assignToOrder($id, $createdMerchant->getId());
                        array_push($existingMerchantsEmails, array($createdMerchant->getId() => $merchantEmail));
                    }
                }
            }
            ++$merchantsCounter;
        }
    }

    if(!empty($detalle['habilidades'])){
        $habilidad      = json_encode(array($detalle['habilidades']));
        $id_param_hab   = PedidosParametros::CreateOrUpdate($id, 'HABILIDADES', $habilidad);
    }

    if($param_barcode_pedido){
        $barcode    = isset($pedidos['codigo_pedido']) && $pedidos['codigo_pedido'] ? $pedidos['codigo_pedido'] : mysql_real_escape_string($detalle['cod_pedido']);
        $id_paramWO = PedidosParametros::CreateOrUpdate($id, 'workOrder', $barcode);
    }

    PedidosParametros::CreateOrUpdate($id, 'ORIGEN', 'OT'.'-'.$template['data']['nombre'].'-'.$template['data']['defecto'].'-'.$template['data']['id']);

    if (($detalle['productos'])) {
        foreach ($detalle['productos'] as $items) {
            //valido si el producto ya existe
            $item_codigo_producto = mysql_real_escape_string($items['cod_producto']);
            if (!$item_codigo_producto || $item_codigo_producto == '') {
                continue;
            }

            $producto = Producto::getByUserAndCodigo($usuario_id, $item_codigo_producto);
           
            $item_desc_producto = mysql_real_escape_string($items['desc_producto']);
            if ($producto === null) {
                $producto = new Producto([
                    'codigo'      => $item_codigo_producto,
                    'descripcion' => $item_desc_producto,
                    'id_usuario'  => $usuario_id,
                    'retornable'  => $items['retornable']
                ]);
                $producto->create();
            } else {

                if ($producto->updateIfHasDiff([
                    'descripcion' => $item_desc_producto,
                    'retornable'  => $items['retornable']

                ])) {
                    $producto->save();
                }
            }

            $id_producto = $producto->getId();
        
            //inserto las lineas del pedido
            $pedido_detalle = [];

            $pedido_detalle['id_pedido']        = $id;
            $pedido_detalle['codigo_producto']  = $item_codigo_producto;
            $pedido_detalle['precio']           = (is_numeric(mysql_real_escape_string($items['precio'])) ? mysql_real_escape_string($items['precio']) : 0);
            $pedido_detalle['cantidad']         = (is_numeric(mysql_real_escape_string($items['cantidad_producto'])) ? intval(mysql_real_escape_string($items['cantidad_producto'])) : 0);
            $pedido_detalle['id_producto']      = $id_producto;

            if (is_numeric(mysql_real_escape_string($items['peso'])))
                $pedido_detalle['peso']     = mysql_real_escape_string($items['peso']);

            if (is_numeric(mysql_real_escape_string($items['volumen'])))
                $pedido_detalle['volumen']  = mysql_real_escape_string($items['volumen']);

            // QS-6913 - No guardar pedidos cortes cuando el valor sea 0
            // QS-7109 Se revierte el cambio del 6913 porque no funciona la optimización, se deja el código ya que a futuro lo van a utilizar.
            /*
            foreach ($pedido_detalle as $key => $value) {
              if (in_array($key, ['precio', 'volumen', 'peso']) && !$value) {
                unset($pedido_detalle[$key]);
              }
            }*/

            $key_ped_det    = array_keys($pedido_detalle);
            $value_ped_det  = array_values($pedido_detalle);

            $query = "INSERT INTO pedidos_detalles ( " . implode(',', $key_ped_det) . ") VALUES('" . implode("','", $value_ped_det) . "')";

            mysql_query($query);
            $id_pedido_detalle = mysql_insert_id();

            if ($items['promocion'] !='' && !empty($items['promocion'])) {

                $r_campo_cliente = mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = $usuario_id and nombre = 'promocion'");
                if (!mysql_num_rows($r_campo_cliente)) {
                    $query = "INSERT INTO clientes_campos (id_usuario, nombre, tipo) VALUES ($usuario_id,'promocion','TEXT')";
                    mysql_query($query);
                    $id_campo = mysql_insert_id();
                } else {
                    $id_campo = mysql_result($r_campo_cliente, 0, 0);
                }
                guardarcampospedidosdetalles($id_campo, $id_pedido_detalle, $items['promocion']);
        }

        }
    }

    if ($detalle['demora'] && $detalle['demora'] != '' && !empty($detalle['demora'])) {
        $campodemora = clientesCampos($usuario_id, 'demora');
        $demora_cliente = (is_numeric($detalle['demora']) ?
            intval($detalle['demora']) :
            5);
        guardarcampos($campodemora, $id_cliente, $demora_cliente);
    }

    $existecomentario = checkcampos($usuario_id, 'Comentario', $detalle['comentario'], $id_cliente);

    // 1 - Comentario existe y es diferente al nuevo
    // 0 - Campo comentario no existe
    // 2 - Comentario guardado es igual al nuevo

    if ($existecomentario == 1 || $existecomentario == 0) {
        $campocomentario = clientesCampos($usuario_id, 'Comentario');
        guardarcampos($campocomentario, $id_cliente, $detalle['comentario']);

        //Verifico la configuracion de control de entregas
        $r_configuracion_campo = mysql_query("SELECT id FROM ce_configuracion WHERE tipo = 'campo' AND id_usuario = $usuario_id AND id_campo = $campocomentario");
        if (!mysql_num_rows($r_configuracion_campo)) {
            $query = mysql_query("INSERT INTO ce_configuracion (id_usuario, tipo, id_campo, descripcion, ubicacion) VALUES ($usuario_id, 'campo', $campocomentario, 'Comentario', 'listacliente')");
        }
    }

    PedidosParametros::CreateOrUpdate($id, 'UNGROUP', $detalle['ungroup']);

    $id_pedidos[] = $id;

    unset($id, $id_pedido);
}
if(!empty($pedidosConFechas)){
    sortOrdersByDate($pedidosConFechas, $usuario_id);
}

if(!empty($clientsToGeo)){

    $clientsCoords =   geocodeCustomerArray($clientsToGeo, $usuario_id);
    foreach($clientsCoords as $idClient => $coords){

        if($coords['latitude'] != 0 && $coords['longitude'] != 0){
           $lat = substr($coords['latitude'], 0,10);
           $long = substr($coords['longitude'], 0,10);

        } else {
            $lat = 0;
            $long = 0;
            $count_singeo++;
        }
        mysql_query("UPDATE clientes SET latitud = $lat , longitud = $long WHERE id = $idClient");

    }
}

// After process, unsubcribe POI domain events listener
DomainEventPublisher::instance()->unsubscribe($poiListenerId);

if ($tnt) {
    bindQTN($id_pedidos);
}

if ($mapping->clienteCodigoAutogenerar) {
    mysql_query("INSERT INTO usuarios_parametros (id_usuario, codigo_parametro, valor) VALUES ($usuario_id, 'ULTIMO_CODIGO_CLIENTE', $ultimo_codigo_cliente)
                ON DUPLICATE KEY UPDATE valor = $ultimo_codigo_cliente");
}
if ($mapping->codPedidoAutogenerar) {
    mysql_query("INSERT INTO usuarios_parametros (id_usuario, codigo_parametro, valor) VALUES ($usuario_id, 'ULTIMO_CODIGO_PEDIDO', $ultimo_codigo_pedido)
                ON DUPLICATE KEY UPDATE valor = $ultimo_codigo_pedido");
}

if ($mapping->clienteCodigoAutogenerar || $mapping->codPedidoAutogenerar) {
    removeUserCache($usuario_id);
}

$data[0] = $count;
$data[1]['pedidos']['cantidad'] = $count;
$data[1]['sinCliente']['cantidad'] = $count_no_client;
$data[1]['clientes_creados']['cantidad'] = $count_new_client;
$data[1]['sinGeo']['cantidad'] = $count_singeo;
$data[1]['duplicados']['cantidad'] = $count_publicado;
$data[1]['modificados']['cantidad'] = $count_modificado;
$data[2] = $logRegistry->getReport();
$data[3] = $log_interfaces_data;

if (!empty($resultArray = $logRegistry->getResultArray())) {
    $data[1]['logFile']['link'] = $logRegistry->writeCsvLogFile($usuario_id);
    $data[4]['array'] = $resultArray;
    $data[4]['usuario'] = $usuario_id;
}

echo json_encode($data);

function corregirCodigoDePedido($codigo, &$ultimo_codigo_pedido, $mapping) {
    if($mapping->codPedidoAutogenerar) {
      $codigo = mysql_real_escape_string($ultimo_codigo_pedido);
      $ultimo_codigo_pedido++;
    }
  
    return mysql_real_escape_string($codigo);
}

function corregirCodigoDeCliente($codigo, &$ultimo_codigo_cliente, $mapping) {
    if($mapping->clienteCodigoAutogenerar) {
      $codigo = mysql_real_escape_string($ultimo_codigo_cliente);
      $ultimo_codigo_cliente++;
    }
  
    return mysql_real_escape_string($codigo);
}

/**
 * Parses a string date using a custom date format string.
 * 
 * @param string $dateString The input date string to parse.
 * @param string $formatString The custom date format string to use.
 *   The format string can use the following format characters:
 *   - DD: day of the month (01 to 31)
 *   - MM: month of the year (01 to 12)
 *   - AA: last two digits of the year (00 to 99)
 *   - AAAA: four digits of the year (e.g. 2021)
 *   The format string can also use either '-' or '/' as date separators.
 * @return string Returns a string representation of the parsed date in the 'Y-m-d' format.
 * @throws InvalidArgumentException if the input date string or format string is invalid.
 */
function createDateFromFormat($dateString, $formatString) {
    $formatMap = array(
        'DD' => 'd',
        'MM' => 'm',
        'AA' => 'y',
        'AAAA' => 'Y'
    );

    $formatPattern = strtr($formatString, $formatMap);
    $dateObj = DateTime::createFromFormat($formatPattern, $dateString);
    if (!$dateObj) {
        throw new InvalidArgumentException('Invalid date or format string');
    }
    return $dateObj->format('Y-m-d');
}

function getNewOrderCode($codigoPedido, $id_usuario) {
    $query = "SELECT codigo_pedido 
              FROM pedidos 
              WHERE id_usuario = $id_usuario AND codigo_pedido LIKE '$codigoPedido%' 
              ORDER BY LENGTH(codigo_pedido) DESC, codigo_pedido DESC 
              LIMIT 1";
    $resultado = mysql_query($query);

    if ($fila = mysql_fetch_assoc($resultado)) {
        $ultimoCodigo = $fila['codigo_pedido'];

        if (preg_match('/\|R(\d+)$/', $ultimoCodigo, $matches)) {
            $nuevoReintento = (int)$matches[1] + 1;
            $nuevoCodigo = preg_replace('/\|R\d+$/', "|R$nuevoReintento", $ultimoCodigo);
        } else {
            $nuevoCodigo = $ultimoCodigo . "|R1";
        }
    } else {
        $nuevoCodigo = $codigoPedido;
    }

    return $nuevoCodigo;
} 

function formatearFecha($fechaInput, $formatoPersonalizado, $year = null) {
    $mapeoFormatos = [
        'DD-MM HH:mm' => 'd-m H:i',
        'DD-MM-YYYY HH:mm' => 'd-m-Y H:i',
        'YYYY-MM-DD HH:mm:SS' => 'Y-m-d H:i:s',
        'MM/DD/YYYY hh:mm a' => 'm/d/Y h:i a',
        'DD MMM YYYY, HH:mm' => 'd M Y, H:i',
        'DD/MM/YYYY – HH:mm:ss' => 'd/m/Y - H:i:s',
        'ddd DD/MM/YYYY HH:mm' => 'D d/m/Y H:i',
        'YYYY-MM-DDTHH:mm:ssZ' => 'Y-m-d\TH:i:s\Z',
    ];

    if (!isset($mapeoFormatos[$formatoPersonalizado])) {
        throw new Exception("Formato personalizado no soportado: '$formatoPersonalizado'.");
    }

    $formatoPhp = $mapeoFormatos[$formatoPersonalizado];
    $dateTime = DateTime::createFromFormat($formatoPhp, $fechaInput);

    if ($dateTime === false) {
        throw new Exception("La fecha '$fechaInput' no coincide con el formato '$formatoPersonalizado'.");
    }

    if (!preg_match('/[yY]/', $formatoPhp)) {
        $year = $year ?? date('Y');
        $dateTime->setDate($year, $dateTime->format('m'), $dateTime->format('d'));
    }

    return $dateTime->format('Y-m-d H:i:s');
}

function verificarOrdenRechazadaVencida($id_pedido){
    $query = "select p.id from pedidos p
        LEFT JOIN ce_estados ce ON ce.id = p.id_ce_estado
        LEFT JOIN ce_subestados ces ON ces.id = p.id_ce_subestado
        LEFT JOIN clientes_repartos cr on cr.id = p.id_cliente_reparto
        lEFT JOIN repartos r ON r.id = cr.id_reparto
        WHERE p.id = $id_pedido
        AND ((ce.afirmativo = 0 OR ce.afirmativo = 3) OR (r.estado in ('VENCIDO','TERMINADO') and ce.afirmativo is null));";
    $result = mysql_query($query);
    
    return (mysql_num_rows($result) > 0 ? true : false);
}

function reprogramarOrden($pedido,$detalle, $id_usuario, $id_vehiculo = null) {
    $ahora = date("Y-m-d H:i:s");

    $codigo_pedido_anterior = $pedido->getData('codigo_pedido');
    $id_pedido = $pedido->getId();
    $nuevo_codigo_pedido    = getNewOrderCode($codigo_pedido_anterior, $id_usuario);
    if ($detalle['fecha_pedido'] && $detalle['fecha_pedido'] != '' && !empty($detalle['fecha_pedido'])) {
        $fecha_pedido_reprogramado =  mysql_real_escape_string($detalle['fecha_pedido']);
    } else {
        $fecha_pedido_reprogramado = $ahora;
    }
    //Pedidos
    $data_pedido = [
        'id_cliente'        => $pedido->getData('id_cliente'),
        'id_usuario'        => $pedido->getData('id_usuario'),
        'fecha_insercion'   => $ahora,
        'salida_asignacion' => null,
        'fecha_pedido'      => $fecha_pedido_reprogramado,
        'codigo_pedido'     => $nuevo_codigo_pedido,
        'agrupacion'        => $pedido->getData('agrupacion'),
        'priority'          => 0,
        'time_window'          => $pedido->getData('time_window'),
        'total_con_iva'     => $pedido->getData('total_con_iva'),
        'operacion'         => $pedido->getData('operacion'),
        'id_dispositivo_asignado' => $id_vehiculo
    ];
    loggear("Reprogramando pedido $codigo_pedido_anterior a $nuevo_codigo_pedido " . json_encode($data_pedido), 'importador_flash');
    $nuevoPedido = new Pedido($data_pedido);
    $nuevoPedido->create();
    $id_nuevo_pedido = $nuevoPedido->getId();

    $query_pedido_remitente = mysql_query("SELECT id_remitente FROM pedidos_remitentes where id_pedido = $id_pedido");
    $query_pedido_remitente_r = mysql_num_rows($query_pedido_remitente);
    if($query_pedido_remitente_r > 0){
        while($result_pedidos_remitentes = mysql_fetch_assoc($query_pedido_remitente)){
            $id_remitente = $result_pedidos_remitentes['id_remitente'];
            
            mysql_query("INSERT INTO pedidos_remitentes (id_remitente,id_pedido) VALUES ($id_remitente,$id_nuevo_pedido)");
        }
    }

    //Pedidos Cortes
    $r_pedido_cortes = PedidosCorte::getCortesByPedido($id_pedido, true);
    $data_cortes = [];
    foreach ($r_pedido_cortes as $corte){
        $pedidoCorte = New PedidosCorte([
            'id_pedido' => $nuevoPedido->getId(),
            'id_corte'  => $corte['id_corte'],
            'valor'     => $corte['valor']
        ]);
        $pedidoCorte->create();
    }

    //Pedidos Detalles
    $r_pedido_detalle = PedidosDetalle::getAllByPedido($id_pedido);
    foreach ($r_pedido_detalle as $pedidoDetalles){
        $pedidoDetalle = new PedidosDetalle([
            'id_pedido'             => $nuevoPedido->getId(),
            'id_producto'           => $pedidoDetalles->getData('id_producto'),
            'codigo_producto'       => $pedidoDetalles->getData('codigo_producto'),
            'cantidad'              => $pedidoDetalles->getData('cantidad'),
            'cantidad_entregada'    => $pedidoDetalles->getData('cantidad_entregada'),
            'precio'                => $pedidoDetalles->getData('precio'),
            'peso'                  => $pedidoDetalles->getData('peso'),
            'volumen'               => $pedidoDetalles->getData('volumen'),
            'id_ce_subestado'       => $pedidoDetalles->getData('id_ce_subestado'),
        ]);
        $pedidoDetalle->create();
    }

    //Pedidos Parametros
    $r_pedido_parametros = PedidosParametros::getDataByIdPedido($id_pedido);
    foreach ($r_pedido_parametros as $pedidoParametros){
        $pedidoParametro = new PedidosParametros([
            'id_pedido' => $nuevoPedido->getId(),
            'parametro' => $pedidoParametros['parametro'],
            'valor'     => $pedidoParametros['valor']
        ]);
        $pedidoParametro->create();
    }


    // taggeo como reprogramado el pedido original
    $pedidoParametro = new PedidosParametros([
            'id_pedido' => $id_pedido,
            'parametro' => "reprogramado",
            'valor'     => 1
    ]);
    $pedidoParametro->create();


    return $id_nuevo_pedido;
}



function formatOrdenDate($dateString) {
    // Primero intentamos con strtotime
    $timestamp = strtotime($dateString);
    
    if ($timestamp === false) {
        loggear("Error al convertir la fecha: '$dateString'", "orders_template_error");
        throw new InvalidArgumentException("No se pudo interpretar la fecha: '$dateString'");
    }
    
    // Formateamos a Y-m-d H:i:s
    return date('Y-m-d', $timestamp);
}

function sortOrdersByDate($pedidosConFechas, $id_usuario) {
    error_log('sort de pedidos! '  . json_encode($pedidosConFechas));
    // Procesar cada grupo de fechas
    foreach ($pedidosConFechas as $fechaGrupo => $pedidos) {
        // Convertir fechas a timestamp para ordenar
        $pedidosParaOrdenar = [];
        
        foreach ($pedidos as $id_pedido => $detalle) {
            $fechaOriginal = $detalle['fecha'];
            
            try {
                // Asumimos formato 'Y-m-d H:i:s'
                $dateTime = DateTime::createFromFormat('Y-m-d H:i:s', $fechaOriginal);
                
                if ($dateTime === false) {
                    loggear("Error al convertir la fecha: '$fechaOriginal'", "orders_template_error");

                    throw new Exception("Formato de fecha no válido: $fechaOriginal");
                }
                
                $pedidosParaOrdenar[$id_pedido] = $dateTime->getTimestamp();
            } catch (Exception $e) {
                // En caso de error, usar timestamp 0 (se ordenarán al principio)
                $pedidosParaOrdenar[$id_pedido] = 0;
                loggear("Error al procesar fecha: " . $e->getMessage(), "orders_template_error");
            }
        }

        // Ordenar los pedidos por timestamp
        asort($pedidosParaOrdenar);

        // Asignar números de orden secuenciales
        $orden = 1;
        foreach ($pedidosParaOrdenar as $id_pedido => $timestamp) {
            // Usando consulta preparada para mayor seguridad (recomendado)
            $query = "UPDATE pedidos SET orden = $orden WHERE id = $id_pedido AND id_usuario = $id_usuario";
            mysql_query($query);
            $orden++;
        }
    }
}