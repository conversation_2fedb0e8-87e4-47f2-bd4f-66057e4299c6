<?php

function clientesCampos($usuario_id, $nombre_campo_adicional) {
    $r_campo_cliente = mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = $usuario_id and nombre = '$nombre_campo_adicional'");
    if (!mysql_num_rows($r_campo_cliente)) {
        $query = "INSERT INTO clientes_campos (id_usuario, nombre, tipo) VALUES ($usuario_id,'$nombre_campo_adicional','TEXT')";
        mysql_query($query);
        $id_campo = mysql_insert_id();
    } else {
        $id_campo = mysql_result($r_campo_cliente, 0, 0);
    }
    return $id_campo;
}

function checkcampos($usuario_id, $nombre_campo_adicional, $valor, $id_cliente) {
    $r_campo_cliente = mysql_result(mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = $usuario_id and nombre = '$nombre_campo_adicional'"), 0, 0);

    if ($r_campo_cliente) {
        $camposaved = mysql_result(mysql_query("SELECT valor
                                                  FROM clientes_campos_detalles 
                                                  WHERE id_campo = $r_campo_cliente
                                                    AND id_cliente = $id_cliente
                                                  LIMIT 1"), 0, 0);
        if ($valor != $camposaved) {
            return 1;
        } elseif ($valor == $camposaved) {
            return 2;
        }
    }
    return 0;
}

function guardarcampos($id_campo, $id_cliente, $valor_campo) {
    $camposaved = mysql_result(mysql_query("SELECT id
                                                  FROM clientes_campos_detalles 
                                                  WHERE id_campo = $id_campo
                                                    AND id_cliente = $id_cliente
                                                  LIMIT 1"), 0, 0);
    if ($camposaved) {
        mysql_query("UPDATE clientes_campos_detalles SET valor = '$valor_campo' WHERE id_cliente = $id_cliente AND id_campo = '$id_campo' AND id = $camposaved ");
    } else {
        mysql_query("INSERT INTO clientes_campos_detalles (id_cliente, id_campo, valor) VALUES ($id_cliente,$id_campo, '$valor_campo') ");
    }
}

function guardarcampospedidos($id_campo, $id_pedido, $valor_campo) {
    $camposaved = mysql_result(mysql_query("SELECT id
                                                  FROM pedidos_campos_detalles 
                                                  WHERE id_cliente_campo = $id_campo
                                                    AND id_pedido = $id_pedido
                                                  LIMIT 1"), 0, 0);
    if ($camposaved) {
        mysql_query("UPDATE pedidos_campos_detalles SET valor = '$valor_campo' WHERE id_pedido = $id_pedido AND id_cliente_campo = '$id_campo' AND id = $camposaved ");
    } else {
        mysql_query("INSERT INTO pedidos_campos_detalles (id_pedido, id_cliente_campo, valor) VALUES ($id_pedido,$id_campo, '$valor_campo') ");
    }
}

function guardarcampospedidosdetalles($id_campo, $id_pedido_detalle, $valor_campo) {
    $camposaved = mysql_result(mysql_query("SELECT id
                                                  FROM pedidos_detalles_atributos
                                                  WHERE id_cliente_campo = $id_campo
                                                    AND id_pedido = $id_pedido_detalle
                                                  LIMIT 1"), 0, 0);
    if ($camposaved) {
        mysql_query("UPDATE pedidos_detalles_atributos SET valor = '$valor_campo' WHERE id_pedido_detalle = $id_pedido_detalle AND id_cliente_campo = '$id_campo' AND id = $camposaved ");
    } else {
        mysql_query("INSERT INTO pedidos_detalles_atributos (id_pedido_detalle, id_cliente_campo, valor) VALUES ($id_pedido_detalle,$id_campo, '$valor_campo') ");
    }
}

function writeCsvLogFile($resultData, $usuario_id) {

    $resultData = array_reduce($resultData, function ($formattedArray, $row){
        $formattedArray[] = array($row['codigo_pedido'], $row['codigo_cliente'], $row['estado']);
        return $formattedArray;
    });

    $filename = "/upload_ruteador/" . 'log_PedidosPlanificador_' . ID_USUARIO . '_' . (new DateTime())->format('Y-m-d_H-i-s') . ".csv";
    $path = "/var/www/saas" . $filename;

    $fp = fopen($path, 'w');

    fputcsv($fp, array('codigo_pedido', 'codigo_cliente', 'estado'));
    foreach ($resultData as $row) {
        fputcsv($fp, $row);
    }
    fclose($fp);
    loggear($path, $usuario_id . "_CLIENTES");
    return $filename;
}

function obtener_ventana_cliente_AMPM($horas) {
    $bitString = '';

    $timeWindowam = explode('-', $horas['am']);
    $timeWindowpm = explode('-', $horas['pm']);

    $timeWindowStartam = explode(':', trim($timeWindowam[0]));
    $timeWindowEndam = explode(':', trim($timeWindowam[1]));
    $timeWindowStartpm = explode(':', trim($timeWindowpm[0]));
    $timeWindowEndpm = explode(':', trim($timeWindowpm[1]));

    $hourStartam = $timeWindowStartam[0] * 2;
    $hourEndam = $timeWindowEndam[0] * 2 - 1;
    $hourStartpm = $timeWindowStartpm[0] * 2;
    $hourEndpm = $timeWindowEndpm[0] * 2 - 1;


    if ($timeWindowStartam[1] >= 30) {
        $hourStartam = $hourStartam + 1;
    }
    if ($timeWindowEndam[1] >= 30) {
        $hourEndam = $hourEndam + 1;
    }
    if ($timeWindowStartpm[1] >= 30) {
        $hourStartpm = $hourStartpm + 1;
    }
    if ($timeWindowEndpm[1] >= 30) {
        $hourEndpm = $hourEndpm + 1;
    }

    for ($i = 0; $i < 48; $i++) {
        if ((($i >= $hourStartam) && ($i <= $hourEndpm)) && (($i <= $hourEndam) || ($i >= $hourStartpm))) {
            $bitString .= "1";
        } else {
            $bitString .= "0";
        }
    }

    $timeWindow = base_convert($bitString, 2, 16);
    $timeWindow = str_pad(strtoupper($timeWindow), 12, '0', STR_PAD_LEFT);

    return array('time_window' => $timeWindow);
}

function get_country_string($isoCode){
    $countries = array
    (
        'AF' => 'Afghanistan',
        'AX' => 'Aland Islands',
        'AL' => 'Albania',
        'DZ' => 'Algeria',
        'AS' => 'American Samoa',
        'AD' => 'Andorra',
        'AO' => 'Angola',
        'AI' => 'Anguilla',
        'AQ' => 'Antarctica',
        'AG' => 'Antigua And Barbuda',
        'AR' => 'Argentina',
        'AM' => 'Armenia',
        'AW' => 'Aruba',
        'AU' => 'Australia',
        'AT' => 'Austria',
        'AZ' => 'Azerbaijan',
        'BS' => 'Bahamas',
        'BH' => 'Bahrain',
        'BD' => 'Bangladesh',
        'BB' => 'Barbados',
        'BY' => 'Belarus',
        'BE' => 'Belgium',
        'BZ' => 'Belize',
        'BJ' => 'Benin',
        'BM' => 'Bermuda',
        'BT' => 'Bhutan',
        'BO' => 'Bolivia',
        'BA' => 'Bosnia And Herzegovina',
        'BW' => 'Botswana',
        'BV' => 'Bouvet Island',
        'BR' => 'Brazil',
        'IO' => 'British Indian Ocean Territory',
        'BN' => 'Brunei Darussalam',
        'BG' => 'Bulgaria',
        'BF' => 'Burkina Faso',
        'BI' => 'Burundi',
        'KH' => 'Cambodia',
        'CM' => 'Cameroon',
        'CA' => 'Canada',
        'CV' => 'Cape Verde',
        'KY' => 'Cayman Islands',
        'CF' => 'Central African Republic',
        'TD' => 'Chad',
        'CL' => 'Chile',
        'CN' => 'China',
        'CX' => 'Christmas Island',
        'CC' => 'Cocos (Keeling) Islands',
        'CO' => 'Colombia',
        'KM' => 'Comoros',
        'CG' => 'Congo',
        'CD' => 'Congo, Democratic Republic',
        'CK' => 'Cook Islands',
        'CR' => 'Costa Rica',
        'CI' => 'Cote D\'Ivoire',
        'HR' => 'Croatia',
        'CU' => 'Cuba',
        'CY' => 'Cyprus',
        'CZ' => 'Czech Republic',
        'DK' => 'Denmark',
        'DJ' => 'Djibouti',
        'DM' => 'Dominica',
        'DO' => 'Dominican Republic',
        'EC' => 'Ecuador',
        'EG' => 'Egypt',
        'SV' => 'El Salvador',
        'GQ' => 'Equatorial Guinea',
        'ER' => 'Eritrea',
        'EE' => 'Estonia',
        'ET' => 'Ethiopia',
        'FK' => 'Falkland Islands (Malvinas)',
        'FO' => 'Faroe Islands',
        'FJ' => 'Fiji',
        'FI' => 'Finland',
        'FR' => 'France',
        'GF' => 'French Guiana',
        'PF' => 'French Polynesia',
        'TF' => 'French Southern Territories',
        'GA' => 'Gabon',
        'GM' => 'Gambia',
        'GE' => 'Georgia',
        'DE' => 'Germany',
        'GH' => 'Ghana',
        'GI' => 'Gibraltar',
        'GR' => 'Greece',
        'GL' => 'Greenland',
        'GD' => 'Grenada',
        'GP' => 'Guadeloupe',
        'GU' => 'Guam',
        'GT' => 'Guatemala',
        'GG' => 'Guernsey',
        'GN' => 'Guinea',
        'GW' => 'Guinea-Bissau',
        'GY' => 'Guyana',
        'HT' => 'Haiti',
        'HM' => 'Heard Island & Mcdonald Islands',
        'VA' => 'Holy See (Vatican City State)',
        'HN' => 'Honduras',
        'HK' => 'Hong Kong',
        'HU' => 'Hungary',
        'IS' => 'Iceland',
        'IN' => 'India',
        'ID' => 'Indonesia',
        'IR' => 'Iran, Islamic Republic Of',
        'IQ' => 'Iraq',
        'IE' => 'Ireland',
        'IM' => 'Isle Of Man',
        'IL' => 'Israel',
        'IT' => 'Italy',
        'JM' => 'Jamaica',
        'JP' => 'Japan',
        'JE' => 'Jersey',
        'JO' => 'Jordan',
        'KZ' => 'Kazakhstan',
        'KE' => 'Kenya',
        'KI' => 'Kiribati',
        'KR' => 'Korea',
        'KW' => 'Kuwait',
        'KG' => 'Kyrgyzstan',
        'LA' => 'Lao People\'s Democratic Republic',
        'LV' => 'Latvia',
        'LB' => 'Lebanon',
        'LS' => 'Lesotho',
        'LR' => 'Liberia',
        'LY' => 'Libyan Arab Jamahiriya',
        'LI' => 'Liechtenstein',
        'LT' => 'Lithuania',
        'LU' => 'Luxembourg',
        'MO' => 'Macao',
        'MK' => 'Macedonia',
        'MG' => 'Madagascar',
        'MW' => 'Malawi',
        'MY' => 'Malaysia',
        'MV' => 'Maldives',
        'ML' => 'Mali',
        'MT' => 'Malta',
        'MH' => 'Marshall Islands',
        'MQ' => 'Martinique',
        'MR' => 'Mauritania',
        'MU' => 'Mauritius',
        'YT' => 'Mayotte',
        'MX' => 'Mexico',
        'FM' => 'Micronesia, Federated States Of',
        'MD' => 'Moldova',
        'MC' => 'Monaco',
        'MN' => 'Mongolia',
        'ME' => 'Montenegro',
        'MS' => 'Montserrat',
        'MA' => 'Morocco',
        'MZ' => 'Mozambique',
        'MM' => 'Myanmar',
        'NA' => 'Namibia',
        'NR' => 'Nauru',
        'NP' => 'Nepal',
        'NL' => 'Netherlands',
        'AN' => 'Netherlands Antilles',
        'NC' => 'New Caledonia',
        'NZ' => 'New Zealand',
        'NI' => 'Nicaragua',
        'NE' => 'Niger',
        'NG' => 'Nigeria',
        'NU' => 'Niue',
        'NF' => 'Norfolk Island',
        'MP' => 'Northern Mariana Islands',
        'NO' => 'Norway',
        'OM' => 'Oman',
        'PK' => 'Pakistan',
        'PW' => 'Palau',
        'PS' => 'Palestinian Territory, Occupied',
        'PA' => 'Panama',
        'PG' => 'Papua New Guinea',
        'PY' => 'Paraguay',
        'PE' => 'Peru',
        'PH' => 'Philippines',
        'PN' => 'Pitcairn',
        'PL' => 'Poland',
        'PT' => 'Portugal',
        'PR' => 'Puerto Rico',
        'QA' => 'Qatar',
        'RE' => 'Reunion',
        'RO' => 'Romania',
        'RU' => 'Russian Federation',
        'RW' => 'Rwanda',
        'BL' => 'Saint Barthelemy',
        'SH' => 'Saint Helena',
        'KN' => 'Saint Kitts And Nevis',
        'LC' => 'Saint Lucia',
        'MF' => 'Saint Martin',
        'PM' => 'Saint Pierre And Miquelon',
        'VC' => 'Saint Vincent And Grenadines',
        'WS' => 'Samoa',
        'SM' => 'San Marino',
        'ST' => 'Sao Tome And Principe',
        'SA' => 'Saudi Arabia',
        'SN' => 'Senegal',
        'RS' => 'Serbia',
        'SC' => 'Seychelles',
        'SL' => 'Sierra Leone',
        'SG' => 'Singapore',
        'SK' => 'Slovakia',
        'SI' => 'Slovenia',
        'SB' => 'Solomon Islands',
        'SO' => 'Somalia',
        'ZA' => 'South Africa',
        'GS' => 'South Georgia And Sandwich Isl.',
        'ES' => 'Spain',
        'LK' => 'Sri Lanka',
        'SD' => 'Sudan',
        'SR' => 'Suriname',
        'SJ' => 'Svalbard And Jan Mayen',
        'SZ' => 'Swaziland',
        'SE' => 'Sweden',
        'CH' => 'Switzerland',
        'SY' => 'Syrian Arab Republic',
        'TW' => 'Taiwan',
        'TJ' => 'Tajikistan',
        'TZ' => 'Tanzania',
        'TH' => 'Thailand',
        'TL' => 'Timor-Leste',
        'TG' => 'Togo',
        'TK' => 'Tokelau',
        'TO' => 'Tonga',
        'TT' => 'Trinidad And Tobago',
        'TN' => 'Tunisia',
        'TR' => 'Turkey',
        'TM' => 'Turkmenistan',
        'TC' => 'Turks And Caicos Islands',
        'TV' => 'Tuvalu',
        'UG' => 'Uganda',
        'UA' => 'Ukraine',
        'AE' => 'United Arab Emirates',
        'GB' => 'United Kingdom',
        'US' => 'United States',
        'UM' => 'United States Outlying Islands',
        'UY' => 'Uruguay',
        'UZ' => 'Uzbekistan',
        'VU' => 'Vanuatu',
        'VE' => 'Venezuela',
        'VN' => 'Viet Nam',
        'VG' => 'Virgin Islands, British',
        'VI' => 'Virgin Islands, U.S.',
        'WF' => 'Wallis And Futuna',
        'EH' => 'Western Sahara',
        'YE' => 'Yemen',
        'ZM' => 'Zambia',
        'ZW' => 'Zimbabwe',
    );

    return $countries[$isoCode];
}

function isRowEmpty($objWorksheet, $row, $columnLength)
{
    for($cell = 0; $cell < $columnLength; ++$cell) {
        if ($objWorksheet->getCellByColumnAndRow($cell,$row)->getValue()) {
            return false;
        }
    }

    return true;
}
function geocodeCustomerArray($address,$usuario_id){
    $hereService = new HereService();

// JTZ - habilito por default doble geocode primero con here
//    $dobleGeocode = get_usuario_has_parametro($usuario_id, "dobleGeocode");
//    $geocodeHereFirst = get_usuario_has_parametro($usuario_id, "GEOCODE_HERE_FIRST");
    $dobleGeocode = 1;
    $geocodeHereFirst = 1;


    // Si es producción se usa google primero y here si no encontró en google.
     if(isProduction()){
        if ($geocodeHereFirst){
            $geocodeResult = $hereService->geocode_here_array($address);
        }else{
            $geocodeResult = geocoding_google_v4($address, 'AIzaSyDYlpmtksKDCVBWrQKrMX4C6pAnB8zC5Yk', $usuario_id);
        }
        if($dobleGeocode){

            // Segunda vuelta
            $toGeocodeSecond = [];
            foreach($geocodeResult as $idClient => $coords){

                if($coords['latitude'] == 0 || $coords['longitude'] == 0){
                    $toGeocodeSecond[$idClient] = $address[$idClient];
                    unset($geocodeResult[$idClient]);
                }
            }
            if(!empty($toGeocodeSecond)){
                loggear("TOTAL SIN GEOCODIFICAR PRIMERA PASADA " . count($toGeocodeSecond), "debug_geo");
                if ($geocodeHereFirst){
                    $geocodeResultSecond = geocoding_google_v4($toGeocodeSecond, 'AIzaSyDYlpmtksKDCVBWrQKrMX4C6pAnB8zC5Yk', $usuario_id);
                } else {
                    $geocodeResultSecond = $hereService->geocode_here_array($toGeocodeSecond);
                }
                $geocodeResult = $geocodeResult + $geocodeResultSecond;
            }
        }

    }else{
        $geocodeResult = $hereService->geocode_here_array($address);
    }

    return $geocodeResult;
}
function geocodeCustomer($address, $usuario_id){
    
    // Si es producción se usa google
    if(isProduction()){
        $geocodeResult = geocoding_google_v2($address, 'AIzaSyDYlpmtksKDCVBWrQKrMX4C6pAnB8zC5Yk', $usuario_id);
        
    }else{
        $hereService = new HereService();
        $geocodeResult = $hereService->geocode_here($address);
    }

    return $geocodeResult;
}

function checkTimeWindow($current_tw, $new_tw){
    //Cuando en el importador envian muchos productos para el mismo pedido (son múltiples lineas por pedido)
    $timezone = '';
    if(isset($current_tw) && $current_tw != 'FFFFFFFFFFFF'){
        if(($new_tw != 'FFFFFFFFFFFF') && ($current_tw != $new_tw)){
            $timezone = $new_tw ;
        }else{
            $timezone = $current_tw ;
        }
    }else{
        $timezone = $new_tw ;
    }
    return $timezone;
}

function checkPedidoClienteTimezone($c_timewindow,$p_timewindow){
    //si el pedido viene sin tw pero el cliente tiene uno configurado
    $timezone = $p_timewindow;
    if($p_timewindow == 'FFFFFFFFFFFF'){
        if($c_timewindow != 'FFFFFFFFFFFF'){
            $timezone = $c_timewindow;
        }
    }
    return $timezone;
}

function securizeInputStringData($data) {
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

function filterInputCortes($corte) {
    return filter_var(str_replace(',', '.', _post($corte, 0)), FILTER_VALIDATE_FLOAT);
}