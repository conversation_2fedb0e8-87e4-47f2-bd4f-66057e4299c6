<?
//error_reporting(E_ALL);
//ini_set('display_errors',1);
include_once('db/util_chameleon.php');
include_once('../util_session.php');
include_once('../util_db.php');
include_once('../interfaz_enwis.php');
include_once('../interfaz_enwis_chile.php');
include_once('../io/proactiva/interfaz_pedidos.php');
include_once('../interfaz_urbanoperu_pedidos.php');
include_once('../sda/interfaces/interfaz_sda_pedidos.php');
include_once('../sda/interfaces/interfaz_sda_informe_rutas.php');
include_once('../classes/ExcelExporter.php');
include_once('custom_loader.php');
include_once('../io/volta/interfaz_pedidos.php');
include_once('../io/ico/interfaz_ico_pedidos.php');
include_once(__DIR__ . '/../classes/DomainObjects/UsuariosParametro.php');
include_once(__DIR__ . '/../util_zonas_fn.php');
include_once(__DIR__ . '/../classes/Sanitizer.php');


manejar_sesion();
$id_usuario = $_SESSION['usuario_id'];
if (get_usuario_has_parametro($id_usuario, 'DB_ERROR_LOGGING')) {
    require_once('../db_error_logger.php');
}
$id_usuarios = getUsuariosPadres($id_usuario, false);
$usuarios_veoliachile   = array(7341, 7302, 7361);
$usuarios_urbano        = array(341, 8003);
$usuarios_repelub       = [7731, 7747, 7749, 8081];
$usuarios_sodimac_tiendas = [7913];
$usuarios_colun         = array(7359, 8137, 8139, 8141, 7357, 7981, 8143, 8145, 8147, 8149, 8151, 17496);
$usuarios_jamef         = array(6801, 7215); // perdon por seguir ensuciando esta cosa
$usuarios_volta         = array(8779, 8781, 8783, 8785, 9099, 9101, 9103, 9105, 9107, 9109, 9111, 9113, 9115, 9117, 9119, 9121, 9123, 9125, 9127, 9129, 9754, 9756, 9758, 9760, 9762, 9764, 9766, 9768, 9770, 9772, 9774, 9776, 9778, 9780, 9782, 9784, 9786, 9788, 9792, 9794, 11517, 11519, 10879, 11481, 11527, 11529);
$usuarios_becton        = UsuariosParametro::getUsersIdHasParameter('BECTON_USERS');
$usuarios_nestle        = array(8131, 8133, 8799, 8801, 9493, 9545, 9547, 9861);
$usuarios_lavirginia    = UsuariosParametro::getUsersIdHasParameter('LAVIRGINIA_USERS');
$usuarios_disnor        = array(415, 5450, 7073, 12835, 12837, 12839, 12841, 14092, 14463);
$usuarios_vanni         = UsuariosParametro::getUsersIdHasParameter('VANNI_USERS');
$usuario_cod_ruta_pers  = UsuariosParametro::getUsersIdHasParameter('REPARTO_NOMENCLATURA');
$usuarios_usieto        = [15616, 15678];
$usuarios_fresenius     = UsuariosParametro::getUsersIdHasParameter('FRESENIUS_USERS');
$usuarios_arcor         = UsuariosParametro::getUsersIdHasParameter('ARCOR_USERS');

session_write_close();

if (_post('data'))
    $data = _post('data');
if (_post('guid'))
    $guid = _post('guid');
if (_post('exportador'))
    $exportador = _post('exportador');
if (_post('importador'))
    $importador = _post('importador');
if (_post('cargaDatos')) {
    $carga = json_decode(stripslashes(_post('cargaDatos')));
    //    $carga = cleanEmptyCarga($carga);
}
if (_post('cargaMetaData')) {
    $cargaMetaData = json_decode(stripslashes(_post('cargaMetaData')));
}
if (_post('cargaSummaryData')) {
    $cargaSummaryData = json_decode(stripslashes(_post('cargaSummaryData')));
}
if (_post('cargaDatos')) {
    $cargaDatos = _post('cargaDatos');
}
if (_post('fecha_desde'))
    $fecha_desde = _post('fecha_desde');
if (_post('beln'))
    $beln = _post('beln');
if (_post('fecha_hasta'))
    $fecha_hasta = _post('fecha_hasta');
if (_post('fecha_asignacion'))
    $fecha_asignacion = _post('fecha_asignacion');
if (_post('fecha'))
    $fecha = _post('fecha');
if (_post('cortes'))
    $cortes = json_decode(stripcslashes(_post('cortes')));
if (_post('dispositivo'))
    $dispositivo = _post('dispositivo');
if (_post('agrupadores'))
    $agrupadores = json_decode(stripslashes(_post('agrupadores')));
if (_post('fecha_reparto'))
    $fecha_reparto = _post('fecha_reparto');
if (_post('salida'))
    $salida = _post('salida');
if (_post('pedido'))
    $id_pedido = _post('pedido');
if (_post('cliente'))
    $id_cliente = _post('cliente');
if (_post('deposito'))
    $deposito = _post('deposito');
if (_post('descarga'))
    $descarga = _post('descarga');
if (_post('agrupacion'))
    $agrupacion = _post('agrupacion');
if (_post('type'))
    $type = _post('type');

if (_get('data'))
    $data = _get('data');
if (_get('dispositivo'))
    $dispositivo = _get('dispositivo');
if (_post('id_assignment'))
    $id_assignment = _post('id_assignment');
if (_get('fecha'))
    $fecha = _get('fecha');
if (_get('vuelta'))
    $vuelta = _get('vuelta');
if (_post('programados'))
    $programados = _post('programados');
if (_post('planificador'))
    $planificador = _post('planificador');
if (isset($_POST['inputValue']))
    $inputValue = _post('inputValue');
if (_post('inputId'))
    $inputId = _post('inputId');
if (_post('pedidos'))
    $pedidos = $_POST['pedidos'];
if (_post('deletePedidos'))
    $deletePedidos = _post('deletePedidos');
if (_post('dispositivo'))
    $id_dispositivo = _post('dispositivo');
if (_post('id_maquina'))
    $id_maquina = json_decode(stripslashes(_post('id_maquina')), true);
if (_post('status'))
    $status = json_decode(stripslashes(_post('status')), true);
if (_post('carriers'))
    $carriers = json_decode(stripslashes(_post('carriers')), true);
if (_post('parametros'))
    $parametros = json_decode(stripslashes(_post('parametros')), true);
if (_post('orderId')) {
    $orderId = _post('orderId');
}
if (_post('orderIds')) {
    $orderIds = json_decode(stripslashes(_post('orderIds')));
}
if (_get('search_cliente'))
    $search_cliente = _get('search_cliente');

if (_post('color_maquina'))
    $color_maquina = _post('color_maquina');

if (_post('json_order_template')) {
    $json_order_template = json_decode(stripslashes(_post('json_order_template')));
}
if (_get('json_order_template')) {
    $json_order_template = json_decode(stripslashes(_get('json_order_template')));
}
if (_get('id_template')) {
    $id_template = _get('id_template');
}
if (_post('id_template')) {
    $id_template = _post('id_template');
}
if (_post('id_chofer')) {
    $id_chofer = _post('id_chofer');
}
if (_post('dayweek')) {
    $dayweek = _post('dayweek');
}
if (_post('create')) {
    $create = _post('create');
}

if (_post('rateId')) {
    $id_rate = _post('rateId');
}

$array = array();
switch ($data) {
    case 'dispositivos':
        $array = getDispositivos($id_usuario);
        break;
    case 'pedidos':
        $array = getPedidos($id_usuario, $fecha_desde, $fecha_hasta, $agrupadores, $fecha_asignacion, $salida, $programados, $planificador);
        break;
    case 'pedido':
        $array = getPedido($id_usuario, $id_pedido);
        break;
    case 'borrarPedidos':
        $array = borrarPedidos($id_usuario, $fecha_desde, $fecha_hasta, $deletePedidos);
        break;
    case 'zonas':
        $array = getZonas($id_usuario, $fecha);
        break;
    case 'guardarZona':
        $payload = json_decode(stripslashes(_post('payload')), true);
        $array = guardarZona($id_usuario, $payload);
        break;
    case 'eliminarZona':
        $payload = json_decode(stripslashes(_post('payload')), true);
        $return = eliminarZona($id_usuario, $payload);
        break;
    case 'save':
        $repartos_previos = getRepartosUsuario($id_usuario, $fecha, $salida);

        if (($exportador == 'exportadorPedidos_america.php') && ($id_usuario != 1575))
            $array = savePedidosAmerica($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_americaarcor.php') {
            $cargasToString = json_encode($carga);
            loggear("$id_usuario: DATA : $cargasToString", "EXPORTADOR_CHAMELEON");
            $array = savePedidosAmerica($id_usuario, $carga, $fecha, $salida);
        } else if (($exportador == 'exportadorPedidos_america.php') && ($id_usuario == 1575))
            $array = savePedidosAbarca($id_usuario, $carga, $fecha);
        else if (($exportador == 'exportadorPedidos_tmluc.php'))
            $array = savePedidosTmluc($id_usuario, $carga, $fecha, $salida);
        else if (($exportador == 'exportadorPedidos_sancor.php'))
            $array = savePedidosSancor($id_usuario, $carga, $fecha, $salida);
        else if (($exportador == 'exportadorExcel_america.php'))
            $array = savePedidosAmerica($id_usuario, $carga, $fecha, $salida);
        else if (($exportador == 'exportadorExcel_americaarcor.php') && (!in_array($id_usuario, $usuarios_arcor)))
            $array = savePedidosAmerica($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_veneroni.php')
            $array = savePedidosVeneroni($carga, $fecha);
        else if ($exportador == 'exportadorPedidos_ProactivaCompactacion.php' && $id_usuario != '5842')
            $array = savePedidosProactivaCompactacion($id_usuario, $carga, $fecha);
        else if ($exportador == 'exportadorPedidos_DeltacomCompactacion.php')
            $array = savePedidosDeltacomCompactacion($id_usuario, $carga, $fecha);
        else if ($exportador == 'exportadorPedidos_DeltacomOperaciones.php')
            $array = savePedidosDeltacomCompactacion($id_usuario, $carga, $fecha);
        else if ($exportador == 'exportadorPedidos_liberpago.php')
            $array = savePedidosLiberpago($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_lapreferida.php')
            $array = savePedidosLapreferida($id_usuario, $carga, $fecha, $salida);
        else if (in_array($id_usuario, $usuarios_urbano))
            $array = savePedidosUrbano($id_usuario, $carga, $fecha, $salida);
        else if (in_array($id_usuario, $usuario_cod_ruta_pers)) {
            $array = savePedidosCodRutaParam($id_usuario, $carga, $fecha, $salida);
        }
        else if ($exportador == 'exportadorPedidos_Disnor.php')
            $array = savePedidos($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == '<EMAIL>' && !in_array($id_usuario, $usuarios_disnor)) {
            if ($id_usuario == 13725 || $id_usuario == 5504 || $id_usuario == 16128)
                $array = savePedidosCodisaBatPilar($id_usuario, $carga, $fecha, $salida);
            else
                $array = savePedidosCodisaPilar($id_usuario, $carga, $fecha, $salida);
        } else if ($exportador == '<EMAIL>') {
            if ($id_usuario == 5528)
                $array = savePedidosCodisaPinamar($id_usuario, $carga, $fecha, $salida);
            else
                $array = savePedidosBailogisticaLogistica($id_usuario, $carga, $fecha, $salida);
        } else if ($exportador == 'exportadorPedidos_disma.php')
            $array = savePedidosDisma($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_conchaytoro.php')
            $array = savePedidosConchayToro($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_RicardoOspital.php')
            $array = savePedidosRicardoOspital($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_Vitalcan.php')
            $array = savePedidosVitalcan($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_razycia.php')
            $array = savePedidosRazycia($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_alimentosRefrigerados.php')
            $array = savePedidosAlimentosRefrigerados($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_Soprole.php')
            $array = savePedidosWhitoutRoutes($carga, $fecha, $salida, $cargaMetaData);
        else if ($exportador == 'exportadorPedidos_Emasa.php')
            $array = savePedidosVeneroni($carga, $fecha);
        else if ($exportador == 'exportadorPedidos_cial.php')
            $array = savePedidosVeneroni($carga, $fecha);
        else if ($exportador == "exportadorPedidos_corona.php")
            $array = savePedidosCorona($id_usuario, $carga, $fecha, $salida);
        else if ($exportador == 'exportadorPedidos_gregorio.php')
            $array = savePedidosGregorioDiez($carga, $fecha, $salida);
        else if ($id_usuario == 2740) {
            $array = savePedidosUrbanoPeru($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(5172, $id_usuarios)) {
            $array = savePedidosVolcan($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(7325, $id_usuarios)) {
            $array = savePedidosCamiloDosSantos($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(4155, $id_usuarios)) {
            $array = savePedidosSDA($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(2444, $id_usuarios)) {
            $array = savePedidosChamical($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_fresenius)) {
            $array = savePedidosFresenius($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(2104, $id_usuarios)) {
            $array = savePedidosDirectv($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(2444, $id_usuarios)) {
            $array = savePedidosChamical($id_usuario, $carga, $fecha, $salida);
        } else if ($id_usuario == 4388 || $id_usuario == 4392) {
            //dedicado a nueva operacion sepostales1 y sepostales2
            $array = savePedidosUrbano($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(4798, $id_usuarios)) {
            $array = savePedidosCencocal($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(5048, $id_usuarios)) {
            $array = savePedidosQXLogistica($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(5406, $id_usuarios)) {
            $array = savePedidosFreseniusProductos($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(5599, $id_usuarios)) {
            $array = savePedidosGoperu($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(4492, $id_usuarios)) {
            $array = savePedidosGrateful($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(5895, $id_usuarios)) {
            $array = savePedidosServipaq($id_usuario, $carga, $fecha, $salida);
        } else if (in_array(5614, $id_usuarios)) {
            $array = savePedidosDirecTVARG($id_usuario, $carga, $fecha, $salida);
        } else if (2683 == call_user_func(function ($id_usuario) {
            return mysql_result(
                mysql_query(
                    "SELECT id_erp_cliente
                                FROM usuarios
                                WHERE id = $id_usuario"
                ),
                0,
                0
            );
        }, $id_usuario)) {
            $array = savePedidosFleet($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_lavirginia)) {
            $array = savePedidosLaVirginia($id_usuario, $carga, $fecha, $salida, $cargaMetaData, $cargaSummaryData);
        } else if (in_array($id_usuario, $usuarios_jamef)) {
            $array = savePedidosJamef($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_veoliachile)) {
            $array = savePedidosVeoliaCompactacionChile($id_usuario, $carga, $fecha);
        } else if (in_array($id_usuario, $usuarios_repelub)) {
            $array = savePedidosRepelub($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_sodimac_tiendas)) {
            $array = savePedidosSodimac($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_vanni)) {
            $array = savePedidosVanni($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_nestle)) {
            $array = savePedidosNestleChile($id_usuario, $carga, $fecha, $salida);
        } else if ($id_usuario == 5842) {
            $array = savePedidosVeoliaBR($id_usuario, $carga, $fecha, $salida);
        } else if ($id_usuario == 9293) {
            $array = savePedidosClientesOnly($id_usuario, $carga, $fecha, $salida, $cargaMetaData);
        } else if (in_array($id_usuario, $usuarios_colun)) {
            $array = savePedidosColun($id_usuario, $carga, $fecha, $salida, $cargaMetaData, $cargaSummaryData);
        } else if (in_array($id_usuario, $usuarios_volta)) {
            $array = savePedidosVolta($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_becton)) {
            $array = savePedidosBectoDickInSon($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_disnor)) {
            $array = savePedidosDisnorNew($id_usuario, $carga, $fecha, $salida);
        } else if (in_array($id_usuario, $usuarios_usieto)) {
            $array = savePedidosUsieto($id_usuario, $carga, $fecha, $salida, $cargaMetaData, $cargaSummaryData);
        } else {
            $array = savePedidos($id_usuario, $carga, $fecha, $salida, $cargaMetaData, $cargaSummaryData);
        }
        
        $repartos_posteriores = getRepartosUsuario($id_usuario, $fecha, $salida);
        $resultado_publicacion = getRepartosPublicacion($repartos_previos, $repartos_posteriores);

        $array = [
            'array' => $array,
            'resultado' => $resultado_publicacion
        ];
        break;
    case 'partida':
        $array = getPuntoPartida($id_usuario);
        break;
    case 'updateCortes':
        $array = updateCortes($dispositivo, $cortes, $deposito, $descarga, $id_usuario, (isset($agrupacion) ? $agrupacion : false), $parametros, (isset($color_maquina) ? $color_maquina : false));
        break;
    case 'agrupadores':
        $array = getAgrupadores($id_usuario, $fecha_desde, $fecha_hasta);
        break;
    case 'agrupadores_2':
        $array = getAgrupadores2($id_usuario, $fecha_desde, $fecha_hasta);
        break;
    case 'agrupadores_3':
        $array = getAgrupadores3($id_usuario, $fecha_desde, $fecha_hasta);
        break;
    case 'choferes':
        $array = getChoferesPorUsuario($id_usuario);
        break;
    case 'center':
        $array = getCenterMap($id_usuario);
        break;
    case 'saveAssignment':
        $array = saveAssignment($carga, $id_usuario);
        break;
    case 'loadAssignment':
        $array = loadAssignment($id_assignment, $id_usuario);
        break;
    case 'getAssignment':
        $array = getAssignments($fecha, $id_usuario);
        break;
    case 'deleteAssignment':
        $array = deleteAssignment($id_assignment, $id_usuario);
        break;
    case 'deleteAutosaveAssignment':
        $array = deleteAutosaveAssignment($fecha, $id_usuario);
        break;
    case 'getAutosave':
        $array = getAutosave($fecha, $id_usuario);
        break;
    case 'getParametrosUsuario':
        $array = getParametrosUsuario($id_usuario);
        break;
    case 'addPedido':
        $array = addPedido($id_usuario, $id_cliente, $fecha);
        break;
    case 'getChoferesAsignados':
        $array = getChoferesAsignados($id_usuario, $fecha, $salida);
        break;
    case 'clientsToGeocode':
        $array = clientsToGeocode($fecha_desde, $fecha_hasta, $id_usuario, $agrupadores);
        break;
    case 'toGeocode':
        $array = toGeocode($fecha_desde, $fecha_hasta, $id_usuario, $agrupadores);
        break;
    case 'getInicioFinalRepartos':
        $array = getInicioFinalRepartos($id_dispositivo, $id_pedido);
        break;
    case 'getZonaDeposito':
        $array = getZonaDeposito($deposito, $id_usuario);
        break;
    case 'removeNotGeocoded':
        removeNotGeocoded($fecha_desde, $fecha_hasta, $id_usuario, $agrupadores);
        break;
    case 'informeUrbano':
        echo informeUrbano($id_usuario, $fecha);
        return;
        break;
    case 'informeRutasSchenker':
        $array = informeRutasSchenker($dispositivo, $fecha, $vuelta);
        break;
    case 'informeRutasSchenkerDanfoss':
        $array = informeRutasSchenkerDanfoss($dispositivo, $fecha, $vuelta);
        break;

    case 'informeRutasWinery':
        $array = informeRutasWinery($dispositivo, $fecha, $vuelta);
        break;
    case 'dataOutSDA':
        $array = informeRutasSDA($carga, $id_usuario);
        break;
    case 'dataOutCencocal':
        $array = dataOutCencocal($guid, $id_usuario);
        break;
    case 'dataOutUrbano':
        $array = dataOutUrbano($exportador, $carga);
        break;
    case 'dataOutUrbanoNOP':
        $array = dataOutUrbanoNOP($exportador, $carga);
        break;
    case 'dataOutUrbanoPeru':
        $array = dataOutUrbanoPeru($exportador, $carga);
        break;
    case 'dataOutUnir':
        $array = dataOutUnir($carga);
        loggear(print_r($array, "UNIR_WS"));
        break;
    case 'getTrafficConditions':
        $array = getTrafficConditions($id_usuario);
        break;
    case 'getTransferByType':
        $array = getTransfersByType($type, $id_usuario);
        break;
    case 'getPedidosEnwis':
        if (in_array($id_usuario, [5842, 5848, 5844, 7297])) {
            $array = getOrdersSigap($id_usuario, $fecha);
        } else if (in_array($id_usuario, [7341, 7302, 7361])) {
            $array = getOrderLinesCompactacionChile($id_usuario, $fecha);
        } else {
            $array = getOrderLinesCompactacion($id_usuario, $fecha);
        }
        break;
    case 'getPedidosUrbanoPeru':
        $array = getPendientesUrbanoPeru();
        break;
    case 'getPedidosVolta':
        $array = getOrdersSAP($id_usuario, $fecha);
        break;
    case 'getPedidosIco':
        $array = enviarPedidosIco($id_usuario, $fecha);
        break;
    case 'importarPedidosWS':
        $array = importarPedidosWS($id_usuario, $fecha_desde, $fecha_hasta, $beln);
        break;
    case 'importPedidosWS':
        $array = importPedidosWS($id_usuario, $fecha, null, null, $importador);
        break;
    case 'importPedidosGenericosWS':
        $array = importPedidosGenericosWS($id_usuario, $fecha, null, null, $importador);
        break;
    case 'downloadImportDataWS':
        $array = exportarExcelImportacionWS($cargaDatos);
        break;
    case 'importPedidosChess':
        $array = importPedidosWS($id_usuario, $fecha, $fecha_desde, $fecha_hasta, $importador);
        break;
    case 'importarPedidosCustomWs':
        $array = importarPedidosCustomWs($id_usuario, $fecha_desde, $fecha_hasta, $beln);
        break;
    case 'updateValorUnidad':
        $array = updateValorUnidad($inputValue, $inputId, $id_maquina);
        break;
    case 'getValoresUnidad':
        $array = getValoresUnidad($id_maquina);
        break;
    case 'getUserFilters':
        $array = getUserFilters($id_usuario);
        break;
    case 'exportarNoPlanificados':
        $array = exportarExcelNoPlanificados($id_usuario, $fecha_desde, $fecha_hasta, $fecha_asignacion);
        break;
    case 'exportarNoPlanificadosDetalle':
        $array = exportarExcelNoPlanificadosDetalle($id_usuario, $fecha_desde, $fecha_hasta, $fecha_asignacion);
        break;
    case 'downloadData':
        $array = exportarExcelPlanificados($id_usuario, $carga, $fecha, $salida);
        break;
    case 'downloadDataDetalle':
        $array = exportarExcelPlanificadosDetallado($id_usuario, $carga, $fecha, $salida);
        break;
    case 'downloadDataExtended':
        $array = getPedidosPlanificacionPlanoExtendido($id_usuario, $carga, $fecha, $salida);
        break;
    case 'getTiendaNubeToken':
        include_once '/var/www/saas/io/tiendanube/common.php';
        $array = getTiendaNubeToken($id_usuario);
        break;
    case 'getTiendaNubeOrders':
        include_once '/var/www/saas/io/tiendanube/common.php';
        $token = getTiendaNubeToken($id_usuario);
        $orders = getTiendaNubeOrders($fecha_desde, $fecha_hasta, $status, $token);
        $array = importTiendaNubeOrders($orders, $id_usuario, $carriers);
        break;
    case 'getTiendaNubeCustomCarriers':
        include_once '/var/www/saas/io/tiendanube/common.php';
        $token = getTiendaNubeToken($id_usuario);
        $orders = getTiendaNubeOrders($fecha_desde, $fecha_hasta, $status, $token);
        $array = getTiendaNubeCustomCarriersFromOrders($orders);
        break;
    case 'fulfillTiendaNubeOrders':
        include_once '/var/www/saas/io/tiendanube/common.php';
        $token = getTiendaNubeToken($id_usuario);
        $array = fulfillTiendaNubeOrdersFromGUID($guid, $id_usuario, $token);
        break;
    case 'cortes':
        $array = getCortes();
        break;
    case 'deleteOrder':
        $array = deleteOrder($id_usuario, $orderId);
        break;
    case 'deleteOrders':
        $array = deleteOrders($id_usuario, $orderIds);
        break;
    case 'clientAutocomplete':
        $array = clientAutocomplete($search_cliente, $id_usuario);
        break;
    case 'agrupadoresPedidos':
        $array = getAgrupadoresPedidos($id_usuario);
        break;
    case 'habilidadesPedidos':
        $array = getHabilidadesPedidos($id_usuario);
        break;
    case 'getCodigoPedido':
        $ultimo_codigo_pedido = (!empty(usuario_parametro($id_usuario, 'ULTIMO_CODIGO_PEDIDO')) ? usuario_parametro($id_usuario, 'ULTIMO_CODIGO_PEDIDO') : 0);
        $ultimo_codigo_pedido++;
        $array = $ultimo_codigo_pedido;
        break;
    case 'getCodigoCliente':
        $ultimo_codigo_cliente = (!empty(usuario_parametro($id_usuario, 'ULTIMO_CODIGO_CLIENTE')) ? usuario_parametro($id_usuario, 'ULTIMO_CODIGO_CLIENTE') : 0);
        $ultimo_codigo_cliente++;
        $array = $ultimo_codigo_cliente;
        break;
    case 'validateMerchant':
        $array = validateMerchantActivated($id_usuario);
        break;
    case 'getMerchantsIntegrations':
        $array = getMerchantsIntegrations($id_usuario);
        break;
    case 'saveParametrosUsuario':
        $array = saveParametrosUsuario($id_usuario, $inputId, $inputValue);
        break;
    case 'removeParametrosUsuario':
        $array = removeParametrosUsuario($id_usuario, $inputId);
        break;
    case 'saveOrderTemplates':
        $array = saveOrderTemplates($json_order_template);
        break;
    case 'getOrderTemplates':
        $array = getTemplates($id_usuario);
        break;
    case 'deleteTemplate':
        $array = deleteTemplate($id_usuario, $id_template);
        break;
    case 'setDefaultImport':
        $array = setDefaultImport($id_usuario, $new_default_import);
        break;
    case 'getChoferFromID':
        $array = getChoferFromID($id_chofer);
        break;
    case 'getPedidoDetalles':
        $array = getPedidoProductos($id_pedido);
        break;
    case 'generarVisitasxDiaCliente':
        $array = generarVisitasxDiaCliente($id_usuario,$fecha,$dayweek,$create);
        break;
    case 'getOrganizationRates':
        $array = getOrganizationRates($id_usuario);
        break;
    case 'getOrganizationTripTypes':
        $array = getOrganizationTripTypes();
        break;
    case 'updateVehicleRouteCostRate':
        $array = updateVehicleRouteCostRate($dispositivo, $id_rate);
        break;
    case 'updateVehicleRouteCostRateAll':
        $array = updateVehicleRouteCostRateAll($dispositivo, $id_rate);
        break;
    case 'updateVehicleRouteCostTripType':
        $array = updateVehicleRouteCostTripType($dispositivo, $id_rate);
        break;
    case 'updateVehicleRouteCostTripTypeAll':
        $array = updateVehicleRouteCostTripTypeAll($dispositivo,$id_rate);
        break;
    case 'ungroupOrder':
        $array = desagruparPedido($id_pedido);
        break;
    case 'ungroupAllOrders':
        $array = desagruparPedidos($orderIds);
        break;
}

echo json_encode($array);
