<?php

namespace App\Repositories;

use App\Models\ClientDeliveryEvent;
use App\Repositories\Base\BaseRepository;
use App\Repositories\Interfaces\IEventRepository;

/**
 * Class ClientDeliveryEventRepository
 * @package App\Repositories
 */
class ClientDeliveryEventRepository extends BaseRepository implements IEventRepository
{
    /**
     * @param $client_delivery_id
     * @return \Illuminate\Support\Collection
     */
    public function findByClientDelivery($client_delivery_id)
    {
        if (is_array($client_delivery_id)) {
            $query = $this->baseQuery()
                        ->select('ypf_client_delivery_event.*')
                        ->join('clientes_repartos', 'clientes_repartos.id', '=', 'ypf_client_delivery_event.client_delivery_id')
                        ->join('repartos', 'repartos.id', '=', 'clientes_repartos.id_reparto')
                        ->join('ypf_delivery_status', 'ypf_delivery_status.delivery_id', '=', 'repartos.id')
                        ->whereIn('ypf_client_delivery_event.client_delivery_id', $client_delivery_id)
                        ->whereNotIn('repartos.estado', ['TERMINADO', 'BAJA', 'PRELIMINAR'])
                        ->orderByRaw('CASE WHEN repartos.estado = "EN CURSO" THEN 0 ELSE 1 END')
                        ->orderBy('ypf_delivery_status.sequence')
                        ->orderBy('clientes_repartos.orden_visita')
                        ->orderBy('type_id');

            //\Log::debug('ClientDeliveryEventRepository->findByClientDelivery SQL ypf_eta_cache: ' . $query->toSql() .' | '.json_encode($client_delivery_id));
            return $query->get();
        } else {
            return $this->baseQuery()->where('client_delivery_id', $client_delivery_id)->get();
        }
    }

    /**
     * @param $client_delivery_id
     * @param $type_id
     * @return ClientDeliveryEvent|null
     */
    public function findByClientDeliveryAndType($client_delivery_id, $type_id)
    {
        return $this->baseQuery()->where('client_delivery_id', $client_delivery_id)->where('type_id', $type_id)->first();
    }

    /**
     * Find arrival event for client_delivery
     *
     * @param $client_delivery_id
     * @return ClientDeliveryEvent
     */
    public function findArrivalToClient($client_delivery_id)
    {
        $type_id = config('event_type_ids.' . config('event_type.ARRIVAL'));
        return $this->findByClientDeliveryAndType($client_delivery_id, $type_id);
    }

    /**
     * Delete events by client delivery
     *
     * @param $client_delivery_id
     * @return boolean
     */
    public function deleteByClientDelivery($client_delivery_id)
    {
        if (is_array($client_delivery_id)) {
            return $this->baseQuery()->whereIn('client_delivery_id', $client_delivery_id)->delete();
        } else {
            return $this->baseQuery()->where('client_delivery_id', $client_delivery_id)->delete();
        }
    }

    /**
     * Find ClientDeliveryEvents for the given date and deviceId
     *
     * @param $date
     * @param $deviceId
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public function findByDateAndDeviceId($date, $deviceId)
    {
        $events = $this->baseQuery()->with('clientDelivery.delivery')->whereHas('clientDelivery.delivery', function($query) use ($date, $deviceId) {
            $query->where('fecha', $date)->where('fecha_inicio', $date)->where('id_dispositivo', $deviceId);
        })->get();

        return $events;
    }

    /**
     * Find ClientDeliveryEvents for the given date and start date and deviceId
     *
     * @param $date
     * @param $deviceId
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public function findByCurrentDateAndDeviceId($date, $deviceId)
    {
        $events = $this->baseQuery()->with('clientDelivery.delivery')->whereHas('clientDelivery.delivery', function($query) use ($date, $deviceId) {
            $query->where('fecha', $date)->where('fecha_inicio', $date)->where('id_dispositivo', $deviceId);
        })->get();

        return $events;
    }

    /**
     * Find ClientDeliveryEvents for the given date and driverId
     *
     * @param $date
     * @param $driverId
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public function findByDateAndDriverId($date, $driverId)
    {
        $events = $this->baseQuery()->with('clientDelivery.delivery')->whereHas('clientDelivery.delivery', function($query) use ($date, $driverId) {
            $query->where('fecha', $date)->where('id_chofer', $driverId);
        })->get();

        return $events;
    }

    /**
     * Find ClientDeliveryEvents for the given date
     *
     * @param $date
     * @return \Illuminate\Database\Eloquent\Collection|static[]
     */
    public function findByDate($date)
    {
        $events = $this->baseQuery()->with('clientDelivery.delivery')->whereHas('clientDelivery.delivery', function($query) use ($date) {
            $query->where('fecha', $date);
        })->get();

        return $events;
    }
}