<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Dependencies\InterfaceController as Dependencies;
use App\Jobs\TasJob;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Log;
use App\Helpers\StorkUtils;
use App\Helpers\YPFUtils;

/**
 * Class InterfaceController
 * @package App\Http\Controllers
 */
class InterfaceController extends Dependencies
{
    /**
     * Arrival to terminal event
     * Fired from /cli/terminalArrivalEvent.php
     *
     * @param Request $request
     * @return \Laravel\Lumen\Http\ResponseFactory|\Symfony\Component\HttpFoundation\Response|void
     */

    public function inTerminal(Request $request)
    {
        // DEVICE_ID es id de la tabla dispositivos
        $device_id = $request->input('device_id');
        $terminal_id = $request->input('terminal_id');

        Log::info("InterfaceController->inTerminal - Request de dispositivo $device_id a terminal $terminal_id");

        $timestamp = $request->input('timestamp');
        // ID DE REPARTO
        $delivery_id = $request->input('delivery_id');

        // Busca en la tabla ypf_device según el device_id ( id de dispositivo )
        $device = $this->deviceRepository->findByDeviceId($device_id);
        try {
            $date = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $timestamp, 'America/Argentina/Buenos_Aires')->toDateString();
        } catch (\Exception $e) {
            Log::error("InterfaceController l45 - Error formato fecha en informes: ".$e->getMessage());
            $date = Carbon::now()->toDateString();
        }
        $tomorrow = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $timestamp, 'America/Argentina/Buenos_Aires')->addDays(1)->toDateString();;
        $secsToMidnight = strtotime("tomorrow 00:00:00") - strtotime($timestamp);

        //Chequeo que no tenga viajes perdidos -> stork
        $this->dropUnfinishedDeliveries($device_id, $date);

        //Si me llega el delivery id es por que estoy recalculando
        if($delivery_id){
            Log::info("InterfaceController->inTerminal - Marcando llegada de dispositivo $device_id a terminal $terminal_id (RECALCULAR)");
            $event_type = config('event_type.IN_TERMINAL');
            $this->scheduleEventsInterface
                ->updateTerminalEvent(
                    $delivery_id,
                    $terminal_id,
                    $timestamp,
                    $event_type
                );

            $this->logger->newInfoLog("Llegada de unidad $device->domain a terminal. (RECALCULAR)")->save();
            return;
        }


        /** @var Collection $events */
        $events_today = $this->eventsRepository->findTerminalAndClientDeliveryByDateAndDeviceId($date, $device_id);

        if($secsToMidnight < 5400)
            $events = $events_today->merge(
                $this->eventsRepository->findTerminalAndClientDeliveryByDateAndDeviceId($tomorrow, $device_id)
            );
        else
            $events = $events_today;

        if($events->isEmpty()) {
            Log::error("InterfaceController->inTerminal - deviceId: $device_id terminalId: $terminal_id timestamp: $timestamp Events not found.");
            return;
        }

        // Obtiene el nextCheckpoint que es el primer ypf_client_delivery_event ordenado por estimated

        $sorted = $events->sortBy("estimated");

        $lastExecuted = $sorted->filter(function($e){
            return $e->real;
        })->last();

        Log::info("InterfaceController->inTerminal - lastExecuted " . json_encode($lastExecuted));


        $nextCheckpoint = $sorted->first(function($e, $v) use ($lastExecuted) {

            Log::info("InterfaceController->inTerminal - nextCheckpoint v: " . json_encode($v) . " e: " . json_encode($e). " lastExecuted: " . json_encode($lastExecuted) . " isCheckpoint: " . $this->isCheckpoint($v));

            return !$lastExecuted || ($v->estimated > $lastExecuted->estimated && !$v->real && $this->isCheckpoint($v));
        });

        // Si no hay proximo evento a realizar
        if (!isset($nextCheckpoint) || $nextCheckpoint == null) {
            Log::error("InterfaceController->inTerminal - nextCheckpoint null $device_id $terminal_id $timestamp");
            return;
        }

        Log::info("InterfaceController->inTerminal - nextCheckpoint: ".json_encode($nextCheckpoint));

        Log::debug("events_today: ".json_encode($events_today)." | events: ".json_encode($events)." | nextcheckpoint: ".json_encode($nextCheckpoint));

        $nextCheckpointTime = Carbon::createFromFormat('Y-m-d H:i:s', $nextCheckpoint->estimated, 'America/Argentina/Buenos_Aires');
        $diffFromCheckpoint = Carbon::createFromFormat('Y-m-d H:i:s', $timestamp, 'America/Argentina/Buenos_Aires')
            ->diffInSeconds($nextCheckpointTime, false);
        if($nextCheckpoint->eventType->code != config('event_type.IN_TERMINAL') && $diffFromCheckpoint < -7200){
            //Por algun motivo no detecte el cliente por mas de 2 horas y el camion volvio a la terminal
            $nextCheckpoint = $this->eventsHelper->findNextToOccurTerminal($events);
        }

        if(!is_object($nextCheckpoint) || !is_object($nextCheckpoint->eventType)){
            Log::error("InterfaceController->InTerminal - NextCheckpoint Error Not Object: ".json_encode
                ($nextCheckpoint));
            return;
        }

        // si el proximo evento a realizar no era llegada a terminal
        if ($nextCheckpoint->eventType->code == config('event_type.ARRIVAL')) {
            Log::error("InterfaceController->inTerminal - ".json_encode($nextCheckpoint)." Volvió cuando tuvo que haber ido a un cliente. Device: $device_id EVENTOS: ".json_encode($events));
            return;
        }

        // si el proximo evento a realizar SI es llegada a terminal (double check?)
        if ($nextCheckpoint->eventType->code == config('event_type.IN_TERMINAL') ||
            $nextCheckpoint->eventType->code == config('event_type.LOAD') ||
            $nextCheckpoint->eventType->code == config('event_type.PREDISPATCH')) {

            // si la terminal a la que llego corresponde al evento
            if($nextCheckpoint->terminal_id != $terminal_id) {
                Log::error("InterfaceController->inTerminal - Esperaba llegada de $device_id a terminal $nextCheckpoint->terminal_id, recibi llegada a $terminal_id");
                return;
            }

            //Obtiene el id del reparto
            $deliveryId = $nextCheckpoint->getDeliveryId();

            //Chequeo Inicio de Jornada Adelantado
            $this->checkInicioJornadaAdelantado($terminal_id, $timestamp, $device_id, $deliveryId);

            // Busca el reparto
            $delivery = $this->deliveryRepository->findById($deliveryId);

            $status = $delivery->deliveryStatus;
            $status->current_status = config('delivery_current_status.INZONE');
            $status->save();

            $shift = $this->shiftRepository->findById($delivery->DeliveryStatus->shift_id)->first();

            Log::info("InterfaceController->inTerminal - Marcando llegada de dispositivo $device_id a terminal $terminal_id");

            $event_type = config('event_type.IN_TERMINAL');

            $this->scheduleEventsInterface
                ->updateTerminalEvent(
                    $deliveryId,
                    $nextCheckpoint->terminal_id,
                    $timestamp,
                    $event_type
                );
            Log::info("InterfaceController->inTerminal - Marcando llegada de dispositivo $device_id a terminal $terminal_id. Done!");

            //Actualizo el evento de inicio de turno
            if($shift){
                Log::debug("InterfaceController->InTerminal First Shift id for date: $date, device: $device->id: $shift");
                $this->scheduleEventsInterface->updateShiftEvent($shift->id, $timestamp, config('event_type.SHIFT_START'));
            } else {
                Log::debug("InterfaceController->InTerminal shift not found for device : ".$device->id." date: $date");
            }
        }

        Log::debug("InterfaceController->InTerminal no actualiza el evento para el device : ".$device->id." date: $date");
        return;
    }

    /**
     * Given a collection of events, returns the next to happen event.
     *
     * @param Collection $events
     *
     * @return IEventModel
     */
    private function isCheckpoint($event){
        return  $event->EventType->code == config('event_type.IN_TERMINAL') ||
            $event->EventType->code == config('event_type.ARRIVAL');
    }

    /**
     * Reprioritization, predispatch event, load event
     * Fired externally
     * An events batch is fired in every request.
     *
     * @param Request $request
     */
    public function TAS(Request $request)
    {
        try {
            $job = (new TasJob($request))->onQueue('tas');
            $jobId = $this->dispatch($job);
            return;
        }catch (\Exception $err){
            return $err;
        }

    }

    /**
     * Client alerts
     * Fired externally
     *
     * @param Request $request
     */
    public function IVR(Request $request)
    {
        /*
            {
               "key":"Qnqds9&@VH88EPsSu8D3@G!Q7XI3QR2b#at4p99#0DmQ6w9skIEU72imS!myKCnY",
               "timestamp":"",
               "incidencias":[
                  {
                     "numero_incidencia":"0500011692",
                     "apies":"03105",
                     "cuenta_sgc":"17760394",
                     "direccion_entrega":"000",
                     "timestamp":"2017-07-24 12:51:00",
                     "motivo":"A1-ZABA0001-ZA03",
                     "submotivo":"011",
                     "desc_submotivo":"QUIEBRE TOTAL D500",
                     "detalle":"SE37- PRUEBA QUADMINDS- QUIEBRE TOTAL D500"
                  }
               ]
            }
         */
        $data = json_decode($request->input('data'), TRUE);
        $incidencias = $data['indicencias'];

        foreach($incidencias as $inc) {
            $client_code = $inc['cuenta_sgc']."-".$inc['direccion_entrega'];
            $motivo = $inc['motivo'];
            $submotivo = $inc['submotivo'];
            $detalle = $inc['detalle'];
            $this->IVRInterface->generateClientAlert($motivo, $submotivo, $client_code, $detalle);
        }
    }

    /**
     * Deliveries create/update
     * Dispatch event fired on update
     *
     * @param Request $request
     */
    public function deliveries(Request $request)
    {
        $data = json_decode($request->input('data'), TRUE);
        //$date = Carbon::parse($data['fecha_carga']);
        try {
            $date = Carbon::parse($data['fecha_carga']);
        } catch (\Exception $e) {
            \Log::error("InterfaceController l269 - Error formato fecha: ".$e->getMessage());
            $date = Carbon::now();
        }

        $is_pending = $date->hour == 0 && $date->minute == 0 && $date->second == 0;
        if ($is_pending) {
            YPFUtils::logResponseInDb($data['interfaz'], json_encode(array('predespacho' => $data['predespacho'], 'action' => 'A', 'response' => 'Error: el viaje corresponde a un pendiente por su formato de fecha de carga: '.$data['fecha_carga'])));
            return;
        }

        try{
            $this->deliveriesInterface->update($data);
        }catch (\Exception $err){
            YPFUtils::logResponseInDb($data['interfaz'], json_encode(array('predespacho' => $data['predespacho'], 'action' => 'A', 'response' => 'Error: hubo un error en el proceso de generación de reparto '.$err->getMessage())));
            return $err;
        }
    }



    /**
     * @param Request $request
     */
    public function pendingManual(Request $request){
        $this->deliveriesInterface->import($request->input('data'));
    }

    /**
     * Dispatch to client event
     * Fired from deliveries interface, on update
     *
     * @param Request $request
     */
    public function dispatchEvent(Request $request)
    {
        $event_type = config('event_type.DEPARTURE');
        $delivery_id = $request->input('delivery_id');
        $terminal_id = $request->input('terminal_id');
        $timestamp = $request->input('timestamp');

        \Log::info('InterfaceController->dispatchEvent: '.json_encode($request));

        $this->scheduleEventsInterface->updateTerminalEvent($delivery_id, $terminal_id, $timestamp, $event_type);
    }

    /**
     * Deliveries soft delete
     * Fired from interfaz_ypf_baja_repartos.php
     *
     * @param Request $request
     */
    public function deliveryDrop(Request $request)
    {
        $delivery_code = $request->input('predespacho');
        $terminal_code = $request->input('terminal');

        $data['predespacho'] = $delivery_code;
        $data['tp_novedad'] = 'B';

        $this->deliveriesInterface->drop($delivery_code, $terminal_code, $data);
    }

    /**
     * Arrival to client event
     * Fired from /cli/clientArrivalEvent.php
     *
     * @param Request $request
     */
    public function arrival(Request $request)
    {
        $event_type = config('event_type.ARRIVAL');
        $delivery_client_id = $request->input('delivery_client_id');

        $timestamp = $this->clientDeliveryRepository->findById($delivery_client_id)->fecha_visitado;

        $this->clientDeliveryRepository->notifyClientDeliveryArrival($delivery_client_id);

        $this->logger->newInfoLog("Llegada a cliente.")->save();
        \Log::info("Llegada a cliente: $delivery_client_id");


        $this->scheduleEventsInterface->updateClientDeliveryEvent($delivery_client_id, $timestamp, $event_type);
    }

    /**
     * Unload event
     * Fired from interfaz_ypf_sitrack.php
     *
     * @param Request $request
     * @return string
     */
    public function unload(Request $request)
    {
        $event_type = config('event_type.UNLOAD');
        $delivery_client_id = $request->input('delivery_client_id');
        $timestamp = $request->input('timestamp');

        $clientDelivery = $this->clientDeliveryRepository->findById($delivery_client_id);

        if(is_null($clientDelivery)) {
            Log::info("InterfaceController->unload: client_delivery not found id: $delivery_client_id");
            return;
        }

        $device = $this->deviceRepository->findByDeviceId($clientDelivery->id_dispositivo);

        if (is_null($device)) {
            Log::info("InterfaceController->unload: device not found id: $clientDelivery->id_dispositivo");
            return;
        }

        $this->logger->newInfoLog("Evento descarga unidad $device->domain.")->save();

        $this->scheduleEventsInterface->updateClientDeliveryEvent($delivery_client_id, $timestamp, $event_type);
    }

    /**
     * Return to terminal event
     * Fired from /cli/clientDepartureEvent.php
     *
     * @param Request $request
     */
    public function clientDeparture(Request $request)
    {
        $event_type = config('event_type.CLIENT_DEPARTURE');
        $delivery_client_id = $request->input('delivery_client_id');
        $timestamp = $request->input('timestamp'); //fecha_fin_visita -> debe ir a CLIENT_DEPARTURE event

        $client_delivery = $this->clientDeliveryRepository->findById($delivery_client_id);

        // every event from client delivery
        $events = $this->clientDeliveryEventRepository->findByClientDelivery($delivery_client_id);

        // event which is going to be set as real
        $event = $events->filter(function($e) use($event_type) {
            return ($e->eventType->code == $event_type) && is_null($e->real);
        })->first();

        // if is the last one, the delivery is finished
        if($this->eventsHelper->isLastEvent($event, $events)) {
            $delivery = $client_delivery->delivery;
            $status = $delivery->deliveryStatus;
            $status->current_status = config('delivery_current_status.FINISHED');
            $status->save();
        }

        $this->scheduleEventsInterface->updateClientDeliveryEvent($delivery_client_id, $timestamp, $event_type);

        if (isset($client_delivery->fecha_inicio_tarea) && $client_delivery->fecha_inicio_tarea) {
            StorkUtils::updateDescarga($client_delivery->delivery->id, 'start_download', $client_delivery->fecha_inicio_tarea);
        }

        if (isset($client_delivery->fecha_fin_tarea) && $client_delivery->fecha_fin_tarea) {
            StorkUtils::updateDescarga($client_delivery->delivery->id, 'finish_download', $client_delivery->fecha_fin_tarea);
        }
    }

    /**
     * Checks for the estimated break time
     * Fired from /cli/checkBreakAlert.php
     *
     * @param Request $request
     */
    public function checkBreakAlert(Request $request)
    {
        $client_id = $request->input('client_id');
        $product_code = $request->input('product_code');

        $this->alertService->checkBreak($client_id, $product_code);
    }

    /**
     * Checks for the estimated break time
     */
    public function updateCacheEtas()
    {
        $this->cacheETAs->updateETAsCache();
    }

    /**
     * Checks for the estimated break time
     */
    public function updateETAsCacheETAEngine()
    {
        $this->cacheETAs->updateETAsCacheETAEngine();
    }

    /**
     * todo: pasar a service o a interfaz
     *
     * Create the shift events (shift_start)
     * Should be called from availability app when an availability is saved, updated or deleted
     *
     * @param Request $request
     */
    public function createShiftEvents(Request $request)
    {
        $shift_ids = $request->input('shift_ids');
        $this->shiftService->newShiftsInterface($shift_ids);
    }

    /**
     * Separate the TAS interface data into repriorizations and the rest of events
     *
     * @param $data
     * @return array
     */
    protected function organizeTASData($data)
    {
        $grouped = collect($data)
            ->filter(function($item) { return array_key_exists('estado', $item); })
            ->groupBy(function($item) { return $item['estado'] == '000' ? 'repr' : 'other'; })
            ->toArray();

        $repr = array_key_exists('repr', $grouped) ? $grouped['repr'] : [];
        $other = array_key_exists('other', $grouped) ? $grouped['other'] : [];

        return [$repr, $other];
    }

    /**
     * @param Request $request
     */
    public function manualReEstimation(Request $request){
        $device_id = $request->input('device_id');
        $date = $request->input('date');
        if($request->input('starting_time'))
            $starting_time = $request->input('starting_time');
        else
            $starting_time = null;

        $this->deliveriesInterface->reEstimateByDateAndDevice($device_id, $date, $starting_time);
    }


    public function manualReEstimationWithReal(Request $request){
        $device_id = $request->input('device_id');
        $date = $request->input('date');

        $this->deliveriesInterface->reEstimateByDateAndDeviceWithReal($device_id,$date);
    }

    //Cierra trips viejos en stork
    private function dropUnfinishedDeliveries($device_id, $date){
        $deliveries_id = $this->deliveryRepository->findUnfinishedDeliveries($device_id);

        if (!empty($deliveries_id)) {
            foreach($deliveries_id as $delivery_id) {
                $data = [];
                $data['fecha_carga'] = $date;
                $data['predespacho'] = $delivery_id;

                //Stork Integration
                try {
                    $url = 'http://127.0.0.1/saas/ypf/ypf-stork/drop_pending.php';
                    $trip = json_encode($data);
                    $opts = array('Content-type: application/json');
                    $curl = curl_init($url);
                    curl_setopt($curl, CURLOPT_POST, true);
                    curl_setopt($curl, CURLOPT_POSTFIELDS, $trip);
                    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($curl, CURLOPT_HTTPHEADER, $opts);
                    curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "POST");
                    $response = curl_exec($curl);
                    $curlErrno = curl_errno($curl);
                    $error = json_encode(curl_error($curl));
                    curl_close($curl);

                    if ($curlErrno) {
                        \Log::error('InterfaceController->inTerminal->dropUnfinishedDeliveries - No se pudo dar de baja el viaje desde InterfaceController->inTerminal->dropUnfinishedDeliveries. Data: '. $trip . ' + cURL error: '. $error);
                    } else {
                        Log::info("InterfaceController->inTerminal->dropUnfinishedDeliveries - Baja enviada a Stork. Predespacho:  ".$delivery_id." Response:".$response);
                    }
                } catch (\Exception $e) {
                    \Log::error('Error al dar de baja el viaje desde InterfaceController->inTerminal->dropUnfinishedDeliveries. '.$e->getMessage().' - Data: '. $trip );
                }
            }
        }
    }

    private function checkInicioJornadaAdelantado($terminal_id, $timestamp, $device_id, $deliveryId) {
        $max_time_diff = 90; //90 min de adelanto máximo
        $timeDiff = $this->terminalEventRepository->findEarlyArrival($terminal_id, $timestamp, $deliveryId);

        if ($timeDiff) {
            //Si está adelanto, el timeDiff viene negativo
            if ($timeDiff < 0 && $max_time_diff > abs($timeDiff)) {
                //Alerta stork
                $hours = floor(abs($timeDiff) / 60);
                $minutes = (abs($timeDiff) % 60);
                $value = $hours.'h '.$minutes.'m';
                sendAlertStork($device_id, 'Inicio jornada adelantado: ' . $value, $value, $timestamp);
                loggear("Dispositivo $device_id llego a la terminal $terminal_id con más de 90min de adelanto", 'ypf_llegada_terminal', "", "log_ypf");
            }
        }
    }
}
