<?php

$app->post('/in-terminal', [
    'middleware' => 'deviceValidation',
    'uses' => 'InterfaceController@inTerminal'
]);

$app->post('/deliveries', [
    'middleware' => 'interfaceAuth',
    'uses' => 'InterfaceController@deliveries'
]);

$app->post('/pending', [
    'middleware' => 'interfaceAuth',
    'uses' => 'ImportsController@pending'
]);

$app->post('/pending/daily', [
    'middleware' => 'interfaceAuth',
    'uses' => 'ImportsController@pendingDaily'
]);

$app->post('/pendingManual', [
    'middleware' => 'interfaceAuth',
    'uses' => 'InterfaceController@pendingManual'
]);

$app->post('/arrival', [
    'middleware' => 'clientDeliveryValidation',
    'uses' => 'InterfaceController@arrival'
]);

$app->post('/dispatch', [
    'as' => 'dispatchInterface',
    'uses' => 'InterfaceController@dispatchEvent'
]);

$app->post('/ivr', [
    'middleware' => 'interfaceAuth',
    'uses' => 'InterfaceController@IVR'
]);

$app->post('/unload', [
    'uses' => 'InterfaceController@unload'
]);

$app->post('/clientdeparture', [
    'middleware' => 'clientDeliveryValidation',
    'uses' => 'InterfaceController@clientDeparture'
]);

$app->post('/tas', [
    'middleware' => 'interfaceAuth',
    'uses' => 'InterfaceController@TAS'
]);

$app->post('/shiftevents', [
    'middleware' => 'internalCallMiddleware',
   'uses' => 'InterfaceController@createShiftEvents'
]);

$app->post('/shifteventsManual', [
    'middleware' => 'internalCallMiddleware',
    'uses' => 'InterfaceController@createShiftEvents'
]);

$app->post('/delivery-drop', [
    'middleware' => 'interfaceAuth',
    'uses' => 'InterfaceController@deliveryDrop'
]);

$app->post('/check-break-alert', [
    'middleware' => 'internalCallMiddleware',
    'uses' => 'InterfaceController@checkBreakAlert'
]);

$app->post('/update-etas-cache', [
    'middleware' => 'internalCallMiddleware',
    'uses' => 'InterfaceController@updateCacheEtas'
]);

$app->post('/update-etas-cache-eta-engine', [
    'middleware' => 'internalCallMiddleware',
    'uses' => 'InterfaceController@updateETAsCacheETAEngine'
]);

$app->post('/manualReEstimation', [
        'middleware' => 'internalCallMiddleware',
        'uses' => 'InterfaceController@manualReEstimation'
]);

$app->post('/manualReEstimationWithReal', [
    'middleware' => 'internalCallMiddleware',
    'uses' => 'InterfaceController@manualReEstimationWithReal'
]);