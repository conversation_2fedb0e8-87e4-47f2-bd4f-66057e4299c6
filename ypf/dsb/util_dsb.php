<?php

include_once('/var/www/saas/util_db.php');
include_once('/var/www/saas/util_session.php');
include_once('/var/www/saas/common_functions.php');
include_once('/var/www/saas/falcon/cors.php');
include_once('/var/www/saas/classes/DomainObjects/UsuariosParametro.php');


$id_usuario_ypf = 3050;
$codigos_gas = ["402500", "402700", "408000", "410100", "410600", "408400"];
$terminal_id_gas = ["11162"];
$cachedPolygons = [];

function getTerminales($id_usuario) {
    global $id_usuario_ypf;
    global $ypf_terminals;
    global $ypf_gas_user;
    $terminales = [];

    if ($ypf_terminals) {
        $isAdmin = mysql_result(mysql_query("SELECT rol_id FROM usuarios_rol WHERE usuario_id = '$id_usuario' AND rol_id = 'YPF_ADMIN'"), 0);

        //$terminales_codigos = "'0012', '0002', '0006', '0009', '0016', '0029', '0013', '0004', '0017', '0021', '0034', '0024', '0022', '0020', 'META-MET1', '0030'";
        if (!$isAdmin && !$ypf_gas_user) {
            $terminales_x_router = mysql_result(mysql_query("SELECT GROUP_CONCAT(pp.codigo_erp)
                            FROM puntos_partida pp
                            WHERE pp.id IN (SELECT terminal_id FROM ypf_router_terminal WHERE router_id = '$id_usuario')
                            AND pp.codigo_erp IN ($ypf_terminals)"), 0);
        } else {
            $terminales_x_router = $ypf_terminals;
        }

        $query_terminales = mysql_query("SELECT pp.id,
                                            CASE WHEN pp.descripcion = pp.codigo_erp THEN descripcion
                                                ELSE CONCAT(pp.descripcion, ' - ', pp.codigo_erp)
                                            END AS title
                                        FROM puntos_partida pp
                                        WHERE pp.id_usuario = $id_usuario_ypf
                                        AND pp.codigo_erp IN ($terminales_x_router)
                                        AND pp.id NOT IN ('21683')
                                        ORDER BY title");

        if (mysql_num_rows($query_terminales)) {
            while ($row = mysql_fetch_assoc($query_terminales)) {
                $terminales[] = $row;
            }
        }
    }

    echo json_encode(["data" => $terminales, "meta" => []], JSON_UNESCAPED_SLASHES);
    return http_response_code(200);
}

function getTrips($terminal_id, $fecha_desde, $fecha_hasta, $app = '') {
    global $id_usuario_ypf;
    date_default_timezone_set('America/Argentina/Buenos_Aires');
    $currentDate = time();
    $today = date("d-m-Y");
    $fecha = date("d-m-Y", (strtotime($fecha_desde)));
    $desde_pendiente = date("Y-m-d", strtotime("-1 month"));
    $hasta_pendiente = date("Y-m-d", strtotime($fecha));
    $fecha_descargas = date("Y-m-d", strtotime($fecha));

    $isToday = ($today === $fecha);

    if ($app != 'aglomeraciones') {
        if ($isToday) {
            $where = " AND r.fecha = '$fecha_descargas' AND r.fecha_inicio = '$fecha_descargas' ";
        } else {
            $where = " AND r.fecha_inicio = '$fecha_descargas' ";
        }
    } else {
        if ($isToday) {
            $where = " AND r.fecha BETWEEN '$fecha_desde' AND '$fecha_hasta' ";
        } else {
            $where = " AND r.fecha_inicio BETWEEN '$fecha_desde' AND '$fecha_hasta' ";
        }
    }

    $response = [
        "pending" => ["total" => 0, "total_productos" => 0, "onTime" => 0, "late" => 0, "trips" => []],
        "predispatch" => ["total" => 0, "total_productos" => 0, "onTime" => 0, "late" => 0, "trips" => []],
        "departureToClient" => ["total" => 0, "total_productos" => 0, "onTime" => 0, "late" => 0, "trips" => []]
    ];

    $sql_repartos = getSqlRepartos($terminal_id, $where);
    if ($app == 'aglomeraciones') {
        $sql_pendientes = getSqlRepartos($terminal_id, false, $desde_pendiente, $hasta_pendiente);
    } else {
        $sql_pendientes = getSqlRepartos($terminal_id, false, $fecha_descargas);
    }

    //loggear("Full trips: " . $sql_repartos, "ypf_dsb_debug");
    //loggear("Full dispositivos: " . $sql_dispositivos, "ypf_dsb_debug");
    $r_trips = mysql_query($sql_repartos);
    $r_pendientes = mysql_query($sql_pendientes);
    //QS-11664
    $en_playa = getEnPlaya($terminal_id, $fecha_descargas);

    $dispositivos_en_terminal = [];

    if (!empty($en_playa)) {
        foreach($en_playa as $row) {
            if (!$dispositivos_en_terminal[$row['id_dispositivo']]) {
                $dispositivos_en_terminal[$row['id_dispositivo']] = $row;
            }
        }
    }

    $processedTrips = [];
    $processedDevices = [];
    if (mysql_num_rows($r_trips)) {
        while ($row = mysql_fetch_assoc($r_trips)) {
            $estado = '';
            $codigoReparto = normalizeCodigoReparto($row['codigo_reparto']);

            //Pendiente
            if (!isBios($row)) $estado = 'pending';

            //En playa
            if (!empty($dispositivos_en_terminal[$row['id_dispositivo']]) && $isToday) {
                //QS-12621
                if (isBios($row) && $app != 'aglomeraciones') {
                    unset($dispositivos_en_terminal[$row['id_dispositivo']]);
                    continue;
                }
                $evento = getEvento($row);
                $estado = 'predispatch';
                $row['eta_estimado'] = $row['eta_estimado_cliente'];
                $row['eta_real'] = ($evento ? $evento : $dispositivos_en_terminal[$row['id_dispositivo']]['ult_actualizacion']);
            }
            //Despachados
            if (!isBios($row) && date('H:i:s', strtotime($row['fecha_presentacion'])) != '00:00:00') {
                $estado = "departureToClient";
                $row['eta_estimado'] = $row['eta_estimado_cliente'];
                $row['eta_real'] = $row['fecha_presentacion'];
            }

            if (!$estado || ($estado == "departureToClient" && !$isToday)) continue;

            $trip = [
                'id' => $row['id_reparto'],
                'id_dispositivo' => strval($row['id_dispositivo']),
                'code' => $row['codigo_reparto'],
                'distance' => round($row['distance'] * 111195, 0),
                'distanceCliente' => round($row['distance_cliente'] * 111195, 0),
                'vehicle' => $row['nombre'],
                'client' => $row['razon_social'],
                'scheduled' => $row['eta_programado'],
                'eta' => ($row['eta_estimado'] ? $row['eta_estimado'] : $row['eta_programado']),
                'eta_real' => $row['eta_real'],
                'eta_departure_to_client' => ($row['eta_departure_to_client'] && strtotime($row['eta_departure_to_client']) < time())
                    ? ($row['eta_estimado_cliente'] ? $row['eta_estimado_cliente'] : $row['eta_departure_to_client'])
                    : $row['eta_departure_to_client'],
                'eta_departure_to_client_debug' => $row['eta_departure_to_client'],
                'eta_estimado_cliente_debug' => $row['eta_estimado_cliente'],
                'signal' => hasSignal($row['ult_medicion_gps']),
                'ultMedicionGPS' => $row['ult_medicion_gps'],
                'onTime' => strtotime($row['eta_estimado']) > $currentDate,
                'estado' => $estado,
                'total_productos' => $row['total_productos']
            ];

            // Check for duplicates and resolve conflicts
            if (isset($processedTrips[$codigoReparto])) {
                loggear("Duplicado en dashboard descargas $codigoReparto", "ypf_dsb_debug");
                continue;
            }

            //Validaciones para id_dispositivo
            if ($estado == 'predispatch' && in_array($trip['id_dispositivo'], $processedDevices)) {

                foreach($processedTrips as $processedTrip) {
                    if ($processedTrip['estado'] == 'predispatch' && $processedTrip['id_dispositivo'] == $trip['id_dispositivo']) {

                        if (strtotime($trip['eta']) < strtotime($processedTrip['eta'])) {
                            // Find the key to move and unset
                            foreach ($response['predispatch']['trips'] as $key => $temp_trip) {
                                if ($temp_trip['id'] == $processedTrip['id']) {
                                    $keyToPending = $key;
                                    continue;
                                }
                            }

                            if ($keyToPending !== false) {
                                // Extract the trip and restore the totals
                                $tripToMove = $response['predispatch']['trips'][$key];
                                $tripToMove['estado'] = 'pending';
                                $response[$estado]['total']--;
                                $response[$estado]['total_productos'] -= $tripToMove['total_productos'] * M3;
                                $response['pending']['total']++;
                                $response['pending']['total_productos'] += $tripToMove['total_productos'] * M3;
                                if ($tripToMove['onTime']) {
                                    $response[$estado]['onTime']--;
                                    $response['pending']['onTime']++;
                                } else {
                                    $response[$estado]['late']--;
                                    $response['pending']['onTime']++;
                                }
                                unset($response[$processedTrip['estado']]['trips'][$keyToPending]);
                                $response['pending']['trips'][] = $tripToMove;
                            }
                        } else {
                            $estado = 'pending';
                            $trip['estado'] = 'pending';
                        }
                        continue;
                    }
                }
            }

            if (!in_array($trip['id_dispositivo'], $processedDevices)) {
                $processedDevices[] = $trip['id_dispositivo'];
            }

            $processedTrips[$codigoReparto] = $trip;

            $isOnTime = $trip['onTime'];
            if ($isOnTime) {
                $response[$estado]['onTime']++;
            } else {
                $response[$estado]['late']++;
            }

            $response[$estado]['total']++;
            $response[$estado]['total_productos'] += $row['total_productos'] * M3;
            $response[$estado]['trips'][] = $trip;

        }
    }

    if (mysql_num_rows($r_pendientes)) {
        $estado = 'pending';
        $pendientes = [];
        $pendientes_quitar = [];
        $codigos_pendientes_array = [];
        while ($row = mysql_fetch_assoc($r_pendientes)) {
            if (!in_array($row['codigo_reparto'], $codigos_pendientes_array)) $codigos_pendientes_array[] = ltrim($row['codigo_reparto'], '0');
            $pendientes[] = $row;
        }

        $codigos_pendientes = implode("','", $codigos_pendientes_array);

        if ($app != 'aglomeraciones') {
            $pendientes_quitar_string = "   AND r.fecha_inicio = '$fecha_descargas'";
        } else {
            $pendientes_quitar_string = "   AND r.fecha > '$desde_pendiente'
                                            AND r.fecha < '$hasta_pendiente'";
        }

        $pendientes_quitar_string = mysql_fetch_assoc(mysql_query("SELECT GROUP_CONCAT(TRIM(LEADING '0' FROM r.codigo)) AS codigo_reparto
                                                                        FROM repartos r
                                                                        WHERE r.id_usuario = $id_usuario_ypf
                                                                        AND TRIM(LEADING '0' FROM r.codigo) IN ('$codigos_pendientes')
                                                                        AND r.estado IN ('BAJA', 'TERMINADO', 'PRELIMINAR', 'EN CURSO')
                                                                        AND r.tipo_reparto IS NULL
                                                                        AND SUBSTRING(r.inicio_id, 2) = '$terminal_id'
                                                                        $pendientes_quitar_string
                                                                    "));

        if (!empty($pendientes_quitar_string['codigo_reparto'])) {
            $pendientes_quitar = explode(',', $pendientes_quitar_string['codigo_reparto']);
        }

        foreach ($pendientes as $row) {
            if (isBios($row) && $app != 'aglomeraciones') {
                continue;
            }

            if (in_array($trip['id_dispositivo'], $processedDevices)) continue;

            if (in_array(ltrim($row['codigo_reparto'], '0'), $pendientes_quitar)) {
                loggear("Duplicado pendientes en dashboard descargas ".$row['codigo_reparto'], "ypf_dsb_debug");
                continue;
            }
            $trip = [
                'id' => $row['id_reparto'],
                'id_dispositivo' => strval($row['id_dispositivo']),
                'code' => $row['codigo_reparto'],
                'distance' => round($row['distance'] * 111195, 0),
                'distanceCliente' => round($row['distance_cliente'] * 111195, 0),
                'vehicle' => $row['nombre'],
                'client' => $row['razon_social'],
                'scheduled' => $row['eta_programado'],
                'eta' => ($row['eta_estimado'] ? $row['eta_estimado'] : $row['eta_programado']),
                'eta_real' => $row['eta_real'],
                'signal' => hasSignal($row['ult_medicion_gps']),
                'ultMedicionGPS' => $row['ult_medicion_gps'],
                'onTime' => strtotime($row['eta_estimado']) > $currentDate,
                'estado' => $estado,
                'total_productos' => $row['total_productos']
            ];

            $isOnTime = $trip['onTime'];
            if ($isOnTime) {
                $response[$estado]['onTime']++;
            } else {
                $response[$estado]['late']++;
            }

            $response['pending']['total']++;
            $response['pending']['total_productos'] += $row['total_productos'] * M3;
            $response['pending']['trips'][] = $trip;
        }
    }

    //TODO: add Interplantas

    //Otros dispositivos sin reparto en terminal
    foreach ($dispositivos_en_terminal as $id_dispositivo => $dispositivo) {
        if (!in_array($id_dispositivo, $processedDevices) && $isToday) {
            $dispositivo['id_reparto'] = null;
            $dispositivo['eta_estimado'] = $dispositivo['ult_actualizacion'];
            $evento = getEvento($dispositivo);
            $evento = ($evento ? $evento : $dispositivo['ult_actualizacion']);
            $trip = [
                'id' => null,
                'id_dispositivo' => strval($id_dispositivo),
                'code' => null,
                'distance' => 0,
                'vehicle' => $dispositivo['nombre'],
                'client' => null,
                'scheduled' => null,
                'eta' => $row['ult_actualizacion'],
                'eta_real' => $evento,
                'signal' => hasSignal($row['ult_actualizacion']),
                'ultMedicionGPS' => $dispositivo['ult_actualizacion'],
                'onTime' => false,
                'estado' => 'predispatch'
            ];

            $response['predispatch']['trips'][] = $trip;
            $response['predispatch']['total']++;
            $response['predispatch']['late']++;
            $response['predispatch']['no_trip']++;
        }
    }

    return $response;
}

function getCharts($terminal_id, $fecha_desde, $fecha_hasta, $bios = false) {
    $terminales = [];
    $data = [];
    $dataBuild = [];
    $response = [];

    date_default_timezone_set('America/Buenos_Aires');

    $dateTime = new DateTime($fecha_desde);
    $today = new DateTime();
    $isToday = $dateTime->format('Y-m-d') === $today->format('Y-m-d');

    if ($isToday) {
        $currentHour = date('G');
    } else {
        $currentHour = 24;
    }

    $data = getChartInfo($terminal_id, $fecha_desde, $fecha_hasta, $bios);

    if (!empty($data)) {
        foreach ($data as $item) {
            if (!isset($result[$item['id_terminal']])) {
                $dataBuild[$item['id_terminal']] = array_fill(0, $currentHour, 0);
            }
            if (!isset($terminales[$item['id_terminal']])) {
                $terminales[$item['id_terminal']] = $item['terminal'];
            }
        }

        foreach ($data as $item) {
            $hour = date('H', strtotime($item['fecha_hora']));
            $dataBuild[$item['id_terminal']][(int)$hour] = (int)$item['cumplidos'];
        }

        foreach ($dataBuild as $id_terminal => $values) {
            if (!empty($trendChart[$id_terminal])) {
                $trend = '';
                $percent = 0;
            }

            $response = [
                'title' => $terminales[$id_terminal],
                'current' => $values,
                'trend' => $trend,
                'percent' => round(abs($percent), 0),
            ];
        }
    }

    header('Content-Type: application/json');
    ob_start('ob_gzhandler');
    echo json_encode(["data" => $response, "meta" => []], JSON_UNESCAPED_SLASHES);
    ob_end_flush();
    return http_response_code(200);
}

function getWidgets($terminal_id, $fecha_desde, $fecha_hasta, $bios = false){

    global $id_usuario_ypf;
    //global $codigos_gas;
    global $terminal_id_gas;
    global $ypf_gas_user;

    $total_despachos = 0;
    $total_despachos_pendientes = 0;
    $total_despachos_cumplidos = 0;
    $total_productos_pendientes = 0;
    $total_productos_cumplidos = 0;
    $productos = [];

    $summedValues = [];
    $expandedData = [];
    $response = [];

    if ($bios) {
        $query_bios = " AND bios = 1 ";
    } else if ($ypf_gas_user) {
        $query_bios = " AND ypf_gas = 1 ";
    } else {
        $query_bios = " AND bios = 0
                        AND descargas = 1 ";
    }

    if ($fecha_desde && $fecha_hasta) {
        if ($ypf_gas_user) {
            $query_ted = mysql_query("SELECT yt.*
                                FROM ypf_ted yt
                                JOIN (
                                    SELECT id_terminal, MAX(id) AS max_id
                                    FROM ypf_ted
                                    WHERE id_usuario = $id_usuario_ypf
                                    AND id_terminal IN ($terminal_id)
                                    AND fecha_hora BETWEEN '$fecha_desde' AND '$fecha_hasta'
                                    $query_bios
                                    GROUP BY id_terminal
                                ) AS max_row ON yt.id_terminal = max_row.id_terminal
                                                AND yt.id = max_row.max_id");
        } else {
            if ($bios) {
                $tripBios = getTripsBios($terminal_id, $fecha_desde, $fecha_hasta);
                $data = processBiosData($tripBios, true, $fecha_desde);
            } else {
                $data = getTrips($terminal_id, $fecha_desde, $fecha_hasta);
            }

            $pendiente = count($data['pending']['trips']);
            $en_playa = count(array_filter($data['predispatch']['trips'], function ($trip) {
                                    return isset($trip['id']) && $trip['id'] !== null;
                                }));
            $cargando = count($data['load']['trips']);
            $despachado = count($data['departureToClient']['trips']);

            $total_despachos_pendientes = (int)$pendiente + $en_playa + $cargando;
            $total_despachos_cumplidos = (int)$despachado;
            $total_despachos = (int)$total_despachos_pendientes + $total_despachos_cumplidos;

            $total_productos_pendientes = $data['pending']['total_productos']
                                    + $data['predispatch']['total_productos']
                                    + $data['load']['total_productos'];

            $total_productos_cumplidos = $data['departureToClient']['total_productos'];
        }
        if (mysql_num_rows($query_ted)) {
            while ($row = mysql_fetch_assoc($query_ted)) {
                $productos_decoded = json_decode($row['productos'], TRUE);
                if (!empty($productos_decoded)) {
                    foreach ($productos_decoded as $producto) {
                        $codigo = ltrim($producto['codigo_producto'], '0');

                        if (isset($summedValues[$codigo])) {
                            $summedValues[$codigo] += intval($producto['cumplidos']);
                        } else {
                            $summedValues[$codigo] = intval($producto['cumplidos']);
                        }
                        $productos[$codigo] = $producto['producto'];

                        $total_productos_pendientes += $producto['pendientes'];
                        $total_productos_cumplidos += $producto['cumplidos'];
                    }
                }

                $total_despachos += $row['pendientes'];
                $total_despachos += $row['cumplidos'];
                $total_despachos_pendientes += $row['pendientes'];
                $total_despachos_cumplidos += $row['cumplidos'];
            }

            foreach ($summedValues as $code => $value) {
                if ($value > 0) {
                    $expandedData[] = [ 'code' => $code,
                                        'title' => $productos[$code],
                                        'value' => $value * M3
                                    ];
                }
            }
            usort($expandedData, function ($a, $b) {
                return $b['value'] - $a['value'];
            });
        }

        $response_productos = getProductosWidgets($terminal_id, $fecha_desde, $bios);
        foreach ($response_productos as $item) {
            $code = $item['codigo_producto'];
            $title = $item['producto'];
            $value = (int) $item['cumplidos'];
            $finalValue = $value * M3;

            $expandedData[] = [
                'code' => $code,
                'title' => $title,
                'value' => $finalValue
            ];
            $total_productos_pendientes += $item['pendientes'];
            $total_productos_cumplidos += $item['cumplidos'];
        }
        usort($expandedData, function ($a, $b) {
            return $b['value'] - $a['value'];
        });

        $total_productos_pendientes = $total_productos_pendientes * M3;
        $total_productos_cumplidos = $total_productos_cumplidos * M3;


        $response = [
            'status' => [
                'id' => 'status',
                'title' => ($bios ? 'Estado de Recepciones' : 'Estado de Despachos'),
                'value' => [
                    'pendientes' => $total_despachos_pendientes,
                    'cumplidos' => $total_despachos_cumplidos,
                    'total' => $total_despachos,
                ],
            ],
            'volume' => [
                'id' => 'volume',
                'gas' => (in_array($terminal_id, $terminal_id_gas) ? true : false),
                'title' => ($bios ? 'Volumen Recepcionado' : (in_array($terminal_id, $terminal_id_gas) ? 'Peso Despachado' : 'Volumen Despachado')),
                'value' => [
                    'total' => round($total_productos_pendientes + $total_productos_cumplidos, 0),
                    'pendientes' => round($total_productos_pendientes, 0),
                    'cumplidos' => round($total_productos_cumplidos, 0),
                    'expandedData' => $expandedData
                ],
            ],
        ];
    }

    header('Content-Type: application/json');
    ob_start('ob_gzhandler');
    echo json_encode(["data" => $response, "meta" => []], JSON_UNESCAPED_SLASHES);
    ob_end_flush();
    return http_response_code(200);
}

function getChartInfo($terminal_id, $fecha_desde, $fecha_hasta, $bios = false) {
    global $id_usuario_ypf;
    global $ypf_gas_user;

    $data = [];

    if ($bios) {
        $query_bios = " AND yt.bios = 1 ";
    } else if ($ypf_gas_user) {
        $query_bios = " AND yt.ypf_gas = 1 ";
    } else {
        $query_bios = " AND yt.bios = 0
                        AND yt.descargas = 1 ";
    }

    if ($terminal_id) {
        $query_chart = mysql_query("SELECT yt.id, yt.id_terminal, yt.pendientes, yt.cumplidos, pp.descripcion AS terminal, yt.fecha_hora
                        FROM ypf_ted yt
                        LEFT JOIN puntos_partida pp ON pp.id = yt.id_terminal
                        WHERE yt.id_usuario = $id_usuario_ypf
                        AND yt.id_terminal IN ($terminal_id)
                        $query_bios
                        AND fecha_hora BETWEEN '$fecha_desde' AND '$fecha_hasta'");

        if (mysql_num_rows($query_chart)) {
            while ($row = mysql_fetch_assoc($query_chart)) {

                $total_pendientes = $row['pendientes'];
                $total_cumplidos = $row['cumplidos'];
                $total = $total_pendientes + $total_cumplidos;

                $row['pendientes'] = round(($total > 0 ? $total_pendientes * 100 / $total : 0), 0);
                $row['cumplidos'] = round(($total > 0 ? $total_cumplidos * 100 / $total : 0), 0);

                $data[] = $row;
            }
        }
    }

    return $data;
}

function getCardsData($terminal_id, $fecha_desde, $fecha_hasta) {
    global $terminal_id_gas;
    $response = getTrips($terminal_id, $fecha_desde, $fecha_hasta);

    $pending = [
        'title' => 'Pendientes',
        'icon' => 'ri-timer-line',
        'value' => $response['pending']['total'],
        'volume' => $response['pending']['total_productos'],
        'onTime' => $response['pending']['onTime'],
        'late' => $response['pending']['late'],
        'etaTooltip' => 'Horario estimado de llegada a Terminal',
        'gas' => (in_array($terminal_id, $terminal_id_gas) ? true : false),
        'trips' => sortTrips($response['pending']['trips'], 'eta_real')
    ];

    $predispatch = [
        'title' => 'En playa',
        'icon' => 'ri-parking-box-line',
        'value' => $response['predispatch']['total'],
        'volume' => $response['predispatch']['total_productos'],
        'onTime' => $response['predispatch']['onTime'],
        'noTrip' => $response['predispatch']['no_trip'],
        'late' => $response['predispatch']['late'],
        'etaTooltip' => 'Horario real de ingreso a playa. Entre paréntesis te mostramos hace cuanto tiempo llegó',
        'gas' => (in_array($terminal_id, $terminal_id_gas) ? true : false),
        'trips' => sortTrips($response['predispatch']['trips'], 'eta_real')
    ];

    $load = [
        'title' => 'Cargando',
        'icon' => 'ri-arrow-up-line',
        'value' =>  0,
        'volume' => 0,
        'onTime' => '0',
        'late' => '0',
        'etaTooltip' => '',
        'gas' => (in_array($terminal_id, $terminal_id_gas) ? true : false),
        'trips' => []
    ];

    $departureToClient = [
        'title' => 'Despachados',
        'icon' => 'ri-truck-line',
        'value' => $response['departureToClient']['total'],
        'volume' => $response['departureToClient']['total_productos'],
        'onTime' => $response['departureToClient']['onTime'],
        'late' => $response['departureToClient']['late'],
        'etaTooltip' => 'Horario estimado de llegada a primer cliente',
        'gas' => (in_array($terminal_id, $terminal_id_gas) ? true : false),
        'trips' => sortTrips($response['departureToClient']['trips'])
    ];

    $response = [
        "load" => $load,
        "predispatch" => $predispatch,
        "pending" => $pending,
        "departureToClient" => $departureToClient
    ];

    header('Content-Type: application/json');
    ob_start('ob_gzhandler');
    echo json_encode(["data" => $response, "meta" => []], JSON_UNESCAPED_SLASHES);
    ob_end_flush();
    return http_response_code(200);
}

function getUltimaActualizacion() {
    $response = '';

    $query_actualizacion = mysql_query("SELECT fecha_fin FROm procesos_flags WHERE codigo_proceso = 'cron_ted'");

    if (mysql_num_rows($query_actualizacion)) {
        $response = mysql_result($query_actualizacion, 0, 0);
    }
    echo json_encode(["data" => $response, "meta" => []], JSON_UNESCAPED_SLASHES);
    return http_response_code(200);
}

function getEvento($trip) {
    $rango_llegada_planta = (UsuariosParametro::getValorByUsuarioAndParametro(3050, 'DASHBOARD_LLEGADA_PLANTA') ? UsuariosParametro::getValorByUsuarioAndParametro(3050, 'DASHBOARD_LLEGADA_PLANTA') : 24);
    $timestamp = null;

    $query = mysql_query("SELECT time_stamp FROM eventos
                                WHERE id_dispositivo = ".$trip['id_dispositivo']."
                                AND time_stamp BETWEEN
                                    DATE_SUB('".$trip['eta_estimado']."', INTERVAL $rango_llegada_planta HOUR)
                                    AND NOW()
                                AND codigo_tipo_evento = 'LZONA'
                                ORDER BY time_stamp DESC
                                LIMIT 1");
    if (mysql_num_rows($query)) {
        $timestamp = mysql_result($query, 0, 0);
    }
    return $timestamp;
}

function processBiosData($data, $widget = false, $fecha_param = null) {
    date_default_timezone_set('America/Argentina/Buenos_Aires');
    $pendingTrips = [];
    $transitTrips = [];
    $predispatchTrips = [];
    $finishTrips = [];
    $total_productos = ['Pendientes' => 0, 'Cargando' => 0, 'En_playa' => 0, 'Despachado' => 0];
    $onTime = ['Pendientes' => 0, 'Cargando' => 0, 'En_playa' => 0, 'Despachado' => 0];
    $late = ['Pendientes' => 0, 'Cargando' => 0, 'En_playa' => 0, 'Despachado' => 0];
    $currentDate = time();
    $response = [];

    $today = date("Y-m-d");
    $fecha = date("Y-m-d", (strtotime($fecha_param)));
    $isToday = ($today === $fecha);

    foreach ($data['aaData'] as $row) {
        $trip = [];
        $trip['id'] = strval($row['id']);
        $trip['id_dispositivo'] = strval($row['id_dispositivo']);
        $trip['code'] = $row['codigo'] .' - '.$row['producto'];
        $trip['distance'] = $row['distancia_recorrer'] * 1000;
        $trip['vehicle'] = $row['domain'];
        $trip['client'] = ($row['estado'] == 'en_playa')
            ? (isset($row['clientes'][0]['razon_social']) ? $row['clientes'][0]['razon_social'] : null)
            : (isset($row['clientes'][1]['razon_social']) ? $row['clientes'][1]['razon_social'] : null);

        $trip['eta'] = $row['eta_estimado_por_recorrer'];
        $trip['eta_real'] = $trip['real'] =
            ($row['estado'] == 'en_playa')
            ? (isset($row['clientes'][0]['fecha_visitado']) ? $row['clientes'][0]['fecha_visitado'] : null)
            : (isset($row['clientes'][1]['fecha_visitado']) ? $row['clientes'][1]['fecha_visitado'] : null);

        $isOnTime = ($trip['eta'] && strtotime($trip['eta']) > $currentDate);
        $trip['onTime'] = $isOnTime;
        $trip['signal'] = hasSignal($row['posicion_actual']['ult_medicion_gps']);
        $trip['ultMedicionGPS'] = $row['posicion_actual']['ult_medicion_gps'];
        $estado = strtolower($row['estado']);

        switch ($estado) {
            case 'pendiente':
                if (($isToday && $row['fecha_inicio'] == $fecha && $row['fecha'] == $fecha) ||
                        (!$isToday && $row['fecha_inicio'] == $fecha)) {
                    $pendingTrips[] = $trip;
                    $total_productos['Pendientes'] += $row['total_productos'] * M3;
                    $onTime['Pendientes'] += $isOnTime ? 1 : 0;
                    $late['Pendientes'] += $isOnTime ? 0 : 1;
                }
                break;
            case 'despachado':
                if ($isToday) {
                    $transitTrips[] = $trip;
                    $total_productos['Cargando'] += $row['total_productos'] * M3;
                    $onTime['Cargando'] += $isOnTime ? 1 : 0;
                    $late['Cargando'] += $isOnTime ? 0 : 1;
                }
                break;
            case 'en_playa':
            case 'en_terminal':
                if ($isToday) {
                    $predispatchTrips[] = $trip;
                    $total_productos['En_playa'] += $row['total_productos'] * M3;
                    $onTime['En_playa'] += $isOnTime ? 1 : 0;
                    $late['En_playa'] += $isOnTime ? 0 : 1;
                }
                break;
            case 'finalizado':
                if (date("Y-m-d", (strtotime($trip['real']))) == $fecha && $fecha <= $today) {
                    $finishTrips[] = $trip;
                    $total_productos['Despachado'] += $row['total_productos'] * M3;
                    $onTime['Despachado'] += $isOnTime ? 1 : 0;
                    $late['Despachado'] += $isOnTime ? 0 : 1;
                }
                break;

            case 'vencido':
            default:
                if (($isToday && $row['fecha_inicio'] == $fecha && $row['fecha'] == $fecha) ||
                        (!$isToday && $row['fecha_inicio'] == $fecha)) {
                    $pendingTrips[] = $trip;
                    $total_productos['Pendientes'] += $row['total_productos'] * M3;
                    $onTime['Pendientes'] += $isOnTime ? 1 : 0;
                    $late['Pendientes'] += $isOnTime ? 0 : 1;
                }
                break;
        }
    }

    $pending = [
        'title' => 'Pendientes',
        'icon' => 'ri-timer-line',
        'value' => count($pendingTrips),
        'volume' => $total_productos['Pendientes'],
        'onTime' => strval($onTime['Pendientes']),
        'late' => strval($late['Pendientes']),
        'etaTooltip' => 'Horario estimado de llegada a Terminal',
        'trips' => sortTrips($pendingTrips)
    ];

    $transit = [
        'title' => 'En tránsito',
        'icon' => 'ri-arrow-up-line',
        'value' => count($transitTrips),
        'volume' => $total_productos['Cargando'],
        'onTime' => $onTime['Cargando'],
        'late' => $late['Cargando'],
        'etaTooltip' => 'Horario estimado de llegada a terminal',
        'trips' => sortTrips($transitTrips)
    ];

    $predispatch = [
        'title' => 'En playa',
        'icon' => 'ri-parking-box-line',
        'value' => count($predispatchTrips),
        'volume' => $total_productos['En_playa'],
        'onTime' => $onTime['En_playa'],
        'late' => $late['En_playa'],
        'etaTooltip' => 'Horario de ingreso a terminal',
        'trips' => sortTrips($predispatchTrips, 'real')
    ];

    $finish = [
        'title' => 'Finalizados',
        'icon' => 'ri-truck-line',
        'value' => count($finishTrips),
        'volume' => $total_productos['Despachado'],
        'onTime' => $onTime['Despachado'],
        'late' => $late['Despachado'],
        'etaTooltip' => '',
        'trips' => sortTrips($finishTrips)
    ];

    $response = [
        "predispatch" => $predispatch,
        "transit" => $transit,
        "pending" => $pending,
        "finish" => $finish
    ];

    $responseWidget = [
        "pending" => ["total" => count($pendingTrips), "total_productos" => $total_productos['Pendientes'], "onTime" => $onTime['Pendientes'], "late" => $late['Pendientes'], "trips" => sortTrips($pendingTrips)],
        "load" => ["total" => count($transitTrips), "total_productos" => $total_productos['Cargando'], "onTime" => $onTime['Cargando'], "late" => $late['Cargando'], "trips" => sortTrips($transitTrips)],
        "predispatch" => ["total" => count($predispatchTrips), "total_productos" => $total_productos['En_playa'], "onTime" => $onTime['En_playa'], "late" => $late['En_playa'], "trips" => sortTrips($predispatchTrips, 'real')],
        "departureToClient" => ["total" =>  count($finishTrips), "total_productos" =>  $total_productos['Despachado'], "onTime" =>  $onTime['Despachado'], "late" => $late['Despachado'], "trips" => sortTrips($finishTrips)]
    ];

    return ($widget ? $responseWidget :$response);
}
function getCardsBiosData($terminal_id, $fecha_desde, $fecha_hasta) {
    $data = getTripsBios($terminal_id, $fecha_desde, $fecha_hasta);
    $response = processBiosData($data, false, $fecha_desde);

    header('Content-Type: application/json');
    ob_start('ob_gzhandler');
    echo json_encode(["data" => $response, "meta" => []], JSON_UNESCAPED_SLASHES);
    ob_end_flush();
    return http_response_code(200);
}

function getTripsBios($terminal_id, $fecha_desde, $fecha_hasta) {
    global $id_usuario_ypf;
    $response = [];

    $query = mysql_query("SELECT id FROM clientes
                                                WHERE id_usuario = $id_usuario_ypf
                                                AND habilitado = 1
                                                AND codigo = (SELECT codigo_erp FROM puntos_partida WHERE id = $terminal_id)
                                                LIMIT 1");


    if(mysql_num_rows($query) > 0){
        $id_cliente_bios_recepcion = mysql_result($query, 0, 0);
        $response = getRepartosBios($id_usuario_ypf, false, $fecha_desde, $fecha_hasta, false, $id_cliente_bios_recepcion);
    }else{
        loggear("No se encontró un cliente-terminal bios", "ypf_dsb_debug");
    }

    return $response;
}

function sortTrips($trips, $field = 'eta', $order = 'ASC') {
    usort($trips, function($a, $b) use ($field, $order) {
        if (!isset($a[$field]) || !isset($b[$field])) {
            return 0;
        }

        $aTime = strtotime($a[$field]);
        $bTime = strtotime($b[$field]);

        if ($aTime == $bTime) {
            return 0;
        }

        if ($order === 'ASC') {
            return ($aTime < $bTime) ? -1 : 1;
        } else {
            return ($aTime > $bTime) ? -1 : 1;
        }
    });

    return $trips;
}

function mapTrips($trips) { //No debería estar en uso
    $transformedTrips = [];

    foreach ($trips as $trip) {
        $transformedTrips[] = [
            'id'       => $trip['id'],
            'code'     => $trip['code'],
            'distance' => $trip['distance'] * 111195,
            'vehicle'  => $trip['vehicle'],
            'client'   => $trip['client'],
            'eta'      => $trip['eta'],
            'eta_real' => $trip['eta_real'],
            'onTime'   => false,
            'signal'   => $trip['signal'],
            'ultMedicionGPS'   => $trip['ultMedicionGPS'],
        ];
    }

    return $transformedTrips;
}

function exportExcelByTerminalId($terminal_id, $fecha_desde, $fecha_hasta, $type, $esBios) {
    $today = date("d-m-Y");
    $currentTime = date("H:i");
    $terminal = mysql_result(mysql_query("SELECT CONCAT(descripcion, ' - ', codigo_erp) FROM puntos_partida WHERE id = $terminal_id"), 0, 0);
    $dataBuild = [];

    $estado = '';
    $titulo = '';
    $headers_type = '';

    switch ($type) {
        case 'pending':
            $estado = 'pendiente';
            $titulo = 'Pendientes';
            if (!$esBios) {
                $headers_type = "<td class='Header' style='width:180pt'> "._("Cliente")."</td>";
            }
            $headers_type .= "<td class='Header' style='width:180pt'> "._("Distancia (km)")."</td>";
            if (!$esBios) {
                $headers_type .= "<td class='Header' style='width:180pt'> "._("Programado")."</td>";
            }
            $headers_type .= "<td class='Header' style='width:180pt'> "._("ETA")."</td>";
            break;

        case 'predispatch':
            $estado = ($esBios ? 'en_terminal' : 'en_playa');
            $titulo = 'En playa';
            if (!$esBios) {
                $headers_type = "<td class='Header' style='width:180pt'> "._("Cliente")."</td>";
            }
            $headers_type .= "<td class='Header' style='width:180pt'> "._("Llegada")."</td>";
            break;

        case 'load':
            $estado = 'cargando';
            $titulo = 'Cargando';
            $headers_type = "<td class='Header' style='width:180pt'> "._("Cliente")."</td>";
            break;

        //Bios exportable sin card
        case 'provider':
            $estado = 'proveedor';
            $titulo = 'En proveedor';
            $headers_type = "<td class='Header' style='width:180pt'> "._("Proveedor Código")."</td>";
            $headers_type .= "<td class='Header' style='width:180pt'> "._("Proveedor")."</td>";
            $headers_type .= "<td class='Header' style='width:180pt'> "._("Llegada")."</td>";
            $headers_type .= "<td class='Header' style='width:180pt'> "._("ETA")."</td>";
            break;

        case 'transit':
            $estado = 'despachado';
            $titulo = 'En tránsito';
            $headers_type .= "<td class='Header' style='width:180pt'> "._("Distancia")."</td>";
            $headers_type .= "<td class='Header' style='width:180pt'> "._("ETA")."</td>";
            break;

        case 'departureToClient':
            $estado = 'despachado';
            $titulo = 'Despachados';
            $headers_type = "<td class='Header' style='width:180pt'> "._("Cliente")."</td>";
            $headers_type .= "<td class='Header' style='width:180pt'> "._("Fecha de carga")."</td>";
            $headers_type .= "<td class='Header' style='width:180pt'> "._("ETA")."</td>";
            break;

        case 'finish':
            $estado = 'finalizado';
            $titulo = 'Finalizados';
            break;

    }

    if ($esBios) {
        $dataBuild = getRepartosExportarBios($terminal_id, $fecha_desde, $fecha_hasta, $estado);
    } else {
        $dataBuild = getRepartosExportar($terminal_id, $fecha_desde, $fecha_hasta, $type);
    }

    $dataHtml = getHeader($estado);
    $dataHtml .= "<tr></tr>
        <tr>
            <td style='width:200pt'> "._("Viajes $titulo para $terminal")." </td>
            <td style='width:200pt'> "._("Fecha: ")." </td><td>$today</td>
            <td style='width:200pt'> "._("Hora actualización: ")."</td><td>$currentTime</td>
        </tr>
        <tr></tr>
        <tr>
            <td></td>
            <td class='Header' style='width:100pt'> "._("Viaje")." </td>
            <td class='Header' style='width:180pt'> "._("Vehículo")."</td>
            $headers_type;
        </tr>";

    foreach($dataBuild as $data) {
        $dataHtml .= "<tr>
            <td></td>
            <td> ".$data['code']." </td>
            <td> ".$data['vehicle']." </td>";

            switch ($type) {
                case 'pending':
                    if (!$esBios) {
                        $dataHtml .= "  <td> ".$data['client']." </td>";
                    }
                    $dataHtml .= "      <td> ".round($data['distance'] / 1000, 2)." </td>";
                    if (!$esBios) {
                        $dataHtml .= "      <td> ".$data['scheduled']." </td>";
                    }
                    $dataHtml .= "      <td> ".$data['eta']." </td>";
                    break;

                case 'predispatch':
                    if (!$esBios) {
                        $dataHtml .= "  <td> ".$data['client']." </td>";
                    }
                    $dataHtml .= "  <td> ".$data['eta_real']." </td>";
                    break;

                case 'load':
                    $dataHtml .= "<td> ".$data['client']." </td>";
                    break;

                case 'provider':
                    $dataHtml .= "<td> ".$data['proveedor_codigo']." </td>";
                    $dataHtml .= "<td> ".$data['proveedor']." </td>";
                    $dataHtml .= "<td> ".$data['eta_real_proveedor']." </td>";
                    $dataHtml .= "<td> ".$data['eta']." </td>";
                    break;

                case 'transit':
                    $dataHtml .= "  <td> ".round($data['distance'])." </td>
                                    <td> ".$data['eta']." </td>";
                    break;

                case 'departureToClient':
                    $dataHtml .= "  <td> ".$data['client']." </td>
                                    <td> ".$data['eta_real']." </td>
                                    <td> ".$data['eta']." </td>";
                    break;

                case 'finish':
                    break;

            }
        $dataHtml .= "</tr>";
    }

    $dataHtml .= '</table></body></html>';

    echo $dataHtml;
}

function getRepartosExportar($terminal_id, $fecha_desde, $fecha_hasta, $type) {
    $data = getTrips($terminal_id, $fecha_desde, $fecha_hasta);
    $trips = $data[$type]['trips'];
    return $trips;
}

function getRepartosExportarBios($terminal_id, $fecha_desde, $fecha_hasta, $estado) {
    date_default_timezone_set('America/Argentina/Buenos_Aires');
    $currentDate = time();
    $margin_of_error = 2; //km
    $data = getTripsBios($terminal_id, $fecha_desde, $fecha_hasta);

    $trips = [];
    foreach ($data['aaData'] as $row) {
        //https://quadminds.atlassian.net/browse/QS-11425
        $distancia_faltante = $row['distancia_total'] - $row['distancia_primer_cliente'];
        $en_proveedor = abs($distancia_faltante - $row['distancia_recorrer']) <= $margin_of_error;
        if ($estado != 'proveedor' && ($estado != strtolower($row['estado']))) continue;
        if ($estado == 'proveedor' && !$en_proveedor) continue;

        $trip = [];
        $trip['id'] = strval($row['id']);
        $trip['id_dispositivo'] = $row['id_dispositivo'];
        $trip['code'] = $row['codigo'];
        $trip['distance'] = $row['distancia_recorrer'];
        $trip['vehicle'] = $row['domain'];
        $trip['client'] = isset($row['clientes'][1]['razon_social']) ? $row['clientes'][1]['razon_social'] : null;
        $trip['proveedor_codigo'] = isset($row['clientes'][0]['codigo']) ? $row['clientes'][0]['codigo'] : null;
        $trip['proveedor'] = isset($row['clientes'][0]['razon_social']) ? $row['clientes'][0]['razon_social'] : null;
        $trip['eta'] = $row['eta_estimado_por_recorrer'];
        $trip['eta_real_proveedor'] = $trip['real'] = isset($row['clientes'][0]['fecha_visitado']) ? $row['clientes'][0]['fecha_visitado'] : null;
        $trip['eta_real'] = $trip['real'] = isset($row['clientes'][1]['fecha_visitado']) ? $row['clientes'][1]['fecha_visitado'] : null;
        $isOnTime = ($trip['eta'] && strtotime($trip['eta']) > $currentDate);
        $trip['onTime'] = $isOnTime;
        $trip['en_proveedor'] = $en_proveedor;
        $trips[] = $trip;
    }

    return sortTrips($trips);
}

function getHeader($titulo = false) {
    global $id_usuario_ypf;

    $r_usuario = mysql_query("SELECT locale FROM usuarios WHERE id = $id_usuario_ypf");

    $user_locale = mysql_result($r_usuario, 0, 'locale');

    if (!$user_locale) {
        $user_locale = "es_AR.UTF-8";
    }

    putenv("LANG=" . $user_locale);
    putenv("LANGUAGE=" . $user_locale);
    $domain = "messages";
    bindtextdomain($domain, "locale/nocache");
    bindtextdomain($domain, "locale");
    bind_textdomain_codeset($domain, 'UTF-8');
    textdomain($domain);

    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=ISO-8859-1');
    $filename = 'qmt_report_descargas'.($titulo ? '_'.$titulo : '').'.xls';
    header('Content-Disposition: attachment; filename=' . $filename);
    header('Cache-Control: max-age=0');

    $dataHtml = '<html xmlns:x="urn:schemas-microsoft-com:office:excel">
                <head>
                <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
                <style>
                .logo{
                    width: 230px;
                    height: 47px;
                }
                .Header {
                    text-align: center;
                    padding:0px;
                    mso-ignore:padding;
                    color:windowtext;
                    font-size:11.0pt;
                    font-weight:700;
                    font-style:normal;
                    text-decoration:none;
                    font-family:"";
                    mso-generic-font-family:auto;
                    mso-font-charset:0;
                    mso-number-format:Standard;
                    vertical-align:middle;
                    border-top:.5pt solid #808080;
                    border-right:none;
                    border-bottom:.5pt solid #808080;
                    border-left:none;
                    background:#538dd5;
                    mso-pattern:black none;
                    white-space:nowrap;
                }
                .Num {
                    mso-number-format:"0";
                },
                .Text {
                    mso-number-format:"\@";
                }
                </style>
                </head>
                <body>
                <table id="listado">
                <tr>
                </tr>
                ';

    return $dataHtml;
}


function hasSignal($ult_medicion_gps) {
    date_default_timezone_set('America/Argentina/Buenos_Aires');
    $targetTime = new DateTime($ult_medicion_gps);
    $now = new DateTime();
    $targetTime->modify("+24 hours");
    return ($now < $targetTime ? true : false); //señal GPS
}

function getProductosWidgets($terminal_id, $fecha_param, $bios = false) {
    date_default_timezone_set('America/Argentina/Buenos_Aires');
    global $id_usuario_ypf;
    $snap_productos_response = [];

    $today = date("Y-m-d");
    $fecha = date("Y-m-d", strtotime($fecha_param));
    $isToday = ($today === $fecha);

    if ($isToday) {
        $where = " AND r.fecha = '$fecha' AND r.fecha_inicio = '$fecha' ";
    } else {
        $where = " AND r.fecha_inicio = '$fecha' ";
    }

    if ($bios) {
        $query_pendientes = " WHEN r.estado != 'TERMINADO' THEN pd.cantidad ELSE 0";
        $query_cumplidos = " WHEN r.estado = 'TERMINADO' THEN pd.cantidad ELSE 0";
        $query_tipo_reparto = " AND (r.codigo_tipo_reparto = 'BIOCOMBUSTIBLES' OR r.tipo_reparto = 5) ";
        $query_not_in = "'BAJA'";
    } else {
        $query_pendientes = " WHEN DATE_FORMAT(r.fecha_presentacion, '%H:%i:%s') = '00:00:00' THEN pd.cantidad ELSE 0";
        $query_cumplidos = " WHEN DATE_FORMAT(r.fecha_presentacion, '%H:%i:%s') != '00:00:00' THEN pd.cantidad ELSE 0";
        $query_tipo_reparto = " AND r.tipo_reparto IS NULL ";
        $query_not_in = "'BAJA', 'TERMINADO'";
    }
    $query_productos = mysql_query("SELECT
                                    SUBSTRING(r.inicio_id, 2) AS id_terminal,
                                    RIGHT(pd.codigo_producto, 6) AS codigo_producto,
                                    TRIM(mp.descripcion) AS producto,
                                    CAST(SUM(
                                        CASE
                                            $query_pendientes
                                        END
                                    ) AS UNSIGNED) AS pendientes,
                                    CAST(SUM(
                                        CASE
                                            $query_cumplidos
                                        END
                                    ) AS UNSIGNED) AS cumplidos
                        FROM repartos r
                            LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id
                            INNER JOIN
                                clientes_repartos cr ON cr.id_reparto = r.id
                            INNER JOIN
                                pedidos p ON p.id_cliente_reparto = cr.id
                            INNER JOIN
                                pedidos_detalles pd ON pd.id_pedido = p.id
                            INNER JOIN
                                maestro_productos mp ON mp.id_usuario = $id_usuario_ypf AND mp.codigo = RIGHT(pd.codigo_producto, 6)
                            WHERE r.id_usuario = $id_usuario_ypf
                            AND ((yds.dropped = 0 OR yds.dropped IS NULL) AND yds.dropped_at IS NULL)
                            AND SUBSTRING(r.inicio_id, 2) = '$terminal_id'
                            $where
                            $query_tipo_reparto
                            AND r.estado NOT IN ($query_not_in)
                            GROUP BY
                                r.inicio_id,
                                pd.codigo_producto");
error_log(SELECT
                                    SUBSTRING(r.inicio_id, 2) AS id_terminal,
                                    RIGHT(pd.codigo_producto, 6) AS codigo_producto,
                                    TRIM(mp.descripcion) AS producto,
                                    CAST(SUM(
                                        CASE
                                            $query_pendientes
                                        END
                                    ) AS UNSIGNED) AS pendientes,
                                    CAST(SUM(
                                        CASE
                                            $query_cumplidos
                                        END
                                    ) AS UNSIGNED) AS cumplidos
                        FROM repartos r
                            LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id
                            INNER JOIN
                                clientes_repartos cr ON cr.id_reparto = r.id
                            INNER JOIN
                                pedidos p ON p.id_cliente_reparto = cr.id
                            INNER JOIN
                                pedidos_detalles pd ON pd.id_pedido = p.id
                            INNER JOIN
                                maestro_productos mp ON mp.id_usuario = $id_usuario_ypf AND mp.codigo = RIGHT(pd.codigo_producto, 6)
                            WHERE r.id_usuario = $id_usuario_ypf
                            AND ((yds.dropped = 0 OR yds.dropped IS NULL) AND yds.dropped_at IS NULL)
                            AND SUBSTRING(r.inicio_id, 2) = '$terminal_id'
                            $where
                            $query_tipo_reparto
                            AND r.estado NOT IN ($query_not_in)
                            GROUP BY
                                r.inicio_id,
                                pd.codigo_producto;
    if (mysql_num_rows($query_productos)) {
        while ($row = mysql_fetch_assoc($query_productos)) {
            $snap_productos_response[] = $row;
        }
    }

    return $snap_productos_response;
}

function isBios($reparto) {
    if ($reparto['tipo_reparto'] == 5 || $reparto['codigo_tipo_reparto'] == 'BIOCOMBUSTIBLES') {
        return true;
    }
}

function normalizeCodigoReparto($codigo) {
    return ltrim($codigo, '0');
}

function getSqlRepartos($terminal_id, $where = '', $fecha_desde = '', $fecha_hasta = '') {
    global $id_usuario_ypf;
    global $ypf_gas_user;

    if($ypf_gas_user){
        $id_campo_nomenclatura = mysql_result(mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = '17242' AND nombre = 'NOMENCLATURA_GAS'"), 0, 0);
        $razon_social = " COALESCE(ccd.valor, c.razon_social) AS razon_social, ";
        $joinNomenclatura = " LEFT JOIN clientes_campos_detalles ccd ON c.id = ccd.id_cliente AND ccd.id_campo = $id_campo_nomenclatura ";
    }else{
        $id_campo_nomenclatura = '';
        $razon_social = "c.razon_social, ";
        $joinNomenclatura = "";
    }

    $sql = "SELECT
            r.id AS id_reparto,
            r.fecha_presentacion,
            r.estado,
            r.codigo AS codigo_reparto,
            r.id_dispositivo,
            r.tipo_reparto,
            r.codigo_tipo_reparto,
            m.nombre,
            $razon_social
            MIN(cr.realizado) AS realizado,
            yte1.estimated AS eta_programado,
            yetc.eta AS eta_estimado,
            yte1.real AS eta_real,
            yte4.estimated AS eta_departure_to_client,
            yecdc.eta AS eta_estimado_cliente,
            d.ult_medicion_gps,
            CAST(SUM(pd.cantidad) AS UNSIGNED) AS total_productos,
            ST_Distance (
                ST_GEOMFROMTEXT (
                    CONCAT ('POINT(', pp.latitud, ' ', pp.longitud, ')')
                ),
                ST_GEOMFROMTEXT (
                    CONCAT ('POINT(', d.latitud_gps, ' ', d.longitud_gps, ')')
                )
            ) AS distance,
            ST_Distance (
                ST_GEOMFROMTEXT (
                    CONCAT ('POINT(', pp.latitud, ' ', pp.longitud, ')')
                ),
                ST_GEOMFROMTEXT (
                    CONCAT ('POINT(', c.latitud, ' ', c.longitud, ')')
                )
            ) AS distance_cliente
        FROM
            repartos r
            LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id
            LEFT JOIN (
                SELECT *
                FROM clientes_repartos cr1
                WHERE cr1.orden_visita = (
                    SELECT MIN(cr2.orden_visita)
                    FROM clientes_repartos cr2
                    WHERE cr2.id_reparto = cr1.id_reparto
                )
            ) cr ON r.id = cr.id_reparto
            LEFT JOIN dispositivos d ON r.id_dispositivo = d.id
            LEFT JOIN maquinas m ON d.id_maquina = m.id
            LEFT JOIN clientes c ON cr.id_cliente = c.id
            LEFT JOIN ypf_terminal_event yte1 ON yte1.delivery_id = r.id
                AND yte1.type_id = 1
            LEFT JOIN ypf_terminal_event yte4 ON yte4.delivery_id = r.id
                AND yte4.type_id = 4
            LEFT JOIN ypf_client_delivery_event ycde ON ycde.client_delivery_id = cr.id AND ycde.type_id = 5
            LEFT JOIN ypf_eta_client_delivery_cache yecdc ON yecdc.client_delivery_event_id = ycde.id
            LEFT JOIN ypf_eta_terminal_cache yetc ON yetc.terminal_event_id = yte1.id
            LEFT JOIN puntos_partida pp ON pp.id = SUBSTRING(r.inicio_id, 2)
            LEFT JOIN pedidos p ON p.id_cliente_reparto = cr.id
            LEFT JOIN pedidos_detalles pd ON pd.id_pedido = p.id
            LEFT JOIN maestro_productos mp ON mp.id_usuario = $id_usuario_ypf
            AND mp.codigo = RIGHT (pd.codigo_producto, 6)
            $joinNomenclatura
        WHERE
            r.id_usuario = $id_usuario_ypf
            AND SUBSTRING(r.inicio_id, 2) = '$terminal_id'
            AND ((yds.dropped = 0 OR yds.dropped IS NULL) AND yds.dropped_at IS NULL)
            AND NOT (r.id_dispositivo = 0 AND r.transportista IS NULL)";

    if ($where) {
        $sql .= $where;
        $sql .= " AND r.estado NOT IN ('BAJA', 'TERMINADO') ";
    } else if ($fecha_desde && !$fecha_hasta) { //QS-13401 Pendientes de la fecha x param
        $sql .= "   AND r.fecha_inicio = '$fecha_desde'
                    AND r.estado NOT IN ('BAJA', 'TERMINADO', 'PRELIMINAR', 'EN CURSO')
                    AND TIME(r.fecha_presentacion) = '00:00:00'";
    } else if ($fecha_desde && $fecha_hasta) {
        $sql .= "   AND r.fecha > '$fecha_desde'
                    AND r.fecha < '$fecha_hasta'";
        $sql .= "   AND r.estado NOT IN ('BAJA', 'TERMINADO', 'PRELIMINAR', 'EN CURSO')
                    AND TIME(r.fecha_presentacion) = '00:00:00'";
    } else {
        $sql .= " AND r.fecha = NOW() ";
    }

    $sql .= "   GROUP BY r.id
                ORDER BY distance ASC";

    return $sql;
}

function getEnPlaya($id_deposito, $fecha) {
    global $id_usuario_ypf;

    $response = [];
    $dispositivos = [];
    $r_maquinas = mysql_query("SELECT d.id AS id_dispositivo, m.nombre, d.latitud_gps, d.longitud_gps, d.id_maquina, d.ult_medicion_gps AS ult_actualizacion,
                        r.id, r.tipo_reparto, r.codigo_tipo_reparto
                    FROM maquinas m, dispositivos d
                    LEFT JOIN repartos r ON r.id = (
                        SELECT r2.id
                        FROM repartos r2
                        WHERE r2.id_dispositivo = d.id
                        AND r2.id_usuario = '$id_usuario_ypf'
                        AND r2.fecha = '$fecha'
                        AND r2.id IS NOT NULL
                        AND r2.estado NOT IN ('BAJA', 'TERMINADO')
                        ORDER BY r2.id DESC
                        LIMIT 1
                    )
                    WHERE d.id_maquina = m.id
                    AND m.id_usuario = $id_usuario_ypf
                    AND d.codigo_tipo_dispositivo = 'AVL-TRAX'");

    while($row = mysql_fetch_assoc($r_maquinas)) {
        if (isBios($row)) continue;
        $dispositivos[] = $row;
    }

    $r_punto_partida = mysql_query("SELECT pp.codigo_erp, pp.radio, pp.latitud, pp.longitud, z.id AS id_zona
                                        FROM puntos_partida pp
                                        LEFT JOIN (
                                            SELECT z.*
                                            FROM zonas z
                                            JOIN tipo_zonas tz ON z.id_tipo_zona = tz.id AND tz.codigo = 'DEPOSITO'
                                            WHERE z.id_usuario = $id_usuario_ypf
                                        ) z ON CAST(pp.codigo_erp AS UNSIGNED) = CAST(z.codigo AS UNSIGNED)
                                        LEFT JOIN tipo_zonas tz ON z.id_tipo_zona = tz.id AND tz.codigo = 'DEPOSITO'
                                        WHERE pp.id_usuario = $id_usuario_ypf
                                        AND pp.id =  $id_deposito
                                        LIMIT 1");
    $punto_partida = mysql_fetch_assoc($r_punto_partida);

    if (isset($punto_partida)) {
        $distancia_partida = $punto_partida['radio'];
        $id_zona = $punto_partida['id_zona'];

        foreach($dispositivos as $dispositivo) {
            $lat = $dispositivo['latitud_gps'];
            $long = $dispositivo['longitud_gps'];

            if ($id_zona > 0) {
                if (enZona($dispositivo['latitud_gps'], $dispositivo['longitud_gps'], $id_zona)) {
                    $response[] = $dispositivo;
                }
            } else {
                $distancia = distance($lat, $long, $punto_partida['latitud'], $punto_partida['longitud']);
                if ($distancia < $distancia_partida) {
                    $response[] = $dispositivo;
                }
            }
        }
    }

    return $response;
}

function enZona($latitude, $longitude, $id_zona) {
    global $cachedPolygons;

    if (!isset($cachedPolygons[$id_zona])) {
        $polygon = [];
        $r_poligono = mysql_query("SELECT latitud, longitud
                                FROM zona_detalles
                                WHERE id_zona = '".$id_zona."'");

        if (mysql_num_rows($r_poligono)) {
            while($row_poligono = mysql_fetch_array($r_poligono, MYSQL_ASSOC)) {
                $latitud = round($row_poligono["latitud"], 6);
                $longitud = round($row_poligono["longitud"], 6);
                $polygon[] = $latitud." ".$longitud;
            }
            $polygon[] = $polygon[0];

            $cachedPolygons[$id_zona] = $polygon;
        } else {
            $cachedPolygons[$id_zona] = [];
        }
    }

    $polygon = $cachedPolygons[$id_zona];

    if (!empty($polygon)) {
        $pointLocation = new pointLocation();
        $point = "$latitude $longitude";
        if ($pointLocation->pointInPolygon($point, $polygon)) {
            return true;
        }
    }

    return false;
}