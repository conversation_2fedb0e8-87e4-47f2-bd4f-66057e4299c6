<?php
//error_reporting(E_ALL);
//ini_set('display_errors', '1');
include_once('/var/www/saas/util_db.php');
include_once('/var/www/saas/util_session.php');
include_once('/var/www/saas/common_functions.php');
include_once('/var/www/saas/cron_functions.php');
include_once('/var/www/saas/falcon/cors.php');
include_once('/var/www/saas/ypf/biocombustibles/functions_ajax.php');
include_once('util_dsb.php');

manejar_sesion();
define('M3', 0.001);
$id_usuario_ypf = 3050;
$id_usuario = $_SESSION['usuario_id'];

$ypf_terminals = get_parametro_usuario($_SESSION['usuario_id'], 'TED_FILTRO_TERMINALES');
$ypf_gas_user = get_parametro_usuario($_SESSION['usuario_id'], 'TED_YPF_GAS');

$ypf_user = get_parametro_usuario($_SESSION['usuario_id'], 'YPF_USER');

if ((isset($_SESSION['usuario_id']) && $_SESSION['usuario_id']) || (!isset($_COOKIE['PHPSESSID']))) {
    if (!$ypf_user) {
        http_response_code(403);
        die();
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = _get('search');
    $terminal_id = _get('terminalId');
    $fecha = _get('date');
    $fecha_desde = $fecha . ' 00:00:00';
    $fecha_hasta = $fecha . ' 23:59:59';
    $type = _get('type');
    $esBios = _get('reception');

    switch ($action) {
        case 'terminales':
            getTerminales($id_usuario);
            break;

        case 'widgetData':
            getWidgets($terminal_id, $fecha_desde, $fecha_hasta);
            break;

        case 'widgetBiosData':
            getWidgets($terminal_id, $fecha_desde, $fecha_hasta, true);
            break;

        case 'chartData':
            getCharts($terminal_id, $fecha_desde, $fecha_hasta);
            break;

        case 'chartBiosData':
            getCharts($terminal_id, $fecha_desde, $fecha_hasta, true);
            break;

        case 'cardsData':
            getCardsData($terminal_id, $fecha_desde, $fecha_hasta);
            break;

        case 'cardsBiosData':
            getCardsBiosData($terminal_id, $fecha_desde, $fecha_hasta);
            break;

        case 'ultimaActualizacion':
            getUltimaActualizacion();
            break;

        case 'exportExcelByTerminalId':
            exportExcelByTerminalId($terminal_id, $fecha_desde, $fecha_hasta, $type, $esBios);
            break;

        default:
            http_response_code(403);
            exit();
    }
}