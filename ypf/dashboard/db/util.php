<?

/*ini_set('display_errors',1);
error_reporting(E_ALL);*/

define ('ROOT', '/var/www/saas/');

include_once(ROOT . '/util_db.php');
include_once(ROOT . '/util_session.php');
include_once ROOT . '/google_ws_signing.php';
include_once(ROOT . '/ypf/dashboard/db/demo_data.php');
include_once(ROOT . '/ypf/dashboard/db/demo_data_pendientes.php');
require_once(ROOT . '/classes/DomainObjects/UsuariosParametro.php');

manejar_sesion();
$usuario_id = $_SESSION['usuario_id'];
$filtroGasParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_GAS') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_GAS') : false);
$filtroTroncalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'YPF_TRONCAL') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'YPF_TRONCAL') : false);
$filtroAeroParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_AERO') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_AERO') : false);
session_write_close();

$isDemoUser = $usuario_id === '3644';
$ypfAdmin = get_usuario_has_rol($usuario_id, 'YPF_ADMIN');
$permisos = get_permisos($usuario_id, 'dashboard');
$ypfSales = in_array('CLIENTES', $permisos);
if($isDemoUser) {
    $usuario_id = 3050;
}
$id_usuario_ypf = 3050;

$troncalSQL = "";
if ($filtroTroncalParametro) {
    $id_cliente_campo_troncal = mysql_result(mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = $id_usuario_ypf AND nombre = '".$filtroTroncalParametro."' LIMIT 1"), 0);
    $troncalSQL = " INNER JOIN clientes_campos_detalles ccdt ON c.id = ccdt.id_cliente AND ccdt.id_campo = $id_cliente_campo_troncal AND ccdt.valor = 1";
}

$aeroSQL = "";
if ($filtroAeroParametro) {
    $aeroSQL = " AND c.codigo_tipo_cliente = 'YPF_AERO' ";
}

$operacion = _get_post('operacion');
rastrear('ypf/dashboard/db/util.php',$operacion, $usuario_id);
$ahora = date('Y-m-d H:i:s');

$fecha_por_post = null;

if(_post('fecha')) {
    $hoy = _post('fecha');
    $fecha_por_post = 1;
} else {
    $hoy = date('Y-m-d');
}

$manana = date("Y-m-d", strtotime($hoy." +1 day"));

switch($operacion) {
    case "assingMaq": {
        $id_dispositivo = _post('id_dispositivo');
        $id_reparto = _post('id_reparto');

        $r_reparto_existe = mysql_query("SELECT id_dispositivo, codigo
                                         FROM repartos
                                         WHERE id_usuario = '$id_usuario_ypf'
                                         AND id = '$id_reparto'");
        if (!mysql_num_rows($r_reparto_existe)) break;

        $reparto_existe = mysql_fetch_assoc($r_reparto_existe);

        $id_dispositivo_old = $reparto_existe['id_dispositivo'];
        $codigo_reparto = $reparto_existe['codigo'];
        $id_usuario = $id_usuario_ypf;

        if ($id_dispositivo_old == $id_dispositivo) break;

        // Cambiar el dispo asignado
        mysql_query("UPDATE repartos SET id_dispositivo = '$id_dispositivo' WHERE id = '$id_reparto'");

        // Generar un evento de cambio de vehiculo

        if ($id_dispositivo_old == 0) {
            // No tenia dispositivo asignado
            $new = mysql_fetch_assoc(mysql_query("SELECT m.nombre, d.latitud_gps as lat, longitud_gps as lon
                                              FROM dispositivos d INNER JOIN maquinas m ON m.id = d.id_maquina
                                              WHERE d.id = '$id_dispositivo'"));
            generar_evento($ahora, 'CAMBIO_DISP', "Reparto: $codigo_reparto - Asignado: $new[nombre]", $id_dispositivo, $new['lat'], $new['lon']);
        } else {
            // Es un cambio de vehiculo
            $old = mysql_fetch_assoc(mysql_query("SELECT m.nombre
                                              FROM dispositivos d INNER JOIN maquinas m ON m.id = d.id_maquina
                                              WHERE d.id = '$id_dispositivo_old'"));
            $new = mysql_fetch_assoc(mysql_query("SELECT m.nombre, d.latitud_gps as lat, longitud_gps as lon
                                              FROM dispositivos d INNER JOIN maquinas m ON m.id = d.id_maquina
                                              WHERE d.id = '$id_dispositivo'"));
            generar_evento($ahora, 'CAMBIO_DISP', "Reparto: $codigo_reparto - Cambio asignacion: $old[nombre] -> $new[nombre]", $id_dispositivo, $new['lat'], $new['lon']);
        }

        break;
    }
    case 'assignDepo': {
        $id_reparto = _post('id_reparto');
        $id_deposito = _post('id_deposito');

        $r_reparto_existe = mysql_query("SELECT inicio_id, codigo
                                     FROM repartos
                                     WHERE id_usuario = '$id_usuario_ypf'
                                     AND id = '$id_reparto'");
        if (!mysql_num_rows($r_reparto_existe)) break;

        $reparto_existe = mysql_fetch_assoc($r_reparto_existe);

        $id_deposito_old = $reparto_existe['inicio_id'];
        $codigo_reparto = $reparto_existe['codigo'];
        $id_usuario = $id_usuario_ypf;

        if ($id_deposito_old == $id_deposito) break;

        mysql_query("UPDATE repartos SET inicio_id = $id_deposito WHERE id = $id_reparto");

        break;
    }
    case "getOrderData": {
        $orders = [];
        $filtroDeTerminalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TERMINAL') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TERMINAL') : false);
        $filtroDeIdTerminalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_ID_TERMINAL') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_ID_TERMINAL') : false);
        $filtroTipoClienteParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TIPO_CLIENTE') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TIPO_CLIENTE') : false);
        if ($filtroGasParametro) {
            $id_campo_nomenclatura = mysql_result(mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = 17242 AND nombre = 'NOMENCLATURA_GAS'"), 0, 0);
            $selectProductos = ",mp.descripcion as producto";
            $joinProductos = "LEFT JOIN pedidos p ON p.id_cliente_reparto = cr.id
                              LEFT JOIN pedidos_detalles pd ON pd.id_pedido = p.id 
                              LEFT JOIN maestro_productos mp ON (mp.codigo = pd.codigo_producto AND mp.id_usuario = 3050)";

        } else {
            $id_campo_nomenclatura = '';
            $selectProductos = "";
            $joinProductos = "";
        }

        if($isDemoUser) {
            $orders = $demoData;
        } else {
            $search = _post('search');
            $entregas = array();
            //condicion para filtrar por dispositivos asignados al usuario, si es YPF ve todos los dispositivos
            if ($ypfSales){
                $userTableDevices = "INNER JOIN ypf_customer_sales ycs ON ycs.id_cliente = c.id";
                $userConditionDevices = "AND ycs.id_usuario = '$usuario_id'";
            } else if($usuario_id != $id_usuario_ypf){
                $userTableDevices = "LEFT JOIN dispositivos_usuarios du ON du.id_dispositivo = r.id_dispositivo";
                $userConditionDevices = "AND du.id_usuario = '$usuario_id'";
            } else{
                $userTableDevices = "";
                $userConditionDevices = "";
            }

            if($filtroDeTerminalParametro){
                $filtroDeTerminal = "AND (ccd.id_campo = 57274 AND ccd.valor in ('$filtroDeTerminalParametro'))";
            }else{
                $filtroDeTerminal = "";
            }

            if($filtroGasParametro){
                $razon_social = " COALESCE(ccd2.valor, c.razon_social) AS razon_social, ";
                $joinNomenclatura = " LEFT JOIN clientes_campos_detalles ccd2 ON c.id = ccd2.id_cliente AND ccd2.id_campo = $id_campo_nomenclatura ";
                $filtroGas = "AND (ccd.id_campo = 57287 AND ccd.valor  ='$filtroGasParametro')";
            }else{
                $razon_social = "c.razon_social, ";
                $joinNomenclatura = "";
                $filtroGas = "";
            }

            if($filtroTipoClienteParametro){
                $filtroTipoCliente = "AND c.codigo_tipo_cliente in ('$filtroTipoClienteParametro')";
            }else{
                $filtroTipoCliente = "";
            }

            if ($filtroDeIdTerminalParametro) {
                $filtroDeIdTerminal = " AND SUBSTRING(r.inicio_id,2) = '$filtroDeIdTerminalParametro' ";
            }else{
                $filtroDeIdTerminal = "";
            }

            if (!$fecha_por_post) {
                $sql = "SELECT r.id AS id_reparto, r.codigo AS codigo_reparto, r.id_dispositivo, m.nombre, 
                                $razon_social
                                c.codigo_tipo_cliente,
                                c.codigo,
                                m.transportista,
                                r.inicio_id,
                                r.fecha_presentacion,
                                cr.fecha_visitado,
                                cr.fecha_inicio_tarea,
                                cr.fecha_fin_tarea,
                                cr.fecha_fin_visita,
                                cr.descripcion,
                                cr.respuesta_garmin,
                                cr.id_cliente,
                                cr.id as cliRId,
                                cr.marcado_manual,
                                pp.descripcion AS terminal,
                                d.ult_medicion_gps
                                $selectProductos
                            FROM repartos r
                            LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id 
                            LEFT JOIN clientes_repartos cr ON r.id = cr.id_reparto
                            LEFT JOIN dispositivos d ON r.id_dispositivo = d.id
                            LEFT JOIN maquinas m ON d.id_maquina = m.id
                            LEFT JOIN clientes c ON cr.id_cliente = c.id
                            LEFT JOIN clientes_campos_detalles ccd ON ccd.id_cliente = c.id
                            LEFT JOIN puntos_partida pp ON pp.id = SUBSTRING(r.inicio_id,2)
                            $joinProductos
                            $joinNomenclatura
                            $userTableDevices
                            $troncalSQL
                            WHERE r.id_usuario = '$id_usuario_ypf'
                            $userConditionDevices
                            $filtroDeTerminal $filtroGas
                            $filtroTipoCliente
                            $aeroSQL
                            $filtroDeIdTerminal
                            AND r.fecha > DATE_SUB(NOW(), INTERVAL 1 MONTH)
                            AND (r.codigo LIKE '%$search%' OR
                                 c.razon_social LIKE '%$search%' OR
                                 c.codigo_tipo_cliente LIKE '%$search%' OR
                                 m.nombre LIKE '%$search%' OR
                                 r.transportista LIKE '%$search%')
                            AND NOT (r.id_dispositivo = 0 AND r.transportista IS NULL)
                            AND ( yds.dropped = 0 and yds.dropped_at is NULL )
                            AND r.estado NOT IN ('PRELIMINAR', 'PENDIENTE')
                            AND r.id_dispositivo > 0
                            AND r.fecha_presentacion NOT LIKE '%00:00:00' /* TODO: Buscar otra forma de filtrar los despachos sin salida */
                            GROUP BY r.codigo, cr.id_cliente
                            ORDER BY r.fecha, r.codigo DESC";

            } else {

                if($hoy === date('Y-m-d')) {
                    $fechaSQLCondition = "AND r.fecha = '$hoy'";
                    $fechaIncialSQLCondition = "AND r.fecha_inicio = '$hoy'";
                    //Filtrar los FINALIZADOS con mas de 24hs de antiguedad
                    $filterFinalizados = "AND (cr.fecha_fin_visita IS NULL OR (cr.fecha_fin_visita IS NOT NULL AND (HOUR(TIMEDIFF(NOW(),  cr.fecha_fin_visita)) * 60) + MINUTE(TIMEDIFF(NOW(),  cr.fecha_fin_visita)) < 1440 ) )";
                } else {
                    $fechaSQLCondition = "AND r.fecha = '$hoy'";
                    $fechaIncialSQLCondition = "AND r.fecha_inicio = '$hoy'";
                    $filterFinalizados = "";
                }

                $sqlInitial = "(SELECT r.id AS id_reparto, r.codigo AS codigo_reparto, r.id_dispositivo, m.nombre, 
                                        $razon_social
                                        c.codigo,
                                        c.codigo_tipo_cliente,
                                        m.transportista,
                                        r.inicio_id,
                                        r.fecha_presentacion,
                                        cr.fecha_visitado,
                                        cr.fecha_inicio_tarea,
                                        cr.fecha_fin_tarea,
                                        cr.fecha_fin_visita,
                                        cr.descripcion,
                                        cr.respuesta_garmin,
                                        cr.id_cliente,
                                        cr.id as cliRId,
                                        cr.marcado_manual,
                                        pp.descripcion AS terminal,
                                        d.ult_medicion_gps
                                        $selectProductos
                                    FROM repartos r
                                    LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id 
                                    LEFT JOIN clientes_repartos cr ON r.id = cr.id_reparto
                                    LEFT JOIN dispositivos d ON r.id_dispositivo = d.id
                                    LEFT JOIN maquinas m ON d.id_maquina = m.id
                                    LEFT JOIN clientes c ON cr.id_cliente = c.id
                                    LEFT JOIN clientes_campos_detalles ccd ON ccd.id_cliente = c.id
                                    LEFT JOIN puntos_partida pp ON pp.id = SUBSTRING(r.inicio_id,2)
                                    $joinProductos
                                    $joinNomenclatura
                                    $userTableDevices
                                    $troncalSQL
                                    WHERE r.id_usuario = '$id_usuario_ypf'
                                    $userConditionDevices
                                    $filtroDeTerminal $filtroGas
                                    $filtroTipoCliente
                                    $aeroSQL
                                    $filtroDeIdTerminal
                                    ";

                $sql = "SELECT * FROM (".$sqlInitial." $fechaSQLCondition
                                        AND (r.codigo LIKE '%$search%' OR
                                             c.razon_social LIKE '%$search%' OR
                                             m.nombre LIKE '%$search%' OR
                                             r.transportista LIKE '%$search%')
                                        AND NOT (r.id_dispositivo = 0 AND r.transportista IS NULL)
                                        $filterFinalizados
                                        AND ( yds.dropped = 0 and yds.dropped_at is NULL )
                                        AND r.estado NOT IN ('PRELIMINAR', 'PENDIENTE')
                                        AND r.id_dispositivo > 0
                                        AND r.fecha_presentacion NOT LIKE '%00:00:00')
                          UNION ".$sqlInitial." $fechaIncialSQLCondition                                        
                                        AND (r.codigo LIKE '%$search%' OR
                                             c.razon_social LIKE '%$search%' OR
                                             m.nombre LIKE '%$search%' OR
                                             r.transportista LIKE '%$search%')
                                        AND NOT (r.id_dispositivo = 0 AND r.transportista IS NULL)
                                        $filterFinalizados
                                        AND ( yds.dropped = 0 and yds.dropped_at is NULL )
                                        AND r.estado NOT IN ('PRELIMINAR', 'PENDIENTE')
                                        AND r.id_dispositivo > 0
                                        AND r.fecha_presentacion NOT LIKE '%00:00:00')) as despachos_fechas
                                        GROUP BY codigo_reparto, id_cliente
                                        ORDER BY codigo_reparto DESC";
            }
            loggear('Dashboard ' . $sql, "ypf_entregas_debug", '', 'log_ypf');

            $r_orders = mysql_query($sql);

            if(mysql_num_rows($r_orders)) {
                while ($row = mysql_fetch_assoc($r_orders)) {
                    $orders[] = $row;
                }
            }
        }

        $entregas = processDataResource($orders);
        loggear(json_encode($entregas), 'ypf_entregas_debug', '', 'log_ypf');
        echo json_encode($entregas);
        break;
    }
    case "getUltimaActualizacion":{

        $sql = "SELECT *
                FROM log_interfaces
                WHERE id_usuario = '$id_usuario_ypf'
                AND interfaz IN ('interfaz_ypf_repartos.php', 'interfaz_ypf_repartos_cenit.php')
                ORDER BY fecha_ejecucion DESC
                LIMIT 1";

        $query = mysql_query($sql);

        $row = mysql_fetch_assoc($query);

        die(json_encode($row));

        break;}
    case "getPendientes": {
        date_default_timezone_set('America/Argentina/Buenos_Aires');
        $hoy = date('Y-m-d');
        $orders = [];

        $filtroDeTerminalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TERMINAL') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TERMINAL') : false);
        $filtroDeIdTerminalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_ID_TERMINAL') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_ID_TERMINAL') : false);
        $filtroTipoClienteParametro = (UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TIPO_CLIENTE') ? UsuariosParametro::getValorByUsuarioAndParametro($usuario_id, 'DASHBOARD_FILTRO_TIPO_CLIENTE') : false);
        if ($filtroGasParametro) {
            $id_campo_nomenclatura = mysql_result(mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = 17242 AND nombre = 'NOMENCLATURA_GAS'"), 0, 0);
            $selectProductos = ", mp.descripcion AS producto";
            $joinProductos = "LEFT JOIN pedidos p ON p.id_cliente_reparto = cr.id
                              LEFT JOIN pedidos_detalles pd ON pd.id_pedido = p.id
                              LEFT JOIN maestro_productos mp ON (mp.codigo = pd.codigo_producto AND mp.id_usuario = 3050)";
        } else {
            $id_campo_nomenclatura = '';
            $selectProductos = '';
            $joinProductos = '';
        }

        $orderBy = " ORDER BY r.codigo DESC ";

        if($isDemoUser) {
            $orders = $demoDataPendientes;
        } else {
            $search = _post('search');
            $fecha = _post('fecha', false);

            if ($ypfSales){
                $userTableDevices = "INNER JOIN ypf_customer_sales ycs ON ycs.id_cliente = c.id";
                $userConditionDevices = "AND ycs.id_usuario = '$usuario_id'";
            } else if($usuario_id != $id_usuario_ypf){
                $userTableDevices = "LEFT JOIN dispositivos_usuarios du ON du.id_dispositivo = r.id_dispositivo";
                $userConditionDevices = "AND du.id_usuario = '$usuario_id'";
            } else{
                $userTableDevices = "";
                $userConditionDevices = "";
            }

            if($filtroDeTerminalParametro){
                $filtroDeTerminal = "AND (ccd.id_campo = 57274 AND ccd.valor in ('$filtroDeTerminalParametro'))";
            }else{
                $filtroDeTerminal = "";
            }

            if ($filtroDeIdTerminalParametro) {
                $filtroDeIdTerminal = " AND SUBSTRING(r.inicio_id,2) = '$filtroDeIdTerminalParametro' ";
            }else{
                $filtroDeIdTerminal = "";
            }

            if($filtroGasParametro){
                $razon_social = " COALESCE(ccd2.valor, c.razon_social) AS razon_social, ";
                $joinNomenclatura = " LEFT JOIN clientes_campos_detalles ccd2 ON c.id = ccd2.id_cliente AND ccd2.id_campo = $id_campo_nomenclatura ";
                $filtroGas = "AND (ccd.id_campo = 57287 AND ccd.valor = '$filtroGasParametro')";
                $estado = " AND r.estado IN ('PENDIENTE', 'PRELIMINAR') ";
                $orderBy = " ORDER BY FIELD(r.estado, 'PRELIMINAR', 'PENDIENTE') ASC,
                            r.codigo DESC ";
            }else{
                $estado  = " AND r.estado IN ('PENDIENTE', 'VENCIDO')";
                $razon_social = "c.razon_social, ";
                $joinNomenclatura = "";
                $filtroGas = "";
            }

            if($filtroTipoClienteParametro){
                $filtroTipoCliente = "AND c.codigo_tipo_cliente in ('$filtroTipoClienteParametro')";
            }else{
                $filtroTipoCliente = "";
            }

            if($fecha) {
                if(date('Y-m-d',strtotime($fecha)) != $fecha) {
                    $fecha = date('Y-m-d');
                }
                if($filtroGasParametro){
                    $filterFecha = " AND r.fecha_inicio = '$fecha' ";
                } else {
                    $filterFecha = " AND r.fecha = '$fecha'
                                    AND r.fecha_inicio BETWEEN DATE_SUB('$fecha', INTERVAL 6 DAY) AND '$fecha'
                                    AND TIME(r.fecha_presentacion) = '00:00:00'
                                    AND r.codigo_tipo_reparto != 'BIOCOMBUSTIBLES' ";
                }
            } else {
                //No debería entrar acá
                //$filterFecha = "AND r.fecha_inicio > DATE_SUB(NOW(), INTERVAL 1 MONTH)";
                loggear('Pendientes error '. $sql, "ypf_entregas_debug", '', 'log_ypf');
            }

            $entregas = array();
            $sql = "SELECT r.id AS id_reparto, r.codigo AS codigo_reparto, r.id_dispositivo, r.estado, m.nombre,
                                $razon_social
                                c.codigo,
                                c.codigo_tipo_cliente,
                                m.transportista,
                                r.inicio_id,
                                r.fecha_presentacion,
                                cr.fecha_visitado,
                                cr.fecha_inicio_tarea,
                                cr.fecha_fin_tarea,
                                cr.fecha_fin_visita,
                                cr.descripcion,
                                cr.respuesta_garmin,
                                cr.id_cliente,
                                cr.id AS cliRId,
                                yte.estimated AS eta_estimado,
                                yte.real AS eta_real,
                                yetc.eta AS eta_actual,
                                d.ult_medicion_gps,
                                    pp.descripcion AS terminal,
                                ST_Distance(
                                    ST_GEOMFROMTEXT(CONCAT('POINT(',pp.latitud,' ',pp.longitud,')')),
                                    ST_GEOMFROMTEXT(CONCAT('POINT(',d.latitud_gps,' ',d.longitud_gps,')'))
                                ) AS distance
                                $selectProductos
                            FROM repartos r
                            LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id
                            LEFT JOIN clientes_repartos cr ON r.id = cr.id_reparto
                            LEFT JOIN dispositivos d ON r.id_dispositivo = d.id
                            LEFT JOIN maquinas m ON d.id_maquina = m.id
                            LEFT JOIN clientes c ON cr.id_cliente = c.id
                            LEFT JOIN puntos_partida pp ON pp.id = SUBSTRING(r.inicio_id,2)
                            LEFT JOIN ypf_terminal_event yte ON yte.delivery_id = r.id
                            LEFT JOIN ypf_eta_terminal_cache yetc ON yetc.terminal_event_id = yte.id
                            LEFT JOIN clientes_campos_detalles ccd ON c.id = ccd.id_cliente
                            $joinProductos
                            $joinNomenclatura
                            $userTableDevices
                            $troncalSQL
                            WHERE r.id_usuario = '$id_usuario_ypf'
                            $userConditionDevices
                            $filtroDeTerminal $filtroGas
                            $filtroTipoCliente
                            $aeroSQL
                            $filtroDeIdTerminal
                            AND (r.codigo LIKE '%$search%' OR
                                 c.razon_social LIKE '%$search%' OR
                                 m.nombre LIKE '%$search%' OR
                                 r.transportista LIKE '%$search%')
                            AND ( yds.dropped = 0 AND yds.dropped_at IS NULL )
                            AND NOT (r.id_dispositivo = 0 AND r.transportista IS NULL)
                            $estado
                            AND yte.type_id = 11
                            $filterFecha
                            GROUP BY r.codigo, cr.id_cliente
                            $orderBy";

            //QS-12117
            if ($filtroGasParametro || (!$filtroGasParametro && $hoy <= $fecha)) {
                $r_orders = mysql_query($sql);

                if (mysql_num_rows($r_orders)) {
                    while ($row = mysql_fetch_assoc($r_orders)) {
                        $orders[] = $row;
                    }
                }
                loggear('Pendientes filtro gas ' . $sql, "ypf_entregas_debug", '', 'log_ypf');
            }else{
                loggear('Pendientes ' . $sql, "ypf_entregas_debug", '', 'log_ypf');
            }
        }
        $entregas = processDataResource($orders, true);
        echo json_encode($entregas);
        break;
    }
    case "exportData":{
        $start = _get('start');
        $end = _get('end');
        $noPendings =  filter_var(_get('noPendings'), FILTER_VALIDATE_BOOLEAN);
        $data = exportData($usuario_id, $start, $end, $noPendings);
        echo json_encode($data);
        break;
    }
    case "editTrip":{
        $id_cliente_reparto = _post('id_cliente_reparto');
        $fecha_visitado = _post('fecha_visitado');
        $fecha_inicio_tarea = _post('fecha_inicio_tarea');
        $fecha_fin_tarea = _post('fecha_fin_tarea');
        $fecha_fin_visita = _post('fecha_fin_visita');

        $cliente = [
            'id' => $id_cliente_reparto,
            'fecha_visitado' => $fecha_visitado,
            'fecha_inicio_tarea' => $fecha_inicio_tarea,
            'fecha_fin_tarea' => $fecha_fin_tarea,
            'fecha_fin_visita' => $fecha_fin_visita
        ];

        $data = editTrip($usuario_id,$cliente);
        echo json_encode($data);
        break;
}
}

function processDataResource($orders,$pendientes = false) {
    global $id_usuario_ypf;
    global $usuario_id;
    $entregas = array();

    $devicesIds = getIdDispositivosUsuario($id_usuario_ypf,false);
    $rango_llegada_planta = (UsuariosParametro::getValorByUsuarioAndParametro(3050, 'DASHBOARD_LLEGADA_PLANTA') ? UsuariosParametro::getValorByUsuarioAndParametro(3050, 'DASHBOARD_LLEGADA_PLANTA') : 24);
    $rango_salida_planta = (UsuariosParametro::getValorByUsuarioAndParametro(3050, 'DASHBOARD_SALIDA_PLANTA') ? UsuariosParametro::getValorByUsuarioAndParametro(3050, 'DASHBOARD_SALIDA_PLANTA') : 24);

    //$depositos = getDepositosById($id_usuario_ypf);
    foreach ($orders as $row) {
        $entrega = array();

        $id_reparto = $row['id_reparto'];
        $codigo_reparto = $row['codigo_reparto'];
        $terminal = $row['terminal'];
        $razon_social = $row['razon_social'];
        $localidad = $row['localidad'];
        $provincia = $row['provincia'];
        $codigo_tipo_cliente = $row['codigo_tipo_cliente'];
        $codigo = $row['codigo'];
        $descripcion = $row['descripcion'];
        $fecha_presentacion = $row['fecha_presentacion'];
        $fecha_visitado = $row['fecha_visitado'];
        $fecha_inicio_tarea = $row['fecha_inicio_tarea'];
        $fecha_fin_tarea = $row['fecha_fin_tarea'];
        $fecha_fin_visita = $row['fecha_fin_visita'];
        $id_dispositivo  = $row['id_dispositivo'];
        $nombre = $row['nombre'];
        $transportista = $row['transportista'];
        $respuesta_garmin = $row['respuesta_garmin'];
        $inicio_id = $row['inicio_id'];
        $id_cliente = $row['id_cliente'];
        $cliRId = $row['cliRId'];
        $ult_medicion_gps = $row['ult_medicion_gps'];
        $distance = $row['distance'];
        $marcado_manual = $row['marcado_manual'];
        $eta_estimado = $row['eta_estimado'];
        $eta_real = $row['eta_real'];
        $eta_actual = $row['eta_actual'];
        $estado = $row['estado'];
        $producto = $row['producto'];

        if(trim($row['producto']) == "PROPANO COMERCIAL"){
            $producto = "PC";
        }
        
        if(trim($row['producto']) == "BUTANO COMERCIAL"){
            $producto = "BC";
        }

        if(in_array($id_dispositivo, $devicesIds)) {

            $name = explode(" ", $nombre);

            $entrega['id_reparto'] = $id_reparto;
            $entrega['codigo_reparto'] = $codigo_reparto;
            $entrega['razon_social'] = $razon_social;
            $entrega['localidad'] = $localidad;
            $entrega['provincia'] = $provincia;
            $entrega['codigo'] = $codigo;
            $entrega['codigo_tipo_cliente'] = $codigo_tipo_cliente;
            $entrega['descripcion'] = $descripcion;
            $entrega['cr'] = $cliRId;
            $entrega['c'] = $id_cliente;
            $entrega['respuesta_garmin'] = $respuesta_garmin;
            $entrega['marcado_manual'] = $marcado_manual;
            $entrega['estado'] = $estado;    

            $entrega['producto'] = $producto;

            if($distance) {
                $entrega['distance'] = $distance * 111195;
            }
//            if($pendientes) {
//                //Obtengo el reparto anterior si tiene, para mostrar el ETA de retorno
//                $r_retorno = mysql_query("SELECT r.id, r.fecha_presentacion, p.fecha_regreso_estimado,
//                        p.fecha_regreso_real, p.fecha_regreso_inicial
//                    FROM repartos r
//                    LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id
//                    LEFT JOIN repartos_prediccion p ON r.id = p.id_reparto
//                    WHERE r.id_usuario = $id_usuario_ypf AND r.id_dispositivo = $id_dispositivo
//                    AND r.codigo < $codigo_reparto
//                    AND ( yds.dropped = 0 and yds.dropped_at is NULL )
//                    AND DATE(r.fecha_presentacion) >= SUBDATE(CURRENT_DATE(),1)
//                    ORDER BY ABS(r.codigo) desc
//                    LIMIT 1");
//                if(mysql_num_rows($r_retorno)) {
//                    $retorno = mysql_fetch_assoc($r_retorno);
//                    $fecha_presentacion_anterior = $retorno['fecha_presentacion'];
//                    $fecha_regreso_inicial = $retorno['fecha_regreso_inicial'];
//                    $fecha_regreso_real = $retorno['fecha_regreso_real'];
//                    if(date('H:i:s', strtotime($fecha_presentacion_anterior)) != '00:00:00' && $fecha_regreso_inicial) {
//                        $entrega['eta_retorno_inicial'] = $fecha_regreso_inicial;
//                        $entrega['eta_retorno_real'] = $fecha_regreso_real ? $fecha_regreso_real : '';
//                    } else {
//                        $entrega['eta_retorno_inicial'] = '';
//                        $entrega['eta_retorno_real'] = '';
//                    }
//                } else {
//                    $entrega['eta_retorno_inicial'] = '';
//                    $entrega['eta_retorno_real'] = '';
//                }
//            }

            $entrega['eta_retorno_inicial'] = $eta_actual ? $eta_actual : '';
            $entrega['fecha_presentacion'] = $fecha_presentacion;
            $entrega['fecha_visitado'] = $fecha_visitado;
            $entrega['fecha_inicio_tarea'] = $fecha_inicio_tarea;
            $entrega['fecha_fin_tarea'] = $fecha_fin_tarea;
            $entrega['fecha_fin_visita'] = $fecha_fin_visita;
            $entrega['fecha_llegada_planta'] = "";
            $entrega['eta_estimado'] = $eta_estimado ? $eta_estimado : '';
            $entrega['eta_real'] = $eta_real ? $eta_real : '';


            $entrega['inicio'] = $terminal;

            $entrega['id_dispositivo'] = "";
            $entrega['nombre'] = "";
            $entrega['maquina'] = "";
            $entrega['transportista'] = "";

            if (!$id_dispositivo) {
                // Todavia no se registro el camion
                $entrega['transportista'] = $transportista;
                $entrega['nombre'] = _("Sin asignar");
                $entrega['maquina'] = _("Sin asignar");
            } else {
                // Ya se registro el camion en planta

                $entrega['id_dispositivo'] = $id_dispositivo;
                $entrega['nombre'] = $nombre;
                $entrega['maquina'] = $nombre;

                $ultTimestamp = strtotime(date($ult_medicion_gps));
                $now = strtotime("now");
                if(!$ult_medicion_gps || ($ultTimestamp < $now && ($now - $ultTimestamp) > (24*60*60))) {
                    $entrega['reportando'] = false;
                    $entrega['nombre'] .= ' <span class="glyphicon glyphicon-warning-sign" ' .
                                                ' data-placement="right" ' .
                                                ' data-toggle="tooltip" ' .
                                                ' data-title="'._('Ult. reporte') . ': ' . $ult_medicion_gps . '"></span> ';
                } else {
                    $entrega['reportando'] = true;
                }

                // Mostrar si esta en marcha debajo del nombre
                if(estaDetenido($id_dispositivo))
                    $entrega['nombre'] .= ' <span class="glyphicon glyphicon-minus-sign" ' .
                                                ' style="color: #A53232;" ' .
                                                ' data-placement="right" ' .
                                                ' data-toggle="tooltip" ' .
                                                ' data-title="'._('Detenido').'"></span> ';
                else
                    $entrega['nombre'] .= ' <span class="glyphicon glyphicon-ok-sign" ' .
                                                ' style="color: #2F9633;" ' .
                                                ' data-placement="right" ' .
                                                ' data-toggle="tooltip" ' .
                                                ' data-title="'._('En marcha').'"></span> ';

                if ($transportista) {
                    $entrega['transportista'] = $transportista;
                }

                if(date('H:i:s',strtotime($entrega['fecha_presentacion'])) != '00:00:00') {
                    //Fecha de llegada a planta
                    if ($fecha_presentacion) {
                        // Inicio de carga (llegada a zona de terminal)
                        $fecha_limite_inicio_carga1 = date("Y-m-d H:i:s", strtotime("-$rango_llegada_planta hours", strtotime($fecha_presentacion)));
                        $fecha_limite_inicio_carga2 = $fecha_presentacion;

                        $r_inicio_carga = mysql_query("SELECT time_stamp FROM eventos
                                                        WHERE id_dispositivo = $id_dispositivo
                                                        AND time_stamp BETWEEN '$fecha_limite_inicio_carga1' AND '$fecha_limite_inicio_carga2'
                                                        AND codigo_tipo_evento = 'LZONA'
                                                        AND (data IN (SELECT descripcion FROM puntos_partida WHERE 
                                                         id_usuario = 3050 and codigo_erp IS NOT NULL) OR data LIKE 'Terminal%')
                                                        ORDER BY time_stamp DESC
                                                        LIMIT 1");
                        if (mysql_num_rows($r_inicio_carga)) {
                            $entrega['fecha_llegada_planta'] = mysql_result($r_inicio_carga,0,0);
                        } else if ($fecha_visitado) {
                            // Oops, llego al cliente y no detecte la fecha de inicio de carga. Le pongo la fecha de registracion.
                            $entrega['fecha_llegada_planta'] = ""; // Mejor no...
                        }
                    }
                    // Fin de carga
                    if ($entrega['fecha_visitado']) {
                        $fecha_inicio = date("Y-m-d H:i:s", strtotime($entrega['fecha_presentacion']));
                        $fecha_limite_fin_carga = date("Y-m-d H:i:s", strtotime($entrega['fecha_visitado']));
                        $order = " DESC ";
                    } else {
                        $fecha_inicio = date("Y-m-d H:i:s", strtotime($entrega['fecha_presentacion']));

                        $fecha_limite_fin_carga = date("Y-m-d H:i:s", strtotime("+$rango_salida_planta hours", strtotime($entrega['fecha_presentacion'])));
                        $order = " ASC ";
                    }
                    $r_fin_carga = mysql_query("SELECT time_stamp FROM eventos
                                                WHERE id_dispositivo = $id_dispositivo
                                                AND time_stamp BETWEEN '$fecha_inicio' AND '$fecha_limite_fin_carga'
                                                AND codigo_tipo_evento = 'FZONA'
                                                AND (data IN (SELECT descripcion FROM puntos_partida WHERE 
                                                         id_usuario = 3050 and codigo_erp IS NOT NULL) OR data LIKE 'Terminal%') 
                                                ORDER BY time_stamp $order
                                                LIMIT 1");

                    if (mysql_num_rows($r_fin_carga)) {
                        $entrega['fecha_fin_carga'] = mysql_result($r_fin_carga,0,0);
                    } else {
                        // fecha_presentacion es mayor a 12 horas actuales flaggear como demorado   
                        if( strtotime($entrega['fecha_presentacion']) < strtotime("-12 hours")){
                            $entrega['demorado'] = true;
                        } else {
                            $entrega['demorado'] = false;
                        }
                        // Oops, llego al cliente y no detecte la fecha de fin de carga. Le pongo la fecha de inicio de carga.
                        $entrega['fecha_fin_carga'] = null; // Mejor no...
                    }

                    // Mostrar el ETA debajo del nombre si esta en transito hacia el cliente
                    if($entrega['fecha_presentacion']) {
//                        $eta = getETA($cliRId);
                        $etaCache = getETACache($cliRId);
                        //$eta_from_event_terminal = getETAFromTerminalEvent($id_reparto, '4');
                        //$entrega['nombre'] .= '<br/> <small>'._('Llegada estimada').': '.$eta['text'].'</small> ';
                        if($etaCache) {
                            $entrega['eta'] = $etaCache['eta_cache'];
                            $entrega['eta_inicial'] = $etaCache['initial_eta'];
//                            $entrega['eta_inicial'] = $eta['fecha_arribo_inicial'];
                        }

                    }
                    if(!isGeocoded($id_cliente)) {
                        $entrega['nogeo'] = 'No geocod.';
                    }

                }
            }

            $entregas[] = $entrega;
        }
    }

    return $entregas;
}


/*  Utils   */

function estaDetenido($id_dispositivo){
    $r_detenido = mysql_query("SELECT id FROM detenidos 
                                  WHERE id_dispositivo = $id_dispositivo 
                                  AND detenido_hasta IS NULL");
    return mysql_num_rows($r_detenido);
}

function isGeocoded($id_cliente) {
    $r = mysql_query("SELECT latitud as lat, longitud as lon
                            FROM clientes 
                            WHERE id = '$id_cliente'
                            AND (latitud != 0 AND latitud IS NOT NULL)
                            AND (longitud != 0 AND longitud IS NOT NULL)");

    return $r && mysql_num_rows($r);
}

function getETA($id_cliente_reparto) {
    $sql = "SELECT fecha_arribo_estimado, fecha_arribo_inicial
            FROM clientes_repartos_prediccion
            WHERE id_cliente_reparto = $id_cliente_reparto";
    $r = mysql_query($sql);
    if($r && mysql_num_rows($r)) {
        $eta = mysql_fetch_assoc($r);
        return $eta;
    } else {
        return false;
    }
}

function getETACache($id_cliente_reparto){
    $sql = "SELECT ecdc.eta as eta_cache, ycdd.initial_eta as initial_eta
            FROM ypf_client_delivery_event cde
            LEFT JOIN ypf_eta_client_delivery_cache ecdc ON ecdc.client_delivery_event_id = cde.id
            LEFT JOIN ypf_client_delivery_data ycdd ON ycdd.client_delivery_id = cde.client_delivery_id
            WHERE cde.client_delivery_id = $id_cliente_reparto
            AND cde.type_id = 5
            ";
    $r = mysql_query($sql);
    if($r && mysql_num_rows($r)) {
        $etaCache = mysql_fetch_assoc($r);
        return $etaCache;
    } else {
        return false;
    }
}

function getETAFromTerminalEvent($id_reparto, $type_id){
    $sql = "SELECT *
            FROM ypf_terminal_event
            WHERE delivery_id = $id_reparto
              AND type_id = '$type_id'";
    $r = mysql_query($sql);
    if($r && mysql_num_rows($r)) {
        $terminalEta = mysql_fetch_assoc($r);
        return $terminalEta;
    } else {
        return false;
    }
}

function getDepositosById($id_usuario) {
    $sql = "SELECT id, descripcion, codigo_erp FROM puntos_partida WHERE id_usuario = '$id_usuario' AND codigo_erp = ''";

    $r_pp = mysql_query($sql);

    $pp = array();
    if(mysql_num_rows($r_pp)) {
        while ($p = mysql_fetch_assoc($r_pp)) {
            $pp[$p['id']] = $p['descripcion'];
        }
    }

    return $pp;
}

function exportData($id_usuario, $start, $end, $noPendings = false) {
    $diff = strtotime($end) - strtotime($start);
    if($diff > 60 * 60 * 24) {
        exportCSV($id_usuario, $start, $end, $noPendings);
    } else {
        exportExcel($id_usuario, $start, $end, $noPendings);
    }
}

function exportExcel($id_usuario, $start, $end,  $noPendings = false) {
    require_once(ROOT.'/classes/PHPExcel.php');
    $data = dataInDateRange($id_usuario, $start, $end);

    $styleArrayTitle = array(
        'font' => array(
            'bold' => true,
        ),
        'alignment' => array(
            'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
        ),
        'fill' => array(
            'type' => PHPExcel_Style_Fill::FILL_SOLID,
            'color' => array(
                'argb' => 'FF9BBB59'
            ),
        )
    );
    $styleArraySubTitle = array(
        'font' => array(
            'bold' => true,
        ),
        'alignment' => array(
            'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_CENTER,
            'vertical' => PHPExcel_Style_Alignment::VERTICAL_JUSTIFY,
        ),
        'fill' => array(
            'type' => PHPExcel_Style_Fill::FILL_SOLID,
            'color' => array(
                'argb' => 'FF538DD5'
            ),
        )
    );
    $styleArrayBorder = array(
        'borders' => array(
            'right' => array(
                'style' => PHPExcel_Style_Border::BORDER_THIN,
            ),
            'left' => array(
                'style' => PHPExcel_Style_Border::BORDER_THIN,
            ),
            'top' => array(
                'style' => PHPExcel_Style_Border::BORDER_THIN,
            ),
            'bottom' => array(
                'style' => PHPExcel_Style_Border::BORDER_THIN,
            )
        )
    );

    $cacheMethod = PHPExcel_CachedObjectStorageFactory:: cache_to_phpTemp;
    $cacheSettings = array( 'memoryCacheSize' => '128MB');
    PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);

    $objPHPExcel = new PHPExcel();

    $objPHPExcel->getProperties()->setCreator("Quadminds Technologies SA")
        ->setLastModifiedBy("Quadminds Technologies SA")
        ->setTitle("Viajes")
        ->setSubject("Viajes")
        ->setDescription("Viajes")
        ->setKeywords("Viajes")
        ->setCategory("Viajes");

    $objPHPExcel->setActiveSheetIndex(0);
    $objPHPExcel->getActiveSheet()->setShowGridlines(false);

    $objDrawing = new PHPExcel_Worksheet_Drawing();
    $objDrawing->setName('Logo');
    $objDrawing->setDescription('Logo');
    $objDrawing->setPath('/var/www/saas/img/qmt_header.png');
    $objDrawing->setWidth(256);
    $objDrawing->setHeight(64);
    $objDrawing->setWorksheet($objPHPExcel->getActiveSheet());

    $columnOffset = 1;
    $rowOffset = 5;
    $colCounter = 0;

    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn($colCounter)->setWidth(9);
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(10);//Terminal
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(12);//Despacho
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(40);//Cliente
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(40);//Localidad
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(40);//Provincia
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(12);//Nro cuenta
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(10);//Trans
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Vehiculo
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//OOPP
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Registracion
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Carga
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//En transito
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//ETA inicial
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(24);//Llegada a cli
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(10);//Dif. ETA
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Inicio descarga
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Fin descarga
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Salida cliente
    $objPHPExcel->getActiveSheet()->getColumnDimensionByColumn(++$colCounter)->setWidth(20);//Estado

    $colCounter = 0;
    $encabezado = array();
    //Array de encabezados
    array_push($encabezado, _('Terminal'));
    array_push($encabezado, _('Despacho'));
    array_push($encabezado, _('Cliente'));
    array_push($encabezado, _('Localidad'));
    array_push($encabezado, _('Provincia'));
    array_push($encabezado, _('Nro cuenta'));
    array_push($encabezado, _('Transportista'));
    array_push($encabezado, _('Vehículo'));
    array_push($encabezado, _('OOPP'));
    array_push($encabezado, _('Llegada a planta'));
    array_push($encabezado, _('Carga'));
    array_push($encabezado, _('En transito'));
    array_push($encabezado, _('ETA inicial'));
    array_push($encabezado, _('Llegada a cliente'));
    array_push($encabezado, _('Dif. ETA'));
    array_push($encabezado, _('Inicio descarga'));
    array_push($encabezado, _('Fin descarga'));
    array_push($encabezado, _('Salida cliente'));
    array_push($encabezado, _('Estado actual'));

    //Dibujar Encabezados
    for ($i = 0; $i < count($encabezado); ++$i){
        //$objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($columnOffset + $i,$rowOffset)->getBorders()->getTop()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset + $i,$rowOffset,$encabezado[$i]);
        $objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($columnOffset + $i,$rowOffset)->applyFromArray($styleArraySubTitle);
        $objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($columnOffset + $i,$rowOffset)->applyFromArray($styleArrayBorder);
        //$objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($columnOffset + $i,$rowOffset)->getBorders()->getLeft()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
    }

    //$objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($columnOffset+(count($encabezado))-1,$rowOffset)->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);

    ++$rowOffset;
    foreach($data as $i => $reparto){
        $colCounter = 0;
        $state = getCurrentState($reparto);

        if($noPendings && $state == "Pendiente"){
            continue;
        }

        $llegadaCliente = '';
        $dif_eta = '';
        if($reparto['nogeo']) {
            $llegadaCliente = $reparto['nogeo'];
        } else if($reparto['fecha_visitado']) {
            $llegadaCliente = $reparto['fecha_visitado'];
            if ($reparto['eta_inicial']) {
                $dif_eta = strtotime($reparto['fecha_visitado']) - strtotime($reparto['eta_inicial']);
                if ($dif_eta >= 0) {
                    $dif_eta = "+".sprintf('%02d:%02d', abs($dif_eta/3600),abs($dif_eta/60%60));
                } else {
                    $dif_eta = "-".sprintf('%02d:%02d', abs($dif_eta/3600),abs($dif_eta/60%60));
                }
            }


        } else if(array_key_exists('eta', $reparto) && $reparto['fecha_fin_carga']) {
            $llegadaCliente = 'ETA: ' . $reparto['eta'];
        }



        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset,$rowOffset, $reparto['inicio']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['codigo_reparto']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['razon_social']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['localidad']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['provincia']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['codigo']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['transportista']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['maquina']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['descripcion']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['fecha_llegada_planta']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['fecha_presentacion']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['fecha_fin_carga']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['eta_inicial']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $llegadaCliente);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $dif_eta);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['fecha_inicio_tarea']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['fecha_fin_tarea']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $reparto['fecha_fin_visita']);
        $objPHPExcel->getActiveSheet()->setCellValueByColumnAndRow($columnOffset+(++$colCounter),$rowOffset, $state);

        $objPHPExcel->getActiveSheet()->getStyleByColumnAndRow(1,$rowOffset)->getBorders()->getLeft()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
        $objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($columnOffset + $colCounter,$rowOffset)->getBorders()->getRight()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);

        $rowOffset++;
    }

    for($i = $columnOffset; $i <= ($columnOffset + $colCounter); $i++) {
        $objPHPExcel->getActiveSheet()->getStyleByColumnAndRow($i,$rowOffset-1)->getBorders()->getBottom()->setBorderStyle(PHPExcel_Style_Border::BORDER_THIN);
    }

    // redirect output to client browser
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=utf-8');
    header('Content-Disposition: attachment;filename="Despachos_'.$start.'_-_'.$end.'.xls"');
    header('Cache-Control: max-age=0');

    $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
    $objWriter->save('php://output');
}

function exportCSV($id_usuario, $start, $end,  $noPendings = false) {

    ini_set('max_execution_time', 600);

    if (ob_get_level()) {
        ob_end_clean();
    }

    $fp = fopen('php://output', 'w');

    header('Content-Encoding: UTF-8');
    header("Content-type: text/plain;charset=utf-8");
    header("Content-Disposition: attachment; filename=Despachos_".$start."_-_".$end.".csv");
    header("Pragma: no-cache");
    header("Expires: 0");
    echo "\xEF\xBB\xBF";

    flush();

    $header = [_('Terminal'),_('Despacho'),_('Cliente'),_('Localidad'),_('Provincia'),_('Nro cuenta'),_('Transportista'),
                _('Vehículo'),_('OOPP'),_('Llegada a planta'),_('Carga'),_('En transito'),
                _('ETA inicial'),_('Llegada a cliente'),_('Dif. ETA'),_('Inicio descarga'),
                _('Fin descarga'),_('Salida cliente'),_('Estado actual')];

    fputcsv($fp, $header, ';', '"');
    flush();

    $dateArray = createDateRangeArray($start, $end);

    foreach ($dateArray as $date) {

        $data = dataInDateRange($id_usuario, $date, $date);

        // Array output
        $output = [];
        foreach($data as $i => $reparto) {
            $state = getCurrentState($reparto);

            if($noPendings == true && $state == "Pendiente"){
                continue;
            }

            $llegadaCliente = '';
            $dif_eta = '';
            if($reparto['nogeo']) {
                $llegadaCliente = $reparto['nogeo'];
            } else if($reparto['fecha_visitado']) {
                $llegadaCliente = $reparto['fecha_visitado'];
                if ($reparto['eta_inicial']) {
                    $dif_eta = strtotime($reparto['fecha_visitado']) - strtotime($reparto['eta_inicial']);
                    if ($dif_eta >= 0) {
                        $dif_eta = "+".sprintf('%02d:%02d', abs($dif_eta/3600),abs($dif_eta/60%60));
                    } else {
                        $dif_eta = "-".sprintf('%02d:%02d', abs($dif_eta/3600),abs($dif_eta/60%60));
                    }
                }


            } else if(array_key_exists('eta', $reparto) && $reparto['fecha_fin_carga']) {
                $llegadaCliente = 'ETA: ' . $reparto['eta'];
            }

            $output[] = [
                $reparto['inicio'],
                $reparto['codigo_reparto'],
                $reparto['razon_social'],
                $reparto['localidad'],
                $reparto['provincia'],
                $reparto['codigo'],
                $reparto['transportista'],
                $reparto['maquina'],
                $reparto['descripcion'],
                $reparto['fecha_llegada_planta'],
                $reparto['fecha_presentacion'],
                $reparto['fecha_fin_carga'],
                $reparto['eta_inicial'],
                $llegadaCliente,
                $dif_eta,
                $reparto['fecha_inicio_tarea'],
                $reparto['fecha_fin_tarea'],
                $reparto['fecha_fin_visita'],
                $state
            ];
        }

        // Build CSV
        for ($i = 0; $i < count($output); ++$i) {
            if($output[$i]) {
                fputcsv($fp, $output[$i], ';', '"');
                flush();
            }
        }
    }

    fclose($fp);
    die;
}

function dataInDateRange($id_usuario, $start, $end) {
    global $id_usuario_ypf;
    global $ypfSales;
    global $filtroGasParametro;
    global $troncalSql;
    global $aeroSQL;

    if ($ypfSales) {
        $filterClientes = "INNER JOIN ypf_customer_sales ycs ON ycs.id_cliente = c.id";
        $filterUsuario = "AND ycs.id_usuario = '$id_usuario'";
    } else if($id_usuario != $id_usuario_ypf){
        $filterClientes = "LEFT JOIN dispositivos_usuarios du ON du.id_dispositivo = r.id_dispositivo";
        $filterUsuario = "AND du.id_usuario = '$id_usuario'";
    } else {
        $filterClientes = "";
        $filterUsuario = "";
    }
    $filtroDeTerminalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($id_usuario, 'DASHBOARD_FILTRO_TERMINAL') ? UsuariosParametro::getValorByUsuarioAndParametro($id_usuario, 'DASHBOARD_FILTRO_TERMINAL') : false);
    $filtroDeIdTerminalParametro = (UsuariosParametro::getValorByUsuarioAndParametro($id_usuario, 'DASHBOARD_FILTRO_ID_TERMINAL') ? UsuariosParametro::getValorByUsuarioAndParametro($id_usuario, 'DASHBOARD_FILTRO_ID_TERMINAL') : false);
    $filtroTipoClienteParametro = (UsuariosParametro::getValorByUsuarioAndParametro($id_usuario, 'DASHBOARD_FILTRO_TIPO_CLIENTE') ? UsuariosParametro::getValorByUsuarioAndParametro($id_usuario, 'DASHBOARD_FILTRO_TIPO_CLIENTE') : false);

    if($filtroDeTerminalParametro){
        $filtroDeTerminal = "AND (ccd.id_campo = 57274 AND ccd.valor in ('$filtroDeTerminalParametro'))";
    }else{
        $filtroDeTerminal = "";
    }

    if($filtroGasParametro){
        $filtroGas = "AND (ccd.id_campo = 57287 AND ccd.valor = '$filtroGasParametro')";
        $id_campo_nomenclatura = mysql_result(mysql_query("SELECT id FROM clientes_campos WHERE id_usuario = 17242 AND nombre = 'NOMENCLATURA_GAS'"), 0, 0);
        $razon_social = " COALESCE(ccd2.valor, c.razon_social) AS razon_social, ";
        $joinNomenclatura = " LEFT JOIN clientes_campos_detalles ccd2 ON c.id = ccd2.id_cliente AND ccd2.id_campo = $id_campo_nomenclatura ";
        $selectProductos = ",mp.descripcion as producto";
        $joinProductos = "LEFT JOIN pedidos p ON p.id_cliente_reparto = cr.id
                          LEFT JOIN pedidos_detalles pd ON pd.id_pedido = p.id 
                          LEFT JOIN maestro_productos mp ON (mp.codigo = pd.codigo_producto AND mp.id_usuario = 3050)";
    }else{
        $filtroGas = "";
        $razon_social = "c.razon_social, ";
        $joinNomenclatura = "";
        $selectProductos = "";
        $joinProductos = "";
    }

    if($filtroTipoClienteParametro){
        $filtroTipoCliente = "AND c.codigo_tipo_cliente in ('$filtroTipoClienteParametro')";
    }else{
        $filtroTipoCliente = "";
    }

    if ($filtroDeIdTerminalParametro) {
        $filtroDeIdTerminal = " AND SUBSTRING(r.inicio_id,2) = '$filtroDeIdTerminalParametro' ";
    }else{
        $filtroDeIdTerminal = "";
    }

    $sql = "SELECT r.id AS id_reparto, r.codigo AS codigo_reparto, r.id_dispositivo, m.nombre,
                $razon_social
                c.codigo,
                c.codigo_tipo_cliente,
                m.transportista,
                r.inicio_id,
                r.fecha_presentacion,
                cr.fecha_visitado,
                cr.fecha_inicio_tarea,
                cr.fecha_fin_tarea,
                cr.fecha_fin_visita,
                cr.descripcion,
                cr.id_cliente,
                cr.id as cliRId,
                cr.marcado_manual,
                cr.respuesta_garmin,
                pp.descripcion AS terminal,
                MAX(CASE WHEN ccd.id_campo = 67 THEN ccd.valor ELSE NULL END) AS provincia,
                MAX(CASE WHEN ccd.id_campo = 69 THEN ccd.valor ELSE NULL END) AS localidad
                $selectProductos
            FROM repartos r
            LEFT JOIN ypf_delivery_status yds ON yds.delivery_id = r.id
            LEFT JOIN clientes_repartos cr ON r.id = cr.id_reparto
            LEFT JOIN dispositivos d ON r.id_dispositivo = d.id
            LEFT JOIN maquinas m ON d.id_maquina = m.id
            LEFT JOIN clientes c ON cr.id_cliente = c.id
            LEFT JOIN clientes_campos_detalles ccd ON ccd.id_cliente = c.id
            LEFT JOIN puntos_partida pp ON pp.id = SUBSTRING(r.inicio_id,2)
            $joinProductos
            $joinNomenclatura
            $filterClientes
            $troncalSql
            WHERE r.id_usuario = '$id_usuario_ypf'
            $filterUsuario
            $filtroDeTerminal $filtroGas
            $filtroTipoCliente
            $aeroSQL
            $filtroDeIdTerminal
            AND ( yds.dropped = 0 and yds.dropped_at is NULL )
            AND r.estado NOT IN ('PRELIMINAR', 'PENDIENTE')
            AND (r.fecha_presentacion BETWEEN '$start 00:00:00' AND '$end 23:59:59')
            AND NOT (r.id_dispositivo = 0 AND r.transportista IS NULL)
            GROUP BY r.codigo, cr.id_cliente
            ORDER BY r.codigo DESC";

    $r_data = mysql_query($sql);

    $orders = [];
    if(mysql_num_rows($r_data)) {
        while ($row = mysql_fetch_assoc($r_data)) {
            $orders[] = $row;
        }
    }
    $data = processDataResource($orders);
    return $data;
}

function getCurrentState($reparto) {
    $state = '';
    if($reparto['respuesta_garmin'] == 4 && $reparto['marcado_manual'] != '1') {
        $state = _("Sin visita");
    } else if (date('H:i:s',strtotime($reparto['fecha_presentacion'])) == '00:00:00') {
        $state = _("Pendiente");
    } else if ($reparto['fecha_fin_visita']) {
        $state = _("Finalizado");
    } else if ($reparto['fecha_inicio_tarea']) {
        $state = _("Descargando");
    } else if ($reparto['fecha_visitado']) {
        $state = _("En cliente");
    } else if ($reparto['fecha_fin_carga']) {
        $state = _("Despachado");
    } else if ($reparto['fecha_presentacion']) {
        $state = _("En playa");
    } else {
        $state = _("No iniciado");
    }

    return $state;
}

function isReadOnlyUser() {
    global $usuario_id;
    $permisos = get_permisos($usuario_id, 'dashboard');
    return in_array('SIN_EDICION', $permisos);
}

function editTrip($usuario_id,$cliente) {
    if($cliente['id']) {
        if(date('Y-m-d H:i:s', strtotime($cliente['fecha_visitado'])) == $cliente['fecha_visitado']) {
            mysql_query("UPDATE clientes_repartos
                SET fecha_visitado = '$cliente[fecha_visitado]',
                    marcado_manual = 1
                WHERE id = '$cliente[id]'");
        }
        if(date('Y-m-d H:i:s', strtotime($cliente['fecha_inicio_tarea'])) == $cliente['fecha_inicio_tarea']) {
            mysql_query("UPDATE clientes_repartos
                SET fecha_inicio_tarea = '$cliente[fecha_inicio_tarea]',
                    marcado_manual = 1
                WHERE id = '$cliente[id]'");
        }
        if(date('Y-m-d H:i:s', strtotime($cliente['fecha_fin_tarea'])) == $cliente['fecha_fin_tarea']) {
            mysql_query("UPDATE clientes_repartos
                SET fecha_fin_tarea = '$cliente[fecha_fin_tarea]',
                    marcado_manual = 1
                WHERE id = '$cliente[id]'");
        }
        if(date('Y-m-d H:i:s', strtotime($cliente['fecha_fin_visita'])) == $cliente['fecha_fin_visita']) {
            mysql_query("UPDATE clientes_repartos
                SET fecha_fin_visita = '$cliente[fecha_fin_visita]',
                    marcado_manual = 1
                WHERE id = '$cliente[id]'");
        }

        return true;
    } else {
        return false;
    }
}

function get_usuario_has_rol($id_usuario, $rol){
    $result = mysql_query("SELECT 1
                           FROM usuarios_rol
                           WHERE usuario_id = '$id_usuario'
                           AND rol_id = '$rol'
                           LIMIT 1");
    return mysql_num_rows($result);
}

function createDateRangeArray($strDateFrom,$strDateTo) {
    $aryRange=array();
    $iDateFrom=mktime(1,0,0,substr($strDateFrom,5,2),substr($strDateFrom,8,2),substr($strDateFrom,0,4));
    $iDateTo=mktime(1,0,0,substr($strDateTo,5,2),substr($strDateTo,8,2),substr($strDateTo,0,4));
    if ($iDateTo>=$iDateFrom) {
        array_push($aryRange,date('Y-m-d',$iDateFrom));
        while ($iDateFrom<$iDateTo) {
            $iDateFrom+=86400; // add 24 hours
            array_push($aryRange,date('Y-m-d',$iDateFrom));
        }
    }
    return $aryRange;
}